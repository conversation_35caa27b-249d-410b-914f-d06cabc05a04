/**
 * Mock Data - البيانات التجريبية
 * يحتوي على بيانات تجريبية لاختبار التطبيق
 */

// بيانات المركبات التجريبية
export const mockVehicles = [
    {
        id: 'VH001',
        'License Plate': 'أ ب ج 123',
        'Vehicle Type': 'سيارة ركوب',
        'Vehicle Status': 'Active',
        'Driver Name': 'أحمد محمد علي',
        'Current Km': 45000,
        'Next Maintenance Km': 50000,
        'Branch': 'الرياض',
        make: 'تويوتا',
        model: 'كامري',
        year: 2022,
        color: 'أبيض',
        fuelType: 'بنزين',
        engineSize: '2.5L',
        registrationDate: '2022-01-15',
        insuranceExpiry: '2024-01-15',
        lastMaintenanceDate: '2023-10-15',
        averageFuelConsumption: 8.5
    },
    {
        id: 'VH002',
        'License Plate': 'د هـ و 456',
        'Vehicle Type': 'شاحنة صغيرة',
        'Vehicle Status': 'Active',
        'Driver Name': 'محمد أحمد سالم',
        'Current Km': 78000,
        'Next Maintenance Km': 80000,
        'Branch': 'جدة',
        make: 'نيسان',
        model: 'نافارا',
        year: 2021,
        color: 'أزرق',
        fuelType: 'ديزل',
        engineSize: '2.3L',
        registrationDate: '2021-03-20',
        insuranceExpiry: '2024-03-20',
        lastMaintenanceDate: '2023-11-01',
        averageFuelConsumption: 12.3
    },
    {
        id: 'VH003',
        'License Plate': 'ز ح ط 789',
        'Vehicle Type': 'حافلة',
        'Vehicle Status': 'Maintenance',
        'Driver Name': 'سالم عبدالله محمد',
        'Current Km': 120000,
        'Next Maintenance Km': 125000,
        'Branch': 'الدمام',
        make: 'مرسيدس',
        model: 'سبرينتر',
        year: 2020,
        color: 'أصفر',
        fuelType: 'ديزل',
        engineSize: '3.0L',
        registrationDate: '2020-05-10',
        insuranceExpiry: '2024-05-10',
        lastMaintenanceDate: '2023-12-01',
        averageFuelConsumption: 15.8
    },
    {
        id: 'VH004',
        'License Plate': 'ي ك ل 321',
        'Vehicle Type': 'سيارة ركوب',
        'Vehicle Status': 'Active',
        'Driver Name': 'عبدالرحمن خالد أحمد',
        'Current Km': 32000,
        'Next Maintenance Km': 35000,
        'Branch': 'الرياض',
        make: 'هيونداي',
        model: 'إلنترا',
        year: 2023,
        color: 'رمادي',
        fuelType: 'بنزين',
        engineSize: '2.0L',
        registrationDate: '2023-02-01',
        insuranceExpiry: '2025-02-01',
        lastMaintenanceDate: '2023-08-15',
        averageFuelConsumption: 7.2
    },
    {
        id: 'VH005',
        'License Plate': 'م ن س 654',
        'Vehicle Type': 'شاحنة كبيرة',
        'Vehicle Status': 'Inactive',
        'Driver Name': '',
        'Current Km': 95000,
        'Next Maintenance Km': 100000,
        'Branch': 'جدة',
        make: 'فولفو',
        model: 'FH16',
        year: 2019,
        color: 'أحمر',
        fuelType: 'ديزل',
        engineSize: '13.0L',
        registrationDate: '2019-08-12',
        insuranceExpiry: '2024-08-12',
        lastMaintenanceDate: '2023-09-20',
        averageFuelConsumption: 25.4
    }
];

// بيانات السائقين التجريبية
export const mockDrivers = [
    {
        id: 'DR001',
        name: 'أحمد محمد علي',
        employeeId: 'EMP001',
        phone: '**********',
        email: '<EMAIL>',
        licenseNumber: 'LIC123456789',
        licenseExpiry: '2025-06-15',
        branch: 'الرياض',
        status: 'active',
        hireDate: '2020-01-15',
        birthDate: '1985-03-20',
        address: 'الرياض، حي النخيل',
        emergencyContact: '**********',
        vehicleAssigned: 'VH001',
        totalKilometers: 125000,
        violations: 2,
        rating: 4.5
    },
    {
        id: 'DR002',
        name: 'محمد أحمد سالم',
        employeeId: 'EMP002',
        phone: '**********',
        email: '<EMAIL>',
        licenseNumber: 'LIC987654321',
        licenseExpiry: '2024-12-20',
        branch: 'جدة',
        status: 'active',
        hireDate: '2019-05-10',
        birthDate: '1982-07-15',
        address: 'جدة، حي الصفا',
        emergencyContact: '**********',
        vehicleAssigned: 'VH002',
        totalKilometers: 180000,
        violations: 1,
        rating: 4.8
    },
    {
        id: 'DR003',
        name: 'سالم عبدالله محمد',
        employeeId: 'EMP003',
        phone: '**********',
        email: '<EMAIL>',
        licenseNumber: 'LIC456789123',
        licenseExpiry: '2025-03-10',
        branch: 'الدمام',
        status: 'active',
        hireDate: '2021-02-01',
        birthDate: '1988-11-05',
        address: 'الدمام، حي الشاطئ',
        emergencyContact: '**********',
        vehicleAssigned: 'VH003',
        totalKilometers: 95000,
        violations: 0,
        rating: 5.0
    },
    {
        id: 'DR004',
        name: 'عبدالرحمن خالد أحمد',
        employeeId: 'EMP004',
        phone: '**********',
        email: '<EMAIL>',
        licenseNumber: 'LIC789123456',
        licenseExpiry: '2026-01-25',
        branch: 'الرياض',
        status: 'active',
        hireDate: '2022-06-15',
        birthDate: '1990-04-12',
        address: 'الرياض، حي العليا',
        emergencyContact: '**********',
        vehicleAssigned: 'VH004',
        totalKilometers: 45000,
        violations: 0,
        rating: 4.7
    },
    {
        id: 'DR005',
        name: 'خالد عبدالعزيز سعد',
        employeeId: 'EMP005',
        phone: '**********',
        email: '<EMAIL>',
        licenseNumber: 'LIC321654987',
        licenseExpiry: '2024-09-30',
        branch: 'جدة',
        status: 'inactive',
        hireDate: '2018-03-20',
        birthDate: '1980-12-08',
        address: 'جدة، حي الروضة',
        emergencyContact: '**********',
        vehicleAssigned: '',
        totalKilometers: 220000,
        violations: 3,
        rating: 4.2
    }
];

// بيانات الصيانة التجريبية
export const mockMaintenance = [
    {
        id: 'MNT001',
        vehicleId: 'VH001',
        vehiclePlate: 'أ ب ج 123',
        serviceType: 'صيانة دورية',
        serviceDate: '2023-10-15',
        nextServiceDate: '2024-01-15',
        currentOdometer: 45000,
        nextServiceOdometer: 50000,
        description: 'تغيير زيت المحرك والفلاتر',
        cost: 350.00,
        serviceProvider: 'مركز الصيانة المتقدم',
        technician: 'أحمد الفني',
        status: 'completed',
        notes: 'تم تغيير جميع الفلاتر وزيت المحرك',
        partsUsed: ['زيت محرك', 'فلتر زيت', 'فلتر هواء'],
        laborHours: 2.5,
        warrantyExpiry: '2024-04-15'
    },
    {
        id: 'MNT002',
        vehicleId: 'VH002',
        vehiclePlate: 'د هـ و 456',
        serviceType: 'إصلاح طارئ',
        serviceDate: '2023-11-01',
        nextServiceDate: '2024-02-01',
        currentOdometer: 78000,
        nextServiceOdometer: 80000,
        description: 'إصلاح نظام الفرامل',
        cost: 850.00,
        serviceProvider: 'ورشة الخليج للسيارات',
        technician: 'محمد الميكانيكي',
        status: 'completed',
        notes: 'تم استبدال أقراص الفرامل الأمامية',
        partsUsed: ['أقراص فرامل أمامية', 'تيل فرامل'],
        laborHours: 4.0,
        warrantyExpiry: '2024-05-01'
    },
    {
        id: 'MNT003',
        vehicleId: 'VH003',
        vehiclePlate: 'ز ح ط 789',
        serviceType: 'صيانة شاملة',
        serviceDate: '2023-12-01',
        nextServiceDate: '2024-03-01',
        currentOdometer: 120000,
        nextServiceOdometer: 125000,
        description: 'صيانة شاملة للمحرك ونظام التبريد',
        cost: 1250.00,
        serviceProvider: 'مركز مرسيدس المعتمد',
        technician: 'سالم الخبير',
        status: 'in_progress',
        notes: 'جاري العمل على إصلاح نظام التبريد',
        partsUsed: ['مضخة مياه', 'ثرموستات', 'خراطيم تبريد'],
        laborHours: 8.0,
        warrantyExpiry: '2024-06-01'
    },
    {
        id: 'MNT004',
        vehicleId: 'VH004',
        vehiclePlate: 'ي ك ل 321',
        serviceType: 'فحص دوري',
        serviceDate: '2023-08-15',
        nextServiceDate: '2023-11-15',
        currentOdometer: 32000,
        nextServiceOdometer: 35000,
        description: 'فحص دوري شامل',
        cost: 150.00,
        serviceProvider: 'مركز الفحص الفني',
        technician: 'عبدالله المفتش',
        status: 'completed',
        notes: 'السيارة في حالة ممتازة',
        partsUsed: [],
        laborHours: 1.0,
        warrantyExpiry: '2023-12-15'
    },
    {
        id: 'MNT005',
        vehicleId: 'VH001',
        vehiclePlate: 'أ ب ج 123',
        serviceType: 'تغيير إطارات',
        serviceDate: '2023-09-20',
        nextServiceDate: '2024-09-20',
        currentOdometer: 43000,
        nextServiceOdometer: 60000,
        description: 'تغيير الإطارات الأربعة',
        cost: 1200.00,
        serviceProvider: 'معرض الإطارات الذهبي',
        technician: 'يوسف الإطارات',
        status: 'completed',
        notes: 'تم تركيب إطارات جديدة عالية الجودة',
        partsUsed: ['إطار أمامي يمين', 'إطار أمامي يسار', 'إطار خلفي يمين', 'إطار خلفي يسار'],
        laborHours: 1.5,
        warrantyExpiry: '2024-09-20'
    }
];

// بيانات الوقود التجريبية
export const mockFuel = [
    {
        id: 'FUEL001',
        vehicleId: 'VH001',
        vehiclePlate: 'أ ب ج 123',
        fuelDate: '2023-12-01',
        liters: 45.5,
        cost: 136.50,
        pricePerLiter: 3.00,
        odometer: 45000,
        fuelType: 'بنزين 95',
        station: 'محطة أرامكو - الرياض',
        driverName: 'أحمد محمد علي',
        receiptNumber: 'RCP001234',
        paymentMethod: 'بطاقة الشركة',
        efficiency: 8.5,
        notes: 'تعبئة كاملة'
    },
    {
        id: 'FUEL002',
        vehicleId: 'VH002',
        vehiclePlate: 'د هـ و 456',
        fuelDate: '2023-12-02',
        liters: 60.0,
        cost: 192.00,
        pricePerLiter: 3.20,
        odometer: 78000,
        fuelType: 'ديزل',
        station: 'محطة البترول - جدة',
        driverName: 'محمد أحمد سالم',
        receiptNumber: 'RCP001235',
        paymentMethod: 'نقداً',
        efficiency: 12.3,
        notes: 'تعبئة جزئية'
    },
    {
        id: 'FUEL003',
        vehicleId: 'VH004',
        vehiclePlate: 'ي ك ل 321',
        fuelDate: '2023-12-03',
        liters: 40.0,
        cost: 120.00,
        pricePerLiter: 3.00,
        odometer: 32000,
        fuelType: 'بنزين 91',
        station: 'محطة الوقود السريع - الرياض',
        driverName: 'عبدالرحمن خالد أحمد',
        receiptNumber: 'RCP001236',
        paymentMethod: 'بطاقة الشركة',
        efficiency: 7.2,
        notes: 'تعبئة عادية'
    }
];

// بيانات المستخدمين التجريبية
export const mockUsers = [
    {
        id: 'USR001',
        name: 'مدير النظام',
        email: '<EMAIL>',
        role: 'Super Admin',
        branch: 'جميع الفروع',
        status: 'active',
        lastLogin: '2023-12-05T10:30:00',
        createdDate: '2023-01-01',
        permissions: ['all']
    },
    {
        id: 'USR002',
        name: 'أحمد المدير',
        email: '<EMAIL>',
        role: 'Manager',
        branch: 'الرياض',
        status: 'active',
        lastLogin: '2023-12-05T09:15:00',
        createdDate: '2023-02-15',
        permissions: ['vehicles.manage', 'drivers.manage', 'maintenance.manage', 'reports.view']
    },
    {
        id: 'USR003',
        name: 'سارة المشرفة',
        email: '<EMAIL>',
        role: 'Supervisor',
        branch: 'جدة',
        status: 'active',
        lastLogin: '2023-12-04T16:45:00',
        createdDate: '2023-03-10',
        permissions: ['vehicles.view', 'drivers.view', 'maintenance.create', 'fuel.manage']
    },
    {
        id: 'USR004',
        name: 'محمد المستخدم',
        email: '<EMAIL>',
        role: 'User',
        branch: 'الدمام',
        status: 'active',
        lastLogin: '2023-12-05T08:20:00',
        createdDate: '2023-04-20',
        permissions: ['vehicles.view', 'maintenance.view', 'fuel.create']
    }
];

// دالة لتوليد بيانات إحصائية
export function generateMockStats() {
    return {
        vehicles: {
            total: mockVehicles.length,
            active: mockVehicles.filter(v => v['Vehicle Status'] === 'Active').length,
            maintenance: mockVehicles.filter(v => v['Vehicle Status'] === 'Maintenance').length,
            inactive: mockVehicles.filter(v => v['Vehicle Status'] === 'Inactive').length
        },
        drivers: {
            total: mockDrivers.length,
            active: mockDrivers.filter(d => d.status === 'active').length,
            inactive: mockDrivers.filter(d => d.status === 'inactive').length
        },
        maintenance: {
            total: mockMaintenance.length,
            completed: mockMaintenance.filter(m => m.status === 'completed').length,
            inProgress: mockMaintenance.filter(m => m.status === 'in_progress').length,
            pending: mockMaintenance.filter(m => m.status === 'pending').length
        },
        fuel: {
            totalRecords: mockFuel.length,
            totalLiters: mockFuel.reduce((sum, f) => sum + f.liters, 0),
            totalCost: mockFuel.reduce((sum, f) => sum + f.cost, 0),
            averageEfficiency: mockFuel.reduce((sum, f) => sum + f.efficiency, 0) / mockFuel.length
        }
    };
}

// دالة لتوليد بيانات الرسوم البيانية
export function generateChartData() {
    return {
        fuelConsumption: {
            labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
            datasets: [{
                label: 'استهلاك الوقود (لتر)',
                data: [1200, 1350, 1100, 1400, 1250, 1300],
                borderColor: '#3b82f6',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                tension: 0.4
            }]
        },
        maintenanceCosts: {
            labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
            datasets: [{
                label: 'تكاليف الصيانة (ريال)',
                data: [5000, 7500, 4200, 8900, 6300, 5800],
                backgroundColor: [
                    '#ef4444',
                    '#f59e0b',
                    '#10b981',
                    '#3b82f6',
                    '#8b5cf6',
                    '#f97316'
                ]
            }]
        },
        vehicleStatus: {
            labels: ['نشط', 'في الصيانة', 'غير نشط'],
            datasets: [{
                data: [
                    mockVehicles.filter(v => v['Vehicle Status'] === 'Active').length,
                    mockVehicles.filter(v => v['Vehicle Status'] === 'Maintenance').length,
                    mockVehicles.filter(v => v['Vehicle Status'] === 'Inactive').length
                ],
                backgroundColor: ['#10b981', '#f59e0b', '#ef4444']
            }]
        }
    };
}

// تصدير جميع البيانات
export default {
    vehicles: mockVehicles,
    drivers: mockDrivers,
    maintenance: mockMaintenance,
    fuel: mockFuel,
    users: mockUsers,
    stats: generateMockStats(),
    charts: generateChartData()
};

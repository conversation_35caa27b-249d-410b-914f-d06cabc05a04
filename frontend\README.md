# نظام إدارة الأسطول - Fleet Management System

نظام شامل لإدارة الأسطول وتتبع المركبات والصيانة والعمليات باللغة العربية.

## 📋 المحتويات

- [نظرة عامة](#نظرة-عامة)
- [المميزات](#المميزات)
- [التقنيات المستخدمة](#التقنيات-المستخدمة)
- [هيكل المشروع](#هيكل-المشروع)
- [التثبيت والتشغيل](#التثبيت-والتشغيل)
- [الاستخدام](#الاستخدام)
- [المساهمة](#المساهمة)
- [الترخيص](#الترخيص)

## 🎯 نظرة عامة

نظام إدارة الأسطول هو تطبيق ويب شامل مصمم لإدارة أساطيل المركبات بكفاءة عالية. يوفر النظام واجهة سهلة الاستخدام باللغة العربية مع دعم كامل لاتجاه RTL.

### الهدف من النظام
- تبسيط إدارة المركبات والسائقين
- تتبع الصيانة والوقود بدقة
- توفير تقارير شاملة وإحصائيات مفيدة
- تحسين كفاءة العمليات وخفض التكاليف

## ✨ المميزات

### 🚗 إدارة المركبات
- **تسجيل شامل**: معلومات كاملة عن كل مركبة
- **تتبع الحالة**: نشط، في الصيانة، غير نشط
- **إدارة الوثائق**: تواريخ انتهاء التأمين والرخص
- **تتبع الكيلومترات**: مراقبة المسافات المقطوعة

### 👥 إدارة السائقين
- **ملفات شخصية**: معلومات كاملة عن السائقين
- **إدارة الرخص**: تتبع تواريخ انتهاء الرخص
- **تقييم الأداء**: نظام تقييم السائقين
- **ربط المركبات**: تخصيص المركبات للسائقين

### 🔧 إدارة الصيانة
- **جدولة الصيانة**: تخطيط الصيانة الدورية
- **تتبع التكاليف**: مراقبة تكاليف الصيانة
- **سجل الخدمات**: تاريخ كامل لجميع أعمال الصيانة
- **تنبيهات**: إشعارات للصيانة المستحقة

### ⛽ إدارة الوقود
- **تسجيل التعبئة**: تتبع جميع عمليات التعبئة
- **حساب الكفاءة**: مراقبة استهلاك الوقود
- **تحليل التكاليف**: تقارير تكاليف الوقود
- **مقارنة الأداء**: مقارنة كفاءة المركبات

### 📊 التقارير والإحصائيات
- **لوحة تحكم تفاعلية**: نظرة عامة على جميع العمليات
- **رسوم بيانية**: تمثيل بصري للبيانات
- **تقارير مفصلة**: تقارير قابلة للتخصيص والتصدير
- **إحصائيات متقدمة**: تحليلات عميقة للأداء

### 👤 إدارة المستخدمين
- **نظام صلاحيات**: تحكم دقيق في الوصول
- **أدوار متعددة**: مدير، مشرف، مستخدم
- **أمان عالي**: حماية البيانات والخصوصية
- **سجل النشاطات**: تتبع أنشطة المستخدمين

## 🛠️ التقنيات المستخدمة

### Frontend
- **HTML5**: هيكل الصفحات
- **CSS3**: تنسيق متجاوب ومتقدم
- **JavaScript ES6+**: منطق التطبيق
- **Chart.js**: الرسوم البيانية التفاعلية
- **Font Awesome**: الأيقونات

### المكتبات والأدوات
- **Chart.js**: رسوم بيانية تفاعلية
- **html2pdf**: تصدير PDF
- **XLSX**: تصدير Excel
- **Service Worker**: دعم PWA

### التصميم والواجهة
- **تصميم متجاوب**: يعمل على جميع الأجهزة
- **دعم RTL**: اتجاه من اليمين لليسار
- **خطوط عربية**: Noto Sans Arabic
- **نظام ألوان متسق**: تصميم احترافي

## 📁 هيكل المشروع

```
frontend/
├── index.html              # الصفحة الرئيسية
├── main.js                 # نقطة الدخول الرئيسية
├── router.js               # نظام التوجيه
├── components/             # المكونات القابلة لإعادة الاستخدام
│   ├── Modal.js           # النوافذ المنبثقة
│   ├── Spinner.js         # مؤشرات التحميل
│   ├── StatCard.js        # بطاقات الإحصائيات
│   ├── ChartWrapper.js    # مجمع الرسوم البيانية
│   ├── Table.js           # الجداول التفاعلية
│   └── index.js           # فهرس المكونات
├── pages/                  # صفحات التطبيق
│   ├── dashboard/         # لوحة التحكم
│   ├── vehicles/          # إدارة المركبات
│   ├── drivers/           # إدارة السائقين
│   ├── maintenance/       # إدارة الصيانة
│   ├── fuel/              # إدارة الوقود
│   ├── users/             # إدارة المستخدمين
│   ├── reports/           # التقارير
│   └── index.js           # فهرس الصفحات
├── services/               # خدمات البيانات
│   ├── api.js             # واجهة برمجة التطبيقات
│   ├── authService.js     # خدمة المصادقة
│   ├── dataService.js     # خدمة البيانات
│   └── index.js           # فهرس الخدمات
├── store/                  # إدارة الحالة
│   ├── store.js           # المخزن الرئيسي
│   ├── actions.js         # الإجراءات
│   ├── selectors.js       # المحددات
│   ├── hooks.js           # الخطافات المخصصة
│   └── index.js           # فهرس المخزن
├── utils/                  # الأدوات المساعدة
│   ├── constants.js       # الثوابت
│   ├── utility.js         # دوال مساعدة
│   ├── permissions.js     # إدارة الصلاحيات
│   ├── validation.js      # التحقق من البيانات
│   ├── storage.js         # إدارة التخزين
│   └── index.js           # فهرس الأدوات
├── css/                    # ملفات التنسيق
│   ├── index.css          # الفهرس الرئيسي
│   ├── main.css           # الأنماط الأساسية
│   ├── layout.css         # تخطيط التطبيق
│   ├── components.css     # أنماط المكونات
│   ├── pages.css          # أنماط الصفحات
│   └── utilities.css      # أدوات CSS المساعدة
├── data/                   # البيانات التجريبية
│   └── mockData.js        # بيانات تجريبية
├── assets/                 # الأصول الثابتة
│   ├── images/            # الصور
│   ├── icons/             # الأيقونات
│   └── css/               # ملفات CSS إضافية
└── README.md              # هذا الملف
```

## 🚀 التثبيت والتشغيل

### المتطلبات
- متصفح ويب حديث (Chrome, Firefox, Safari, Edge)
- خادم ويب محلي (اختياري للتطوير)

### خطوات التثبيت

1. **تحميل المشروع**
   ```bash
   git clone [repository-url]
   cd fleet-management-system
   ```

2. **تشغيل الخادم المحلي** (اختياري)
   ```bash
   # باستخدام Python
   python -m http.server 8000
   
   # أو باستخدام Node.js
   npx serve .
   
   # أو باستخدام PHP
   php -S localhost:8000
   ```

3. **فتح التطبيق**
   - افتح `index.html` في المتصفح مباشرة
   - أو اذهب إلى `http://localhost:8000`

### بيانات تسجيل الدخول التجريبية
```
اسم المستخدم: admin
كلمة المرور: admin123
```

## 📖 الاستخدام

### تسجيل الدخول
1. افتح التطبيق في المتصفح
2. أدخل بيانات تسجيل الدخول
3. انقر على زر "دخول"

### التنقل في التطبيق
- **لوحة التحكم**: نظرة عامة على النظام
- **المركبات**: إدارة أسطول المركبات
- **السائقين**: إدارة بيانات السائقين
- **الصيانة**: تتبع أعمال الصيانة
- **الوقود**: مراقبة استهلاك الوقود
- **التقارير**: تقارير وإحصائيات مفصلة
- **المستخدمين**: إدارة مستخدمي النظام

### إضافة مركبة جديدة
1. اذهب إلى صفحة "المركبات"
2. انقر على "إضافة مركبة"
3. املأ البيانات المطلوبة
4. احفظ المعلومات

### تسجيل صيانة
1. اذهب إلى صفحة "الصيانة"
2. انقر على "إضافة سجل صيانة"
3. اختر المركبة ونوع الصيانة
4. أدخل التفاصيل والتكلفة
5. احفظ السجل

## 🎨 التخصيص

### تغيير الألوان
```css
:root {
    --primary-color: #your-color;
    --secondary-color: #your-color;
}
```

### إضافة صفحة جديدة
1. أنشئ ملف في مجلد `pages/`
2. أضف الصفحة إلى `pages/index.js`
3. أضف رابط في التنقل

### إضافة مكون جديد
1. أنشئ ملف في مجلد `components/`
2. صدر المكون من `components/index.js`
3. استخدم المكون في الصفحات

## 🔧 التطوير

### إضافة ميزة جديدة
1. خطط للميزة وحدد المتطلبات
2. أنشئ المكونات اللازمة
3. أضف الخدمات المطلوبة
4. اختبر الميزة بدقة
5. وثق التغييرات

### أفضل الممارسات
- استخدم أسماء واضحة ومعبرة
- اكتب تعليقات باللغة العربية
- اتبع نمط الكود الموجود
- اختبر على متصفحات مختلفة
- تأكد من الاستجابة على الأجهزة المحمولة

## 🐛 الأخطاء الشائعة وحلولها

### المشكلة: لا تظهر الرسوم البيانية
**الحل**: تأكد من تحميل مكتبة Chart.js
```html
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
```

### المشكلة: لا تعمل الأيقونات
**الحل**: تأكد من تحميل Font Awesome
```html
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
```

### المشكلة: مشاكل في التنسيق
**الحل**: تأكد من تحميل ملفات CSS بالترتيب الصحيح

## 📱 دعم الأجهزة المحمولة

النظام مصمم ليكون متجاوباً بالكامل ويعمل بسلاسة على:
- الهواتف الذكية (320px+)
- الأجهزة اللوحية (768px+)
- أجهزة الكمبيوتر المحمولة (1024px+)
- شاشات سطح المكتب (1200px+)

## 🔒 الأمان

- تشفير كلمات المرور
- جلسات آمنة
- حماية من CSRF
- تحقق من الصلاحيات
- تسجيل العمليات

## 📈 الأداء

- تحميل تدريجي للبيانات
- ضغط الملفات
- تخزين مؤقت ذكي
- تحسين الصور
- كود محسن

## 🌐 المتصفحات المدعومة

- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## 📞 الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- افتح issue في GitHub
- راسلنا على البريد الإلكتروني
- راجع الوثائق المفصلة

## 🤝 المساهمة

نرحب بمساهماتكم! يرجى:
1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. إجراء التغييرات المطلوبة
4. اختبار التغييرات
5. إرسال Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 🙏 شكر وتقدير

شكر خاص لجميع المساهمين والمطورين الذين ساعدوا في تطوير هذا النظام.

---

**تم تطوير هذا النظام بعناية فائقة لخدمة المؤسسات العربية وتسهيل إدارة أساطيلها بكفاءة عالية.**

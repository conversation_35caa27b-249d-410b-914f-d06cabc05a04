# Store Directory - مجلد إدارة الحالة

هذا المجلد يحتوي على نظام إدارة حالة شامل للتطبيق باستخدام نمط مشابه لـ Redux ولكن مبسط.

## هيكل المجلد

```
store/
├── index.js                 # فهرس المخزن الرئيسي
├── store.js                 # فئة المخزن الأساسية
├── actions.js               # إجراءات تحديث الحالة
├── selectors.js             # محددات استخراج البيانات
├── hooks.js                 # خطافات مخصصة للتفاعل
└── README.md               # هذا الملف
```

## المكونات الأساسية

### 1. Store (المخزن الأساسي)
**الملف**: `store.js`

فئة المخزن الرئيسية التي تدير حالة التطبيق.

#### المميزات:
- إدارة حالة مركزية
- نظام اشتراك للتغييرات
- دعم الـ middleware
- تاريخ التغييرات (undo/redo)
- حفظ تلقائي في localStorage

#### مثال الاستخدام:
```javascript
import { appStore } from './store/store.js';

// الحصول على الحالة
const state = appStore.getState();

// تحديث الحالة
appStore.setState({
    app: { isLoading: true }
}, 'SET_LOADING');

// الاشتراك في التغييرات
const unsubscribe = appStore.subscribe(
    state => state.app.isLoading,
    (isLoading) => console.log('Loading:', isLoading)
);
```

### 2. Actions (الإجراءات)
**الملف**: `actions.js`

مجموعة من الإجراءات المنظمة لتحديث حالة التطبيق.

#### الأقسام:
- **userActions**: إجراءات المستخدم والمصادقة
- **appActions**: إجراءات التطبيق العامة
- **dataActions**: إجراءات البيانات الرئيسية
- **uiActions**: إجراءات واجهة المستخدم

#### مثال الاستخدام:
```javascript
import { actions } from './store/actions.js';

// تسجيل دخول المستخدم
actions.user.login(userData);

// إضافة إشعار
actions.app.addNotification({
    message: 'تم الحفظ بنجاح',
    type: 'success'
});

// تحديث المركبات
actions.data.setVehicles(vehiclesData);

// فتح نافذة منبثقة
actions.ui.setModal('vehicleForm', true, vehicleData);
```

### 3. Selectors (المحددات)
**الملف**: `selectors.js`

دوال لاستخراج أجزاء محددة من الحالة مع إمكانية الحوسبة.

#### الأقسام:
- **userSelectors**: محددات المستخدم
- **appSelectors**: محددات التطبيق
- **dataSelectors**: محددات البيانات
- **uiSelectors**: محددات واجهة المستخدم
- **statsSelectors**: محددات الإحصائيات
- **searchSelectors**: محددات البحث والفلترة

#### مثال الاستخدام:
```javascript
import { selectors } from './store/selectors.js';
import { appStore } from './store/store.js';

const state = appStore.getState();

// الحصول على المستخدم الحالي
const currentUser = selectors.user.getCurrentUser(state);

// الحصول على المركبات النشطة
const activeVehicles = selectors.data.getActiveVehicles(state);

// الحصول على إحصائيات المركبات
const vehicleStats = selectors.stats.getVehicleStats(state);

// البحث في المركبات
const searchResults = selectors.search.searchVehicles('تويوتا')(state);
```

### 4. Hooks (الخطافات)
**الملف**: `hooks.js`

خطافات مخصصة تسهل التفاعل مع المخزن.

#### الخطافات المتاحة:
- **useSelector**: للاشتراك في جزء من الحالة
- **useCurrentUser**: لإدارة المستخدم الحالي
- **useAppState**: لحالة التطبيق العامة
- **useVehicles**: لإدارة المركبات
- **useDrivers**: لإدارة السائقين
- **useModal**: لإدارة النوافذ المنبثقة
- **useForm**: لإدارة النماذج
- **useTable**: لإدارة الجداول

#### مثال الاستخدام:
```javascript
import { useVehicles, useCurrentUser } from './store/hooks.js';

// استخدام خطاف المركبات
const vehiclesHook = useVehicles();
console.log('Vehicles:', vehiclesHook.vehicles);
console.log('Stats:', vehiclesHook.stats);

// إضافة مركبة جديدة
vehiclesHook.addVehicle(newVehicleData);

// البحث في المركبات
const searchResults = vehiclesHook.search('تويوتا');

// الاشتراك في التغييرات
const unsubscribe = vehiclesHook.subscribe((vehicles) => {
    console.log('Vehicles updated:', vehicles);
});

// استخدام خطاف المستخدم
const userHook = useCurrentUser();
if (userHook.isAuthenticated) {
    console.log('Current user:', userHook.user);
}
```

## هيكل الحالة

```javascript
{
    // بيانات المستخدم
    user: {
        isAuthenticated: false,
        currentUser: null,
        preferences: {
            theme: 'light',
            language: 'ar',
            notifications: true
        }
    },

    // بيانات التطبيق
    app: {
        isLoading: false,
        currentPage: 'dashboard',
        sidebarOpen: true,
        notifications: [],
        errors: []
    },

    // البيانات الرئيسية
    data: {
        vehicles: [],
        drivers: [],
        maintenance: [],
        fuel: [],
        users: [],
        lastSync: null,
        isOnline: true
    },

    // واجهة المستخدم
    ui: {
        modals: {},
        forms: {},
        tables: {},
        charts: {}
    },

    // الفلاتر والبحث
    filters: {
        vehicles: {},
        drivers: {},
        maintenance: {},
        fuel: {}
    }
}
```

## الاستخدام العام

### تهيئة المخزن
```javascript
import { initializeStore } from './store/index.js';

// تهيئة نظام إدارة الحالة
const initialized = initializeStore();
if (initialized) {
    console.log('Store initialized successfully');
}
```

### الوصول للمخزن
```javascript
import { store } from './store/index.js';

// الحصول على الحالة
const state = store.getState();

// تحديث الحالة
store.setState({ app: { isLoading: true } });

// الحصول على البيانات
const vehicles = store.getData('vehicles');

// الحصول على الإحصائيات
const stats = store.getStats();
```

### استخدام الإجراءات
```javascript
import { actions } from './store/index.js';

// إجراءات المستخدم
actions.user.login(userData);
actions.user.logout();
actions.user.updatePreferences({ theme: 'dark' });

// إجراءات التطبيق
actions.app.setLoading(true, 'جاري التحميل...');
actions.app.addNotification({
    message: 'تم الحفظ بنجاح',
    type: 'success'
});

// إجراءات البيانات
actions.data.setVehicles(vehiclesData);
actions.data.addVehicle(newVehicle);
actions.data.updateVehicle(vehicleId, updates);
```

### استخدام المحددات
```javascript
import { selectors } from './store/index.js';

const state = store.getState();

// محددات البيانات
const allVehicles = selectors.data.getAllVehicles(state);
const activeVehicles = selectors.data.getActiveVehicles(state);
const vehicleById = selectors.data.getVehicleById('123')(state);

// محددات الإحصائيات
const vehicleStats = selectors.stats.getVehicleStats(state);
const driverStats = selectors.stats.getDriverStats(state);

// محددات البحث
const searchResults = selectors.search.searchVehicles('تويوتا')(state);
```

### استخدام الخطافات
```javascript
import { hooks } from './store/index.js';

// خطاف المركبات
const vehiclesHook = hooks.useVehicles();
vehiclesHook.addVehicle(newVehicle);
vehiclesHook.subscribe(vehicles => console.log(vehicles));

// خطاف النوافذ المنبثقة
const modalHook = hooks.useModal('vehicleForm');
modalHook.open(vehicleData);
modalHook.close();

// خطاف النماذج
const formHook = hooks.useForm('vehicleForm');
formHook.updateField('licensePlate', 'أ ب ج 123');
formHook.reset();
```

## أمثلة متقدمة

### إنشاء مكون يستخدم المخزن
```javascript
import { hooks } from './store/index.js';

class VehicleList {
    constructor(container) {
        this.container = container;
        this.vehiclesHook = hooks.useVehicles();
        this.filtersHook = hooks.useFilters('vehicles');
        
        this.init();
    }

    init() {
        // الاشتراك في تغييرات المركبات
        this.vehiclesHook.subscribe((vehicles) => {
            this.render(vehicles);
        });

        // الاشتراك في تغييرات الفلاتر
        this.filtersHook.subscribe((filters) => {
            this.applyFilters(filters);
        });

        // العرض الأولي
        this.render(this.vehiclesHook.vehicles);
    }

    render(vehicles) {
        // عرض قائمة المركبات
        this.container.innerHTML = vehicles.map(vehicle => `
            <div class="vehicle-card">
                <h3>${vehicle['License Plate']}</h3>
                <p>${vehicle['Vehicle Type']}</p>
                <span class="status ${vehicle['Vehicle Status']}">${vehicle['Vehicle Status']}</span>
            </div>
        `).join('');
    }

    applyFilters(filters) {
        const filteredVehicles = this.vehiclesHook.filter(filters);
        this.render(filteredVehicles);
    }

    destroy() {
        this.vehiclesHook.unsubscribe();
        this.filtersHook.unsubscribe();
    }
}
```

### إدارة النماذج مع المخزن
```javascript
import { hooks, actions } from './store/index.js';

class VehicleForm {
    constructor(formElement) {
        this.form = formElement;
        this.formHook = hooks.useForm('vehicleForm');
        this.modalHook = hooks.useModal('vehicleModal');
        
        this.init();
    }

    init() {
        // الاشتراك في تغييرات النموذج
        this.formHook.subscribe((formState) => {
            this.updateFormFields(formState);
        });

        // الاشتراك في تغييرات النافذة المنبثقة
        this.modalHook.subscribe((isOpen, data) => {
            if (isOpen && data) {
                this.loadVehicleData(data);
            }
        });

        // إعداد أحداث النموذج
        this.setupFormEvents();
    }

    setupFormEvents() {
        this.form.addEventListener('input', (e) => {
            this.formHook.updateField(e.target.name, e.target.value);
        });

        this.form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveVehicle();
        });
    }

    loadVehicleData(vehicleData) {
        this.formHook.setState(vehicleData);
    }

    async saveVehicle() {
        const formData = this.formHook.state;
        
        try {
            actions.app.setLoading(true, 'جاري الحفظ...');
            
            if (formData.id) {
                await actions.data.updateVehicle(formData.id, formData);
            } else {
                await actions.data.addVehicle(formData);
            }
            
            actions.app.addNotification({
                message: 'تم حفظ المركبة بنجاح',
                type: 'success'
            });
            
            this.modalHook.close();
            this.formHook.reset();
            
        } catch (error) {
            actions.app.addError(error);
        } finally {
            actions.app.setLoading(false);
        }
    }
}
```

## أفضل الممارسات

### 1. تنظيم الإجراءات
```javascript
// ✅ جيد - إجراءات منظمة
actions.data.setVehicles(vehicles);
actions.ui.setModal('vehicleForm', true);

// ❌ سيء - تحديث مباشر
appStore.setState({ data: { vehicles } });
```

### 2. استخدام المحددات
```javascript
// ✅ جيد - استخدام محددات
const activeVehicles = selectors.data.getActiveVehicles(state);

// ❌ سيء - وصول مباشر
const activeVehicles = state.data.vehicles.filter(v => v.status === 'active');
```

### 3. إدارة الاشتراكات
```javascript
// ✅ جيد - إلغاء الاشتراك
const unsubscribe = vehiclesHook.subscribe(callback);
// ... في وقت لاحق
unsubscribe();

// ❌ سيء - عدم إلغاء الاشتراك (تسريب ذاكرة)
vehiclesHook.subscribe(callback);
```

## الأداء

### نصائح لتحسين الأداء
1. استخدم المحددات للحوسبة المعقدة
2. تجنب الاشتراكات غير الضرورية
3. استخدم الخطافات المناسبة لكل حالة
4. نظف الاشتراكات عند عدم الحاجة
5. استخدم الفلاتر بدلاً من إعادة تحميل البيانات

## التطوير والاختبار

### اختبار المخزن
```javascript
// اختبار الإجراءات
actions.data.setVehicles([{ id: 1, name: 'Test Vehicle' }]);
const vehicles = selectors.data.getAllVehicles(appStore.getState());
console.assert(vehicles.length === 1, 'Vehicle should be added');

// اختبار المحددات
const stats = selectors.stats.getVehicleStats(appStore.getState());
console.assert(stats.total === 1, 'Stats should be calculated correctly');
```

## الملاحظات

- نظام إدارة حالة مبسط وفعال
- دعم كامل للغة العربية
- حفظ تلقائي في localStorage
- معالجة شاملة للأخطاء
- تحسين الأداء والذاكرة
- سهولة الاستخدام والصيانة

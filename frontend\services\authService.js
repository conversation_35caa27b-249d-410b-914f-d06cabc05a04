/**
 * Authentication Service - خدمة المصادقة
 * يوفر وظائف تسجيل الدخول والخروج وإدارة جلسة المستخدم
 */

import { fleetAPI } from './api.js';

export class AuthService {
    constructor() {
        this.currentUser = null;
        this.isAuthenticated = false;
        this.sessionKey = 'fleet_user_session';
        this.tokenKey = 'fleet_auth_token';
        
        // استرجاع الجلسة المحفوظة
        this.restoreSession();
    }

    /**
     * تسجيل الدخول
     */
    async login(credentials) {
        try {
            const response = await fleetAPI.login(credentials);
            
            if (response.success && response.user) {
                this.currentUser = response.user;
                this.isAuthenticated = true;
                
                // حفظ الجلسة
                this.saveSession(response.user, response.token);
                
                // إطلاق حدث تسجيل الدخول
                this.dispatchAuthEvent('login', this.currentUser);
                
                return {
                    success: true,
                    user: this.currentUser
                };
            } else {
                throw new Error(response.message || 'فشل في تسجيل الدخول');
            }
        } catch (error) {
            console.error('Login error:', error);
            throw error;
        }
    }

    /**
     * تسجيل الخروج
     */
    async logout() {
        try {
            // محاولة إشعار الخادم بتسجيل الخروج
            try {
                await fleetAPI.logout();
            } catch (error) {
                console.warn('Server logout failed:', error);
            }
            
            // مسح الجلسة المحلية
            this.clearSession();
            
            // إطلاق حدث تسجيل الخروج
            this.dispatchAuthEvent('logout');
            
            return { success: true };
        } catch (error) {
            console.error('Logout error:', error);
            // حتى لو فشل تسجيل الخروج من الخادم، نمسح الجلسة المحلية
            this.clearSession();
            throw error;
        }
    }

    /**
     * التحقق من صحة الجلسة
     */
    async validateSession() {
        if (!this.isAuthenticated || !this.currentUser) {
            return false;
        }

        try {
            // يمكن إضافة تحقق من الخادم هنا
            return true;
        } catch (error) {
            console.error('Session validation failed:', error);
            this.clearSession();
            return false;
        }
    }

    /**
     * حفظ الجلسة في localStorage
     */
    saveSession(user, token = null) {
        try {
            localStorage.setItem(this.sessionKey, JSON.stringify(user));
            if (token) {
                localStorage.setItem(this.tokenKey, token);
            }
        } catch (error) {
            console.error('Failed to save session:', error);
        }
    }

    /**
     * استرجاع الجلسة من localStorage
     */
    restoreSession() {
        try {
            const savedUser = localStorage.getItem(this.sessionKey);
            const savedToken = localStorage.getItem(this.tokenKey);
            
            if (savedUser) {
                this.currentUser = JSON.parse(savedUser);
                this.isAuthenticated = true;
                
                // يمكن إضافة تحقق من صحة التوكن هنا
                return true;
            }
        } catch (error) {
            console.error('Failed to restore session:', error);
            this.clearSession();
        }
        
        return false;
    }

    /**
     * مسح الجلسة
     */
    clearSession() {
        this.currentUser = null;
        this.isAuthenticated = false;
        
        try {
            localStorage.removeItem(this.sessionKey);
            localStorage.removeItem(this.tokenKey);
        } catch (error) {
            console.error('Failed to clear session:', error);
        }
    }

    /**
     * الحصول على المستخدم الحالي
     */
    getCurrentUser() {
        return this.currentUser;
    }

    /**
     * التحقق من حالة المصادقة
     */
    isUserAuthenticated() {
        return this.isAuthenticated && this.currentUser !== null;
    }

    /**
     * الحصول على دور المستخدم
     */
    getUserRole() {
        return this.currentUser?.role || null;
    }

    /**
     * التحقق من صلاحية المستخدم
     */
    hasPermission(permission) {
        if (!this.currentUser) return false;
        
        // يمكن تطوير منطق الصلاحيات هنا
        return this.currentUser.permissions?.includes(permission) || false;
    }

    /**
     * إطلاق أحداث المصادقة
     */
    dispatchAuthEvent(type, data = null) {
        const event = new CustomEvent(`auth:${type}`, {
            detail: data
        });
        document.dispatchEvent(event);
    }

    /**
     * الاستماع لأحداث المصادقة
     */
    onAuthChange(callback) {
        const handleLogin = (event) => callback('login', event.detail);
        const handleLogout = (event) => callback('logout', null);
        
        document.addEventListener('auth:login', handleLogin);
        document.addEventListener('auth:logout', handleLogout);
        
        // إرجاع دالة لإلغاء الاستماع
        return () => {
            document.removeEventListener('auth:login', handleLogin);
            document.removeEventListener('auth:logout', handleLogout);
        };
    }

    /**
     * تحديث بيانات المستخدم
     */
    updateUser(userData) {
        if (this.currentUser) {
            this.currentUser = { ...this.currentUser, ...userData };
            this.saveSession(this.currentUser);
            this.dispatchAuthEvent('userUpdate', this.currentUser);
        }
    }

    /**
     * تغيير كلمة المرور
     */
    async changePassword(oldPassword, newPassword) {
        try {
            const response = await fleetAPI.post('changePassword', {
                oldPassword,
                newPassword
            });
            
            if (response.success) {
                return { success: true };
            } else {
                throw new Error(response.message || 'فشل في تغيير كلمة المرور');
            }
        } catch (error) {
            console.error('Change password error:', error);
            throw error;
        }
    }
}

// إنشاء instance مشترك
const authService = new AuthService();

// دوال مساعدة للاستخدام السريع
export async function login(email, password) {
    return await authService.login({ email, password });
}

export async function logout() {
    return await authService.logout();
}

export function isAuthenticated() {
    return authService.isUserAuthenticated();
}

export function getCurrentUser() {
    return authService.getCurrentUser();
}

export function getUserRole() {
    return authService.getUserRole();
}

export function hasPermission(permission) {
    return authService.hasPermission(permission);
}

export function onAuthChange(callback) {
    return authService.onAuthChange(callback);
}

// تصدير الخدمة الرئيسية
export default authService;

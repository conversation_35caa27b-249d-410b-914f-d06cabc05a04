<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار بسيط - Fleet Management System</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f0f2f5;
            direction: rtl;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid;
        }
        .success {
            background: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border-color: #17a2b8;
            color: #0c5460;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 16px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn-success {
            background: #28a745;
        }
        .btn-success:hover {
            background: #1e7e34;
        }
        .code {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            direction: ltr;
            text-align: left;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 اختبار بسيط لنظام إدارة الأسطول</h1>
        
        <div class="status info">
            <strong>ℹ️ معلومات:</strong> هذا اختبار بسيط للتأكد من عمل النظام الأساسي
        </div>
        
        <div id="test-status">
            <div class="status info">⏳ جاري التحقق من النظام...</div>
        </div>
        
        <div style="text-align: center; margin: 20px 0;">
            <button class="btn" onclick="testBasicFunctionality()">🔄 اختبار الوظائف الأساسية</button>
            <button class="btn" onclick="testModuleLoading()">📦 اختبار تحميل الوحدات</button>
            <button class="btn btn-success" onclick="window.location.href='index.html'">🏠 فتح النظام الرئيسي</button>
        </div>
        
        <div id="detailed-results" style="display: none;">
            <h3>📋 تفاصيل النتائج:</h3>
            <div id="results-content"></div>
        </div>
        
        <div class="status info">
            <strong>💡 نصائح لحل المشاكل:</strong>
            <ul>
                <li>تأكد من تشغيل الملفات من خادم ويب (وليس file://)</li>
                <li>امسح cache المتصفح (Ctrl+Shift+R)</li>
                <li>تأكد من وجود جميع الملفات في المجلدات الصحيحة</li>
                <li>افتح Developer Tools للاطلاع على الأخطاء</li>
            </ul>
        </div>
        
        <div class="code">
            <strong>🌐 طرق تشغيل خادم محلي:</strong><br>
            # Python<br>
            python -m http.server 8000<br><br>
            # Node.js<br>
            npx serve .<br><br>
            # PHP<br>
            php -S localhost:8000
        </div>
    </div>

    <script>
        let testResults = [];

        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('test-status');
            const icon = type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️';
            statusDiv.innerHTML = `<div class="status ${type}"><strong>${icon}</strong> ${message}</div>`;
        }

        function addResult(test, status, details) {
            testResults.push({ test, status, details });
            updateDetailedResults();
        }

        function updateDetailedResults() {
            const container = document.getElementById('detailed-results');
            const content = document.getElementById('results-content');
            
            if (testResults.length > 0) {
                container.style.display = 'block';
                content.innerHTML = testResults.map(result => {
                    const statusClass = result.status ? 'success' : 'error';
                    const icon = result.status ? '✅' : '❌';
                    return `<div class="status ${statusClass}">
                        ${icon} <strong>${result.test}:</strong> ${result.details}
                    </div>`;
                }).join('');
            }
        }

        async function testBasicFunctionality() {
            testResults = [];
            updateStatus('جاري اختبار الوظائف الأساسية...', 'info');
            
            // اختبار 1: JavaScript الأساسي
            try {
                const testArray = [1, 2, 3];
                const result = testArray.map(x => x * 2);
                addResult('JavaScript الأساسي', true, 'يعمل بشكل صحيح');
            } catch (error) {
                addResult('JavaScript الأساسي', false, `خطأ: ${error.message}`);
            }
            
            // اختبار 2: localStorage
            try {
                localStorage.setItem('test_key', 'test_value');
                const value = localStorage.getItem('test_key');
                localStorage.removeItem('test_key');
                addResult('التخزين المحلي', value === 'test_value', 
                    value === 'test_value' ? 'يعمل بشكل صحيح' : 'لا يعمل بشكل صحيح');
            } catch (error) {
                addResult('التخزين المحلي', false, `خطأ: ${error.message}`);
            }
            
            // اختبار 3: Fetch API
            try {
                const response = await fetch('./package.json');
                addResult('Fetch API', response.ok, 
                    response.ok ? 'يعمل بشكل صحيح' : `خطأ HTTP: ${response.status}`);
            } catch (error) {
                addResult('Fetch API', false, `خطأ: ${error.message}`);
            }
            
            // اختبار 4: ES6 Modules
            try {
                const testModule = await import('data:text/javascript,export default "test"');
                addResult('ES6 Modules', testModule.default === 'test', 
                    testModule.default === 'test' ? 'مدعومة' : 'غير مدعومة');
            } catch (error) {
                addResult('ES6 Modules', false, `خطأ: ${error.message}`);
            }
            
            const allPassed = testResults.every(r => r.status);
            updateStatus(
                allPassed ? 'جميع الاختبارات الأساسية نجحت! ✅' : 'بعض الاختبارات فشلت ❌',
                allPassed ? 'success' : 'error'
            );
        }

        async function testModuleLoading() {
            testResults = [];
            updateStatus('جاري اختبار تحميل الوحدات...', 'info');
            
            const modules = [
                { name: 'main.js', path: './main.js' },
                { name: 'router.js', path: './router.js' },
                { name: 'components/Modal.js', path: './components/Modal.js' },
                { name: 'services/authService.js', path: './services/authService.js' },
                { name: 'store/store.js', path: './store/store.js' },
                { name: 'utils/utility.js', path: './utils/utility.js' },
                { name: 'data/mockData.js', path: './data/mockData.js' }
            ];
            
            for (const module of modules) {
                try {
                    const response = await fetch(module.path);
                    addResult(module.name, response.ok, 
                        response.ok ? 'تم التحميل بنجاح' : `خطأ HTTP: ${response.status}`);
                } catch (error) {
                    addResult(module.name, false, `خطأ في التحميل: ${error.message}`);
                }
            }
            
            const allPassed = testResults.every(r => r.status);
            updateStatus(
                allPassed ? 'جميع الوحدات تم تحميلها بنجاح! ✅' : 'بعض الوحدات فشلت في التحميل ❌',
                allPassed ? 'success' : 'error'
            );
        }

        // اختبار تلقائي عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                testBasicFunctionality();
            }, 500);
        });

        // التحقق من البروتوكول
        if (window.location.protocol === 'file:') {
            updateStatus('⚠️ تحذير: يتم تشغيل الملفات من file:// - قد تحدث مشاكل في تحميل الوحدات. يُنصح بتشغيل خادم ويب محلي.', 'error');
        }
    </script>
</body>
</html>

/**
 * StatCard Component - بطاقة إحصائية
 * يوفر بطاقات إحصائية موحدة مع أيقونات وقيم
 */

export class StatCard {
    constructor(options = {}) {
        this.options = {
            title: '',
            value: 0,
            icon: 'fas fa-chart-bar',
            color: 'blue', // blue, green, orange, red, purple
            trend: null, // { value: 5, direction: 'up' | 'down' }
            subtitle: '',
            clickable: false,
            onClick: null,
            ...options
        };
    }

    render() {
        const cardClass = this.options.clickable ? 'stat-card clickable' : 'stat-card';
        const colorClass = `stat-card-${this.options.color}`;
        
        const card = document.createElement('div');
        card.className = `${cardClass} ${colorClass}`;
        
        if (this.options.clickable && this.options.onClick) {
            card.addEventListener('click', this.options.onClick);
            card.style.cursor = 'pointer';
        }
        
        card.innerHTML = this.getCardHTML();
        
        return card;
    }

    getCardHTML() {
        return `
            <div class="stat-icon ${this.options.color}">
                <i class="${this.options.icon}"></i>
            </div>
            <div class="stat-info">
                <h3 class="stat-title">${this.options.title}</h3>
                <p class="stat-value">${this.formatValue(this.options.value)}</p>
                ${this.options.subtitle ? `<span class="stat-subtitle">${this.options.subtitle}</span>` : ''}
                ${this.options.trend ? this.getTrendHTML() : ''}
            </div>
        `;
    }

    getTrendHTML() {
        const trend = this.options.trend;
        const trendClass = trend.direction === 'up' ? 'trend-up' : 'trend-down';
        const trendIcon = trend.direction === 'up' ? 'fas fa-arrow-up' : 'fas fa-arrow-down';
        
        return `
            <div class="stat-trend ${trendClass}">
                <i class="${trendIcon}"></i>
                <span>${trend.value}%</span>
            </div>
        `;
    }

    formatValue(value) {
        if (typeof value === 'number') {
            return value.toLocaleString('ar-SA');
        }
        return value;
    }

    update(newOptions) {
        this.options = { ...this.options, ...newOptions };
    }
}

// دوال مساعدة لإنشاء بطاقات سريعة
export function createStatCard(container, options) {
    const card = new StatCard(options);
    const cardElement = card.render();
    
    if (typeof container === 'string') {
        const containerElement = document.getElementById(container);
        if (containerElement) {
            containerElement.appendChild(cardElement);
        }
    } else if (container instanceof HTMLElement) {
        container.appendChild(cardElement);
    }
    
    return card;
}

export function createStatsGrid(container, statsArray) {
    const grid = document.createElement('div');
    grid.className = 'stats-grid';
    
    statsArray.forEach(statOptions => {
        const card = new StatCard(statOptions);
        grid.appendChild(card.render());
    });
    
    if (typeof container === 'string') {
        const containerElement = document.getElementById(container);
        if (containerElement) {
            containerElement.appendChild(grid);
        }
    } else if (container instanceof HTMLElement) {
        container.appendChild(grid);
    }
    
    return grid;
}

// بطاقات إحصائية محددة مسبقاً للمشروع
export const FLEET_STAT_CARDS = {
    totalVehicles: (count) => new StatCard({
        title: 'إجمالي المركبات',
        value: count,
        icon: 'fas fa-car',
        color: 'blue'
    }),
    
    activeVehicles: (count) => new StatCard({
        title: 'المركبات النشطة',
        value: count,
        icon: 'fas fa-truck',
        color: 'green'
    }),
    
    inMaintenance: (count) => new StatCard({
        title: 'في الصيانة',
        value: count,
        icon: 'fas fa-tools',
        color: 'orange'
    }),
    
    inactiveVehicles: (count) => new StatCard({
        title: 'المركبات المتوقفة',
        value: count,
        icon: 'fas fa-times-circle',
        color: 'red'
    }),
    
    totalDrivers: (count) => new StatCard({
        title: 'إجمالي السائقين',
        value: count,
        icon: 'fas fa-users',
        color: 'purple'
    }),
    
    fuelConsumption: (amount) => new StatCard({
        title: 'استهلاك الوقود',
        value: amount,
        icon: 'fas fa-gas-pump',
        color: 'orange',
        subtitle: 'لتر'
    }),
    
    maintenanceCost: (cost) => new StatCard({
        title: 'تكلفة الصيانة',
        value: cost,
        icon: 'fas fa-dollar-sign',
        color: 'red',
        subtitle: 'ريال سعودي'
    })
};

// إنشاء CSS للبطاقات إذا لم يكن موجوداً
export function injectStatCardCSS() {
    if (document.getElementById('stat-card-styles')) return;
    
    const css = `
        <style id="stat-card-styles">
            .stats-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 1rem;
                margin-bottom: 2rem;
            }
            
            .stat-card {
                background: white;
                border-radius: 8px;
                padding: 1.5rem;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                display: flex;
                align-items: center;
                gap: 1rem;
                transition: transform 0.2s, box-shadow 0.2s;
            }
            
            .stat-card.clickable:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 8px rgba(0,0,0,0.15);
            }
            
            .stat-icon {
                width: 60px;
                height: 60px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 24px;
                color: white;
            }
            
            .stat-icon.blue { background: #0057ff; }
            .stat-icon.green { background: #28a745; }
            .stat-icon.orange { background: #fd7e14; }
            .stat-icon.red { background: #dc3545; }
            .stat-icon.purple { background: #6f42c1; }
            
            .stat-info {
                flex: 1;
            }
            
            .stat-title {
                margin: 0 0 0.5rem 0;
                font-size: 14px;
                color: #666;
                font-weight: 500;
            }
            
            .stat-value {
                margin: 0;
                font-size: 28px;
                font-weight: bold;
                color: #333;
            }
            
            .stat-subtitle {
                font-size: 12px;
                color: #888;
            }
            
            .stat-trend {
                display: flex;
                align-items: center;
                gap: 0.25rem;
                font-size: 12px;
                margin-top: 0.5rem;
            }
            
            .trend-up { color: #28a745; }
            .trend-down { color: #dc3545; }
        </style>
    `;
    
    document.head.insertAdjacentHTML('beforeend', css);
}

// تحميل CSS تلقائياً
injectStatCardCSS();

export default StatCard;

// Service Worker for Fleet Management System

const CACHE_NAME = 'fleet-management-cache-v1';
const staticAssets = [
    '/',
    '/index.html',
    '/styles.css',
    '/script.js',
    '/auth.js',
    '/dashboard.js',
    '/vehicles.js',
    '/maintenance.js',
    '/fuel.js',
    '/drivers.js',
    '/users.js',
    '/reports.js',
    '/events.js',
    '/utility.js'
];

// Install event
self.addEventListener('install', event => {
    console.log('[ServiceWorker] Installed');
    
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then(cache => {
                console.log('[ServiceWorker] Caching files');
                // Use a more robust approach to add each file individually
                return Promise.all(staticAssets.map(url => {
                    // Try to fetch and cache each asset individually
                    return fetch(url)
                        .then(response => {
                            // Only cache successful responses
                            if (response.ok) {
                                return cache.put(url, response);
                            }
                            // Otherwise log the failure but don't fail the whole process
                            console.log(`[ServiceWorker] Failed to cache: ${url}`);
                            return Promise.resolve();
                        })
                        .catch(error => {
                            console.log(`[ServiceWorker] Error caching ${url}: ${error}`);
                            // Continue with other files even if one fails
                            return Promise.resolve();
                        });
                }));
            })
            .catch(error => {
                console.log('[ServiceWorker] Cache error:', error);
            })
    );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
    console.log('[ServiceWorker] Activated');
    
    event.waitUntil(
        caches.keys()
            .then(keyList => {
                return Promise.all(keyList.map(key => {
                    if (key !== CACHE_NAME) {
                        console.log('[ServiceWorker] Removing old cache:', key);
                        return caches.delete(key);
                    }
                }));
            })
    );
    
    // Immediately claim clients
    return self.clients.claim();
});

// Fetch event - serve from cache or network
self.addEventListener('fetch', event => {
    // Skip for cross-origin requests to avoid CORS issues
    if (!event.request.url.startsWith(self.location.origin)) {
        return;
    }
    
    event.respondWith(
        caches.match(event.request)
            .then(response => {
                // Return cached response if found
                if (response) {
                    return response;
                }
                
                // Otherwise fetch from network
                return fetch(event.request)
                    .then(response => {
                        // Don't cache if not a valid response
                        if (!response || response.status !== 200 || response.type !== 'basic') {
                            return response;
                        }
                        
                        // Clone the response since it's a stream that can only be consumed once
                        const responseToCache = response.clone();
                        
                        caches.open(CACHE_NAME)
                            .then(cache => {
                                cache.put(event.request, responseToCache);
                            });
                            
                        return response;
                    });
            })
            .catch(error => {
                console.log('[ServiceWorker] Fetch error:', error);
                
                // For HTML requests, return a fallback page
                if (event.request.headers.get('accept').includes('text/html')) {
                    return caches.match('/index.html');
                }
                
                // Let the error propagate for other resource types
                throw error;
            })
    );
});

// Handle messages from clients
self.addEventListener('message', event => {
    if (event.data.action === 'skipWaiting') {
        self.skipWaiting();
    }
});

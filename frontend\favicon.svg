<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 64 64" width="64" height="64">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0057ff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#003bb3;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background Circle -->
  <circle cx="32" cy="32" r="30" fill="url(#grad1)" stroke="#ffffff" stroke-width="2"/>
  
  <!-- Car Icon -->
  <g transform="translate(16, 20)">
    <!-- Car Body -->
    <rect x="4" y="8" width="24" height="12" rx="2" fill="#ffffff"/>
    
    <!-- Car Roof -->
    <path d="M8 8 L12 4 L20 4 L24 8 Z" fill="#ffffff"/>
    
    <!-- Wheels -->
    <circle cx="8" cy="22" r="3" fill="#333333"/>
    <circle cx="24" cy="22" r="3" fill="#333333"/>
    
    <!-- Wheel Centers -->
    <circle cx="8" cy="22" r="1.5" fill="#ffffff"/>
    <circle cx="24" cy="22" r="1.5" fill="#ffffff"/>
    
    <!-- Windows -->
    <rect x="6" y="6" width="4" height="4" rx="1" fill="#87ceeb"/>
    <rect x="22" y="6" width="4" height="4" rx="1" fill="#87ceeb"/>
    
    <!-- Headlight -->
    <circle cx="30" cy="14" r="1.5" fill="#ffff99"/>
  </g>
</svg>

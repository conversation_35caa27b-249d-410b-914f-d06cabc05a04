# Utils Directory - مجلد الأدوات المساعدة

هذا المجلد يحتوي على جميع الأدوات المساعدة والوظائف العامة المستخدمة في التطبيق.

## هيكل المجلد

```
utils/
├── index.js                 # فهرس الأدوات الرئيسي
├── constants.js             # الثوابت والإعدادات
├── utility.js               # الدوال المساعدة العامة
├── permissions.js           # نظام الصلاحيات والأدوار
├── validation.js            # التحقق من صحة البيانات
├── storage.js               # إدارة التخزين المحلي
└── README.md               # هذا الملف
```

## الأدوات المتاحة

### 1. Constants (الثوابت)
**الملف**: `constants.js`

يحتوي على جميع الثوابت والإعدادات المستخدمة في التطبيق.

#### المحتويات:
- أنواع الإشعارات ومدتها
- تنسيقات التاريخ والوقت
- حالات المركبات والسائقين
- أنواع الصيانة والوقود
- إعدادات API والتطبيق

#### مثال الاستخدام:
```javascript
import { NOTIFICATION_TYPES, VEHICLE_STATUS, DATE_FORMATS } from './utils/constants.js';

// استخدام أنواع الإشعارات
showNotification('تم الحفظ بنجاح', NOTIFICATION_TYPES.SUCCESS);

// استخدام حالات المركبات
const activeVehicles = vehicles.filter(v => v.status === VEHICLE_STATUS.ACTIVE);

// استخدام تنسيقات التاريخ
const formattedDate = formatDate(new Date(), DATE_FORMATS.DISPLAY);
```

### 2. Utility Functions (الدوال المساعدة)
**الملف**: `utility.js`

مجموعة شاملة من الدوال المساعدة للاستخدام العام.

#### المميزات:
- تنسيق الأرقام والعملات والتواريخ
- التحقق من صحة البيانات
- إدارة الإشعارات
- معالجة النصوص والمصفوفات
- تصدير البيانات
- نسخ النصوص

#### مثال الاستخدام:
```javascript
import { 
    formatNumber, 
    formatCurrency, 
    formatDate, 
    isValidEmail, 
    showNotification,
    downloadCSV 
} from './utils/utility.js';

// تنسيق الأرقام
const formatted = formatNumber(1234567); // "1,234,567"

// تنسيق العملة
const price = formatCurrency(1500); // "1,500 ريال"

// التحقق من البريد الإلكتروني
const isValid = isValidEmail('<EMAIL>'); // true

// عرض إشعار
showNotification('تم الحفظ بنجاح', 'success');

// تصدير البيانات
downloadCSV(vehiclesData, 'vehicles.csv');
```

### 3. Permissions System (نظام الصلاحيات)
**الملف**: `permissions.js`

نظام شامل لإدارة الأدوار والصلاحيات.

#### المميزات:
- تعريف الأدوار والصلاحيات
- التحقق من الصلاحيات
- فلترة البيانات حسب الصلاحيات
- إدارة الوصول للصفحات والعمليات

#### مثال الاستخدام:
```javascript
import { hasPermission, canAccessPage, ROLES, PERMISSIONS } from './utils/permissions.js';

// التحقق من صلاحية
const canEdit = hasPermission(userRole, PERMISSIONS.MANAGE_VEHICLES_ALL_BRANCHES);

// التحقق من الوصول للصفحة
const canViewReports = canAccessPage(userRole, 'reports');

// فلترة البيانات حسب الفرع
const accessibleVehicles = filterByBranchAccess(vehicles, userRole, userBranch);
```

### 4. Validation System (نظام التحقق)
**الملف**: `validation.js`

نظام شامل للتحقق من صحة البيانات والنماذج.

#### المميزات:
- قواعد تحقق متنوعة
- رسائل خطأ قابلة للتخصيص
- دعم التحقق المتقدم
- عرض الأخطاء في النماذج

#### مثال الاستخدام:
```javascript
import { validateVehicle, validateForm, Validator } from './utils/validation.js';

// التحقق من بيانات المركبة
const result = validateVehicle(vehicleData);
if (!result.isValid) {
    console.log('Errors:', result.errors);
}

// التحقق المخصص
const validator = new Validator();
validator
    .addRule('email', 'required')
    .addRule('email', 'email')
    .addRule('password', { type: 'minLength', min: 8 });

const isValid = validator.validate(formData);
```

### 5. Storage Management (إدارة التخزين)
**الملف**: `storage.js`

نظام متقدم لإدارة التخزين المحلي مع دعم التشفير والضغط.

#### المميزات:
- تخزين آمن مع انتهاء صلاحية
- ضغط وتشفير البيانات
- تنظيف تلقائي للبيانات القديمة
- نسخ احتياطي واستعادة
- مراقبة المساحة المستخدمة

#### مثال الاستخدام:
```javascript
import { 
    setStorageItem, 
    getStorageItem, 
    saveUserSession, 
    saveCachedData 
} from './utils/storage.js';

// حفظ بيانات عادية
setStorageItem('user_preferences', preferences);

// حفظ مع انتهاء صلاحية
setStorageItem('temp_data', data, { expires: Date.now() + 3600000 }); // ساعة واحدة

// حفظ جلسة المستخدم
saveUserSession(userData);

// حفظ بيانات مؤقتة
saveCachedData('vehicles', vehiclesData, 24); // 24 ساعة
```

## الاستخدام العام

### استيراد الأدوات
```javascript
// استيراد أداة واحدة
import { formatNumber } from './utils/utility.js';

// استيراد متعدد
import { 
    formatNumber, 
    hasPermission, 
    validateForm, 
    setStorageItem 
} from './utils/index.js';

// استيراد جميع الأدوات
import utils from './utils/index.js';
```

### تهيئة الأدوات
```javascript
import { initializeUtils } from './utils/index.js';

// تهيئة جميع الأدوات
const initialized = initializeUtils();
if (initialized) {
    console.log('Utils initialized successfully');
}
```

### استخدام مجموعة الأدوات
```javascript
import { utils } from './utils/index.js';

// استخدام الأدوات
const formatted = utils.format(1234, 'number');
const isValid = utils.validateData(formData, 'vehicle');
const hasAccess = utils.checkPermission(userRole, 'vehicles.edit');
utils.notify('تم الحفظ بنجاح', 'success');
```

### دوال مساعدة سريعة
```javascript
import { 
    validateData, 
    saveData, 
    loadData, 
    notify, 
    format 
} from './utils/index.js';

// التحقق من البيانات
const validation = validateData(vehicleData, 'vehicle');

// حفظ واسترجاع البيانات
saveData('user_settings', settings);
const settings = loadData('user_settings', defaultSettings);

// عرض إشعار
notify('تم الحفظ بنجاح', 'success');

// تنسيق البيانات
const price = format(1500, 'currency');
const date = format(new Date(), 'date', { format: 'long' });
```

## أمثلة متقدمة

### التحقق من النماذج
```javascript
import { validateForm } from './utils/index.js';

// التحقق من نموذج المركبة
const form = document.getElementById('vehicle-form');
const result = validateForm(form, 'vehicle');

if (result.isValid) {
    // حفظ البيانات
    console.log('Form is valid');
} else {
    // عرض الأخطاء (يتم عرضها تلقائياً في النموذج)
    console.log('Form has errors:', result.errors);
}
```

### إدارة البيانات
```javascript
import { filterData, sortData, groupData } from './utils/index.js';

// فلترة البيانات
const filteredVehicles = filterData(vehicles, 'تويوتا', ['make', 'model']);

// ترتيب البيانات
const sortedVehicles = sortData(vehicles, 'licensePlate', 'asc');

// تجميع البيانات
const groupedByBranch = groupData(vehicles, 'branch');
```

### التصدير والاستيراد
```javascript
import { exportToCSV, exportToJSON } from './utils/index.js';

// تصدير إلى CSV
exportToCSV(vehicles, 'vehicles.csv', ['License Plate', 'Vehicle Type', 'Status']);

// تصدير إلى JSON
exportToJSON(vehicles, 'vehicles.json');
```

### إدارة التخزين المؤقت
```javascript
import { cacheData, getCachedData, clearCache } from './utils/index.js';

// حفظ في التخزين المؤقت
cacheData('vehicles', vehiclesData, 12); // 12 ساعة

// استرجاع من التخزين المؤقت
const cachedVehicles = getCachedData('vehicles');

// مسح التخزين المؤقت
clearCache('vehicles'); // مسح نوع معين
clearCache(); // مسح جميع البيانات المؤقتة
```

## معلومات النظام

### الحصول على معلومات النظام
```javascript
import { getSystemInfo } from './utils/index.js';

const systemInfo = getSystemInfo();
console.log('Browser:', systemInfo.browser);
console.log('Screen:', systemInfo.screen);
console.log('Storage:', systemInfo.storage);
console.log('Features:', systemInfo.features);
```

## أفضل الممارسات

### 1. استخدام الثوابت
```javascript
// ✅ جيد
import { VEHICLE_STATUS } from './utils/constants.js';
if (vehicle.status === VEHICLE_STATUS.ACTIVE) { ... }

// ❌ سيء
if (vehicle.status === 'active') { ... }
```

### 2. التحقق من صحة البيانات
```javascript
// ✅ جيد
const result = validateVehicle(vehicleData);
if (result.isValid) {
    await saveVehicle(vehicleData);
}

// ❌ سيء
await saveVehicle(vehicleData); // بدون تحقق
```

### 3. معالجة الأخطاء
```javascript
// ✅ جيد
try {
    const result = await saveData('key', data);
    if (result) {
        notify('تم الحفظ بنجاح', 'success');
    }
} catch (error) {
    notify('فشل في الحفظ', 'error');
}
```

### 4. استخدام التخزين المؤقت
```javascript
// ✅ جيد
let vehicles = getCachedData('vehicles');
if (!vehicles) {
    vehicles = await fetchVehicles();
    cacheData('vehicles', vehicles, 6); // 6 ساعات
}

// ❌ سيء
const vehicles = await fetchVehicles(); // في كل مرة
```

## الاختبار

### اختبار الأدوات
```javascript
// اختبار تنسيق الأرقام
console.assert(formatNumber(1234) === '1,234', 'Number formatting failed');

// اختبار التحقق من البريد الإلكتروني
console.assert(isValidEmail('<EMAIL>') === true, 'Email validation failed');

// اختبار الصلاحيات
console.assert(hasPermission('Admin', 'vehicles.edit') === true, 'Permission check failed');
```

## الأداء

### نصائح لتحسين الأداء
1. استخدم التخزين المؤقت للبيانات المتكررة
2. تجنب التحقق من الصلاحيات في الحلقات
3. استخدم debouncing للبحث والفلترة
4. نظف التخزين المحلي بانتظام
5. استخدم lazy loading للبيانات الكبيرة

## المساهمة

عند إضافة أداة جديدة:
1. اتبع نمط التطوير المحدد
2. أضف التوثيق الكامل
3. اختبر في بيئات مختلفة
4. حدث ملف index.js
5. حدث هذا التوثيق

## الملاحظات

- جميع الأدوات تدعم اللغة العربية
- معالجة شاملة للأخطاء
- تحسين الأداء والذاكرة
- دعم المتصفحات الحديثة
- توافق مع معايير الويب

/**
 * Layout CSS - تنسيق التخطيط
 * يحتوي على أنماط التخطيط الأساسي للتطبيق
 */

/* ===== الرأس (Header) ===== */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: var(--header-height);
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
    z-index: 100;
    display: flex;
    align-items: center;
    padding: 0 var(--spacing-lg);
}

.header-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.header-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-right: auto;
}

.header-logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-lg);
    font-weight: var(--font-bold);
    color: var(--primary-color);
    text-decoration: none;
}

.header-logo img {
    width: 32px;
    height: 32px;
}

.header-toggle {
    background: none;
    border: none;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.header-toggle:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

.header-search {
    position: relative;
    width: 300px;
}

.header-search input {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    padding-right: 2.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    background: var(--bg-secondary);
    transition: all var(--transition-fast);
}

.header-search input:focus {
    background: var(--bg-primary);
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

.header-search-icon {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
}

.header-notifications {
    position: relative;
}

.header-notifications-btn {
    background: none;
    border: none;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition-fast);
    position: relative;
}

.header-notifications-btn:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

.header-notifications-badge {
    position: absolute;
    top: 0;
    left: 0;
    background: var(--error-color);
    color: var(--text-light);
    font-size: var(--font-xs);
    font-weight: var(--font-bold);
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
}

.header-user {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.header-user:hover {
    background: var(--bg-secondary);
}

.header-user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: var(--primary-color);
    color: var(--text-light);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: var(--font-semibold);
}

.header-user-info {
    display: flex;
    flex-direction: column;
}

.header-user-name {
    font-size: var(--font-sm);
    font-weight: var(--font-medium);
    color: var(--text-primary);
}

.header-user-role {
    font-size: var(--font-xs);
    color: var(--text-secondary);
}

/* ===== الشريط الجانبي (Sidebar) ===== */
.sidebar {
    position: fixed;
    top: 0;
    right: 0;
    width: var(--sidebar-width);
    height: 100vh;
    background: var(--bg-primary);
    border-left: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
    z-index: 90;
    transition: width var(--transition-normal);
    overflow: hidden;
}

.sidebar.collapsed {
    width: var(--sidebar-collapsed-width);
}

.sidebar-header {
    height: var(--header-height);
    padding: 0 var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.sidebar.collapsed .sidebar-header {
    padding: 0 var(--spacing-md);
    justify-content: center;
}

.sidebar-logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-lg);
    font-weight: var(--font-bold);
    color: var(--primary-color);
}

.sidebar.collapsed .sidebar-logo-text {
    display: none;
}

.sidebar-content {
    padding: var(--spacing-lg) 0;
    height: calc(100vh - var(--header-height));
    overflow-y: auto;
}

.sidebar.collapsed .sidebar-content {
    padding: var(--spacing-lg) var(--spacing-sm);
}

.sidebar-nav {
    list-style: none;
    margin: 0;
    padding: 0;
}

.sidebar-nav-item {
    margin-bottom: var(--spacing-xs);
}

.sidebar-nav-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md) var(--spacing-lg);
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: var(--radius-md);
    margin: 0 var(--spacing-md);
    transition: all var(--transition-fast);
    position: relative;
}

.sidebar.collapsed .sidebar-nav-link {
    padding: var(--spacing-md);
    margin: 0 var(--spacing-sm);
    justify-content: center;
}

.sidebar-nav-link:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

.sidebar-nav-link.active {
    background: var(--primary-color);
    color: var(--text-light);
}

.sidebar-nav-link.active::before {
    content: '';
    position: absolute;
    right: -var(--spacing-md);
    top: 50%;
    transform: translateY(-50%);
    width: 3px;
    height: 20px;
    background: var(--primary-color);
    border-radius: 2px;
}

.sidebar-nav-icon {
    font-size: var(--font-lg);
    width: 20px;
    text-align: center;
}

.sidebar-nav-text {
    font-size: var(--font-sm);
    font-weight: var(--font-medium);
}

.sidebar.collapsed .sidebar-nav-text {
    display: none;
}

.sidebar-nav-badge {
    background: var(--error-color);
    color: var(--text-light);
    font-size: var(--font-xs);
    font-weight: var(--font-bold);
    padding: 2px 6px;
    border-radius: 10px;
    margin-right: auto;
}

.sidebar.collapsed .sidebar-nav-badge {
    display: none;
}

.sidebar-section {
    margin: var(--spacing-lg) 0;
}

.sidebar-section-title {
    font-size: var(--font-xs);
    font-weight: var(--font-semibold);
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    padding: 0 var(--spacing-lg);
    margin-bottom: var(--spacing-md);
}

.sidebar.collapsed .sidebar-section-title {
    display: none;
}

.sidebar-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
}

.sidebar.collapsed .sidebar-footer {
    padding: var(--spacing-md);
}

/* ===== المحتوى الرئيسي ===== */
.main-wrapper {
    margin-right: var(--sidebar-width);
    margin-top: var(--header-height);
    min-height: calc(100vh - var(--header-height));
    transition: margin-right var(--transition-normal);
}

.main-wrapper.sidebar-collapsed {
    margin-right: var(--sidebar-collapsed-width);
}

.page-header {
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
    padding: var(--spacing-lg);
}

.page-title {
    font-size: var(--font-2xl);
    font-weight: var(--font-bold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.page-subtitle {
    font-size: var(--font-base);
    color: var(--text-secondary);
}

.page-actions {
    display: flex;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-md);
}

.page-content {
    padding: var(--spacing-lg);
    background: var(--bg-secondary);
    min-height: calc(100vh - var(--header-height) - 120px);
}

/* ===== التنقل المتدرج (Breadcrumb) ===== */
.breadcrumb {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
    font-size: var(--font-sm);
}

.breadcrumb-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.breadcrumb-item:not(:last-child)::after {
    content: '/';
    color: var(--text-muted);
}

.breadcrumb-link {
    color: var(--text-secondary);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.breadcrumb-link:hover {
    color: var(--primary-color);
}

.breadcrumb-item.active {
    color: var(--text-primary);
    font-weight: var(--font-medium);
}

/* ===== القائمة المنسدلة ===== */
.dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    min-width: 200px;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all var(--transition-fast);
}

.dropdown.show .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: block;
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--text-primary);
    text-decoration: none;
    border: none;
    background: none;
    text-align: right;
    cursor: pointer;
    transition: background-color var(--transition-fast);
}

.dropdown-item:hover {
    background: var(--bg-secondary);
}

.dropdown-item:first-child {
    border-radius: var(--radius-md) var(--radius-md) 0 0;
}

.dropdown-item:last-child {
    border-radius: 0 0 var(--radius-md) var(--radius-md);
}

.dropdown-divider {
    height: 1px;
    background: var(--border-color);
    margin: var(--spacing-xs) 0;
}

/* ===== التبويبات ===== */
.tabs {
    border-bottom: 1px solid var(--border-color);
    margin-bottom: var(--spacing-lg);
}

.tabs-nav {
    display: flex;
    gap: var(--spacing-md);
    list-style: none;
    margin: 0;
    padding: 0;
}

.tabs-nav-item {
    margin-bottom: -1px;
}

.tabs-nav-link {
    display: block;
    padding: var(--spacing-md) var(--spacing-lg);
    color: var(--text-secondary);
    text-decoration: none;
    border-bottom: 2px solid transparent;
    transition: all var(--transition-fast);
}

.tabs-nav-link:hover {
    color: var(--text-primary);
}

.tabs-nav-link.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* ===== الاستجابة للتخطيط ===== */
@media (max-width: 1024px) {
    .header-search {
        width: 200px;
    }
}

@media (max-width: 768px) {
    .sidebar {
        transform: translateX(100%);
        transition: transform var(--transition-normal);
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-wrapper {
        margin-right: 0;
    }
    
    .header-search {
        display: none;
    }
    
    .header-user-info {
        display: none;
    }
    
    .page-header {
        padding: var(--spacing-md);
    }
    
    .page-content {
        padding: var(--spacing-md);
    }
    
    .page-actions {
        flex-direction: column;
    }
    
    .tabs-nav {
        overflow-x: auto;
        white-space: nowrap;
    }
    
    .tabs-nav-link {
        white-space: nowrap;
    }
}

@media (max-width: 576px) {
    .header {
        padding: 0 var(--spacing-md);
    }
    
    .page-title {
        font-size: var(--font-xl);
    }
    
    .breadcrumb {
        font-size: var(--font-xs);
    }
    
    .dropdown-menu {
        min-width: 150px;
    }
}

/* ===== الوضع المظلم ===== */
.dark-mode {
    --bg-primary: #1e293b;
    --bg-secondary: #334155;
    --bg-tertiary: #475569;
    --text-primary: #f1f5f9;
    --text-secondary: #cbd5e1;
    --text-muted: #94a3b8;
    --border-color: #475569;
    --border-dark: #64748b;
}

.dark-mode .sidebar {
    background: var(--bg-dark);
}

.dark-mode .header {
    background: var(--bg-dark);
}

.dark-mode .page-header {
    background: var(--bg-dark);
}

/* ===== تأثيرات خاصة ===== */
.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in-right {
    animation: slideInRight 0.3s ease-in-out;
}

@keyframes slideInRight {
    from { transform: translateX(100%); }
    to { transform: translateX(0); }
}

.bounce-in {
    animation: bounceIn 0.5s ease-in-out;
}

@keyframes bounceIn {
    0% { transform: scale(0.3); opacity: 0; }
    50% { transform: scale(1.05); }
    70% { transform: scale(0.9); }
    100% { transform: scale(1); opacity: 1; }
}

/**
 * API Service - خدمة التواصل مع الخادم
 * يوفر واجهة موحدة للتواصل مع Google Apps Script API
 */

import { showSpinner, hideSpinner } from '../components/Spinner.js';

// إعدادات API
const API_CONFIG = {
    BASE_URL: 'https://script.google.com/macros/s/AKfycbyvgby4dtYeqahohK7loTDqU4ek3yYBi_8J_J5c5CBF4aBF0tWflyiKaC0AJK1xy-oW/exec',
    TIMEOUT: 30000, // 30 ثانية
    RETRY_ATTEMPTS: 3,
    RETRY_DELAY: 1000 // 1 ثانية
};

export class ApiService {
    constructor() {
        this.baseUrl = API_CONFIG.BASE_URL;
        this.timeout = API_CONFIG.TIMEOUT;
        this.retryAttempts = API_CONFIG.RETRY_ATTEMPTS;
        this.retryDelay = API_CONFIG.RETRY_DELAY;
    }

    /**
     * تنفيذ طلب HTTP مع إعادة المحاولة
     */
    async request(method, endpoint = '', data = null, options = {}) {
        const config = {
            method: method.toUpperCase(),
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        };

        // إضافة البيانات للطلب
        if (data && (method.toUpperCase() === 'POST' || method.toUpperCase() === 'PUT')) {
            config.body = JSON.stringify(data);
        }

        // إضافة معاملات URL للطلبات GET
        let url = this.baseUrl;
        if (endpoint) {
            url += `?action=${endpoint}`;
        }
        
        if (data && method.toUpperCase() === 'GET') {
            const params = new URLSearchParams(data);
            url += (url.includes('?') ? '&' : '?') + params.toString();
        }

        // تنفيذ الطلب مع إعادة المحاولة
        return await this.executeWithRetry(url, config);
    }

    /**
     * تنفيذ الطلب مع إعادة المحاولة
     */
    async executeWithRetry(url, config, attempt = 1) {
        try {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), this.timeout);

            const response = await fetch(url, {
                ...config,
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
                throw new Error(`HTTP Error: ${response.status} ${response.statusText}`);
            }

            const result = await response.json();
            
            // التحقق من وجود خطأ في الاستجابة
            if (result.error) {
                throw new Error(result.error);
            }

            return result;

        } catch (error) {
            console.error(`API Request failed (attempt ${attempt}):`, error);

            // إعادة المحاولة إذا لم نصل للحد الأقصى
            if (attempt < this.retryAttempts && this.shouldRetry(error)) {
                console.log(`Retrying in ${this.retryDelay}ms...`);
                await this.delay(this.retryDelay);
                return await this.executeWithRetry(url, config, attempt + 1);
            }

            throw error;
        }
    }

    /**
     * تحديد ما إذا كان يجب إعادة المحاولة
     */
    shouldRetry(error) {
        // إعادة المحاولة في حالة مشاكل الشبكة أو timeout
        return error.name === 'AbortError' || 
               error.message.includes('fetch') ||
               error.message.includes('network');
    }

    /**
     * تأخير لفترة محددة
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // طرق HTTP المختصرة
    async get(endpoint, params = null, options = {}) {
        return await this.request('GET', endpoint, params, options);
    }

    async post(endpoint, data = null, options = {}) {
        return await this.request('POST', endpoint, data, options);
    }

    async put(endpoint, data = null, options = {}) {
        return await this.request('PUT', endpoint, data, options);
    }

    async delete(endpoint, data = null, options = {}) {
        return await this.request('DELETE', endpoint, data, options);
    }
}

// إنشاء instance مشترك
const apiService = new ApiService();

/**
 * دالة مساعدة لتنفيذ طلبات API مع مؤشر التحميل
 */
export async function apiFetch(method, endpoint, data = null, options = {}) {
    const showLoader = options.showLoader !== false;
    
    try {
        if (showLoader) {
            showSpinner('جاري التحميل...');
        }

        const result = await apiService.request(method, endpoint, data, options);
        return result;

    } catch (error) {
        console.error('API Error:', error);
        
        // إظهار رسالة خطأ للمستخدم
        if (options.showError !== false) {
            showNotification(
                error.message || 'حدث خطأ أثناء الاتصال بالخادم',
                'error'
            );
        }
        
        throw error;
    } finally {
        if (showLoader) {
            hideSpinner();
        }
    }
}

/**
 * دوال API محددة للمشروع
 */
export const fleetAPI = {
    // المصادقة
    async login(credentials) {
        return await apiFetch('POST', 'login', credentials);
    },

    async logout() {
        return await apiFetch('POST', 'logout');
    },

    // المركبات
    async getVehicles() {
        return await apiFetch('GET', 'getVehicles');
    },

    async addVehicle(vehicleData) {
        return await apiFetch('POST', 'addVehicle', vehicleData);
    },

    async updateVehicle(vehicleId, vehicleData) {
        return await apiFetch('PUT', 'updateVehicle', { id: vehicleId, ...vehicleData });
    },

    async deleteVehicle(vehicleId) {
        return await apiFetch('DELETE', 'deleteVehicle', { id: vehicleId });
    },

    // السائقين
    async getDrivers() {
        return await apiFetch('GET', 'getDrivers');
    },

    async addDriver(driverData) {
        return await apiFetch('POST', 'addDriver', driverData);
    },

    // الصيانة
    async getMaintenanceRecords() {
        return await apiFetch('GET', 'getMaintenanceRecords');
    },

    async addMaintenanceRecord(maintenanceData) {
        return await apiFetch('POST', 'addMaintenanceRecord', maintenanceData);
    },

    // الوقود
    async getFuelRecords() {
        return await apiFetch('GET', 'getFuelRecords');
    },

    async addFuelRecord(fuelData) {
        return await apiFetch('POST', 'addFuelRecord', fuelData);
    },

    // المستخدمين
    async getUsers() {
        return await apiFetch('GET', 'getUsers');
    },

    async addUser(userData) {
        return await apiFetch('POST', 'addUser', userData);
    },

    // التقارير
    async generateReport(reportType, filters) {
        return await apiFetch('POST', 'generateReport', { type: reportType, filters });
    }
};

// دالة لإظهار الإشعارات (يجب تنفيذها في utility.js)
function showNotification(message, type = 'info') {
    // TODO: تنفيذ نظام الإشعارات
    console.log(`${type.toUpperCase()}: ${message}`);
}

export default apiService;

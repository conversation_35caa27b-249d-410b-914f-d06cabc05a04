<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النظام - Fleet Management System</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-item {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            border-left: 4px solid #ccc;
        }
        .test-success {
            background: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        .test-error {
            background: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        .test-warning {
            background: #fff3cd;
            border-color: #ffc107;
            color: #856404;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            direction: ltr;
            text-align: left;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 اختبار نظام إدارة الأسطول</h1>
        <p>هذه الصفحة لاختبار تحميل المكونات والتأكد من عمل النظام بشكل صحيح.</p>
        
        <div id="test-results">
            <div class="test-item test-warning">
                ⏳ جاري تشغيل الاختبارات...
            </div>
        </div>
        
        <div style="margin-top: 20px;">
            <button class="btn" onclick="runTests()">🔄 إعادة تشغيل الاختبارات</button>
            <button class="btn" onclick="clearCache()">🗑️ مسح Cache المتصفح</button>
            <button class="btn" onclick="window.location.href='index.html'">🏠 العودة للصفحة الرئيسية</button>
        </div>
        
        <div id="error-details" style="margin-top: 20px; display: none;">
            <h3>تفاصيل الأخطاء:</h3>
            <pre id="error-log"></pre>
        </div>
    </div>

    <script type="module">
        let testResults = [];
        let errorLog = [];

        function addTest(name, status, message, error = null) {
            testResults.push({ name, status, message, error });
            if (error) {
                errorLog.push(`${name}: ${error.message}\n${error.stack}`);
            }
        }

        function displayResults() {
            const container = document.getElementById('test-results');
            const errorDetails = document.getElementById('error-details');
            const errorLogElement = document.getElementById('error-log');
            
            container.innerHTML = testResults.map(test => {
                const statusClass = test.status === 'success' ? 'test-success' : 
                                  test.status === 'error' ? 'test-error' : 'test-warning';
                const icon = test.status === 'success' ? '✅' : 
                           test.status === 'error' ? '❌' : '⚠️';
                
                return `<div class="test-item ${statusClass}">
                    ${icon} <strong>${test.name}:</strong> ${test.message}
                </div>`;
            }).join('');
            
            if (errorLog.length > 0) {
                errorDetails.style.display = 'block';
                errorLogElement.textContent = errorLog.join('\n\n');
            } else {
                errorDetails.style.display = 'none';
            }
        }

        async function runTests() {
            testResults = [];
            errorLog = [];
            
            // اختبار 1: تحميل الملفات الأساسية
            try {
                const response = await fetch('./main.js');
                if (response.ok) {
                    addTest('main.js', 'success', 'تم تحميل الملف الرئيسي بنجاح');
                } else {
                    addTest('main.js', 'error', `فشل في تحميل الملف: ${response.status}`);
                }
            } catch (error) {
                addTest('main.js', 'error', 'خطأ في تحميل الملف الرئيسي', error);
            }

            // اختبار 2: تحميل CSS
            try {
                const response = await fetch('./css/index.css');
                if (response.ok) {
                    addTest('CSS Files', 'success', 'تم تحميل ملفات CSS بنجاح');
                } else {
                    addTest('CSS Files', 'error', `فشل في تحميل CSS: ${response.status}`);
                }
            } catch (error) {
                addTest('CSS Files', 'error', 'خطأ في تحميل ملفات CSS', error);
            }

            // اختبار 3: تحميل المكونات
            try {
                const { default: Modal } = await import('./components/Modal.js');
                addTest('Components', 'success', 'تم تحميل المكونات بنجاح');
            } catch (error) {
                addTest('Components', 'error', 'فشل في تحميل المكونات', error);
            }

            // اختبار 4: تحميل الخدمات
            try {
                const { default: authService } = await import('./services/authService.js');
                addTest('Services', 'success', 'تم تحميل الخدمات بنجاح');
            } catch (error) {
                addTest('Services', 'error', 'فشل في تحميل الخدمات', error);
            }

            // اختبار 5: تحميل Store
            try {
                const { default: appStore } = await import('./store/store.js');
                addTest('Store', 'success', 'تم تحميل نظام إدارة الحالة بنجاح');
            } catch (error) {
                addTest('Store', 'error', 'فشل في تحميل نظام إدارة الحالة', error);
            }

            // اختبار 6: تحميل الأدوات
            try {
                const { showNotification } = await import('./utils/utility.js');
                addTest('Utils', 'success', 'تم تحميل الأدوات المساعدة بنجاح');
            } catch (error) {
                addTest('Utils', 'error', 'فشل في تحميل الأدوات المساعدة', error);
            }

            // اختبار 7: تحميل الصفحات
            try {
                const { default: pages } = await import('./pages/index.js');
                addTest('Pages', 'success', 'تم تحميل الصفحات بنجاح');
            } catch (error) {
                addTest('Pages', 'error', 'فشل في تحميل الصفحات', error);
            }

            // اختبار 8: تحميل Router
            try {
                const { default: router } = await import('./router.js');
                addTest('Router', 'success', 'تم تحميل نظام التوجيه بنجاح');
            } catch (error) {
                addTest('Router', 'error', 'فشل في تحميل نظام التوجيه', error);
            }

            // اختبار 9: تحميل البيانات التجريبية
            try {
                const { default: mockData } = await import('./data/mockData.js');
                addTest('Mock Data', 'success', `تم تحميل البيانات التجريبية (${mockData.vehicles.length} مركبة)`);
            } catch (error) {
                addTest('Mock Data', 'error', 'فشل في تحميل البيانات التجريبية', error);
            }

            // اختبار 10: اختبار localStorage
            try {
                localStorage.setItem('test', 'value');
                const value = localStorage.getItem('test');
                localStorage.removeItem('test');
                if (value === 'value') {
                    addTest('LocalStorage', 'success', 'التخزين المحلي يعمل بشكل صحيح');
                } else {
                    addTest('LocalStorage', 'error', 'مشكلة في التخزين المحلي');
                }
            } catch (error) {
                addTest('LocalStorage', 'error', 'فشل في اختبار التخزين المحلي', error);
            }

            displayResults();
        }

        function clearCache() {
            if ('caches' in window) {
                caches.keys().then(names => {
                    names.forEach(name => {
                        caches.delete(name);
                    });
                });
            }
            
            // مسح localStorage
            localStorage.clear();
            
            // مسح sessionStorage
            sessionStorage.clear();
            
            alert('تم مسح Cache المتصفح. يرجى إعادة تحميل الصفحة.');
            window.location.reload(true);
        }

        // تشغيل الاختبارات عند تحميل الصفحة
        window.runTests = runTests;
        window.clearCache = clearCache;
        
        // تشغيل الاختبارات تلقائياً
        runTests();
    </script>
</body>
</html>

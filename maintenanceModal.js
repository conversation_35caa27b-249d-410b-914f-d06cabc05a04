// Import required modules
import { vehicles, drivers, showNotification, API_URL } from './script.js';
import { toggleButtonSpinner } from './spinner.js';
import { getUpcomingServices } from './upcoming-services.js';

// Maintenance records management
export let selectedVehicleId = null;

// Function to update vehicle select options
function updateVehicleSelectOptions(modal) {
    try {
        if (!modal) return;

        const vehicleSelect = modal.querySelector('#maintenance-vehicle');
        if (!vehicleSelect) return;

        console.log('Updating vehicle select options');
        console.log('Available vehicles:', vehicles);

        // Clear existing options
        vehicleSelect.innerHTML = '<option value="">Select Vehicle</option>';

        // Add vehicle options
        if (vehicles && vehicles.length > 0) {
            vehicles.forEach(vehicle => {
                const vehicleId = vehicle['Vehicle ID'] || vehicle.id;
                const licensePlate = vehicle['License Plate'] || vehicleId;

                const option = document.createElement('option');
                option.value = vehicleId;
                option.text = licensePlate;
                vehicleSelect.appendChild(option);
            });
        }
    } catch (error) {
        console.error('Error updating vehicle select options:', error);
    }
}

// Initialize maintenance modal
export function initializeMaintenanceModal() {
    try {
        const modal = document.getElementById('maintenance-modal');
        if (!modal) {
            console.error('Maintenance modal not found');
            return;
        }

        // Setup form event listeners
        const form = modal.querySelector('form');
        if (form) {
            form.removeEventListener('submit', handleMaintenanceSubmit);
            form.addEventListener('submit', handleMaintenanceSubmit);
        }

        // Setup close button
        const closeBtn = modal.querySelector('.close-btn');
        if (closeBtn) {
            closeBtn.onclick = closeMaintenanceModal;
        }

        // Setup outside click to close
        window.onclick = function(event) {
            if (event.target === modal) {
                closeMaintenanceModal();
            }
        }
    } catch (error) {
        console.error('Error initializing maintenance modal:', error);
        showNotification('Error initializing maintenance modal', 'error');
    }
}

// Function to create maintenance modal
function createMaintenanceModal() {
    // Check if modal already exists
    let modal = document.getElementById('maintenance-modal');
    if (modal) return modal;

    // Create modal HTML
    const modalHTML = `
        <div id="maintenance-modal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title">Add Maintenance Record</h3>
                    <button class="close-btn">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="maintenance-form">
                        <div class="form-group">
                            <label for="maintenance-vehicle">Vehicle</label>
                            <select id="maintenance-vehicle" name="vehicle" required>
                                <option value="">Select Vehicle</option>
                                ${vehicles.map(vehicle => {
                                    const vehicleId = vehicle['Vehicle ID'] || vehicle.id;
                                    const licensePlate = vehicle['License Plate'] || vehicleId;
                                    console.log(`Creating option: value=${vehicleId}, text=${licensePlate}`);
                                    return `<option value="${vehicleId}">${licensePlate}</option>`;
                                }).join('')}
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="maintenance-date">Service Date</label>
                            <input type="date" id="maintenance-date" name="date" required>
                        </div>
                        <div class="form-group">
                            <label for="maintenance-type">Service Type</label>
                            <select id="maintenance-type" name="service-type" required>
                                <option value="">Select Service Type</option>
                                <option value="Routine Maintenance">Routine Maintenance</option>
                                <option value="Oil Change">Oil Change</option>
                                <option value="Tire Change">Tire Change</option>
                                <option value="Tire Rotation">Tire Rotation</option>
                                <option value="Brake Service">Brake Service</option>
                                <option value="Battery Replacement">Battery Replacement</option>
                                <option value="Other">Other</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="maintenance-description">Description</label>
                            <textarea id="maintenance-description" name="description" rows="3"></textarea>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="maintenance-odometer">Odometer Reading (km)</label>
                                <input type="number" id="maintenance-odometer" name="odometer" min="0">
                            </div>
                            <div class="form-group">
                                <label for="next-maintenance-odometer">Next Service Odometer (km)</label>
                                <input type="number" id="next-maintenance-odometer" name="next-odometer" min="0">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="maintenance-cost">Total Cost</label>
                            <input type="number" id="maintenance-cost" name="cost" min="0" step="0.01">
                        </div>
                        <div class="form-actions">
                            <button type="button" id="save-maintenance-btn" class="btn btn-primary">Save</button>
                            <button type="button" id="cancel-maintenance-btn" class="btn btn-secondary">Cancel</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    `;

    // Add modal to the DOM
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // Get the newly created modal
    modal = document.getElementById('maintenance-modal');

    // Add event listeners
    const closeBtn = modal.querySelector('.close-btn');
    if (closeBtn) {
        closeBtn.addEventListener('click', closeMaintenanceModal);
    }

    const cancelBtn = modal.querySelector('#cancel-maintenance-btn');
    if (cancelBtn) {
        cancelBtn.addEventListener('click', closeMaintenanceModal);
    }

    const saveBtn = modal.querySelector('#save-maintenance-btn');
    if (saveBtn) {
        saveBtn.addEventListener('click', () => {
            handleMaintenanceSubmit();
        });
    }

    // Set today's date as default
    const dateInput = modal.querySelector('#maintenance-date');
    if (dateInput) {
        dateInput.valueAsDate = new Date();
    }

    return modal;
}

// Function to open maintenance modal
export function openMaintenanceModal(idOrVehicleId = null) {
    try {
        // Create or get the modal
        const modal = createMaintenanceModal();
        if (!modal) {
            console.error('Failed to create maintenance modal');
            return;
        }

        // Update the vehicle select options
        updateVehicleSelectOptions(modal);

        // Get the form and title element
        const form = modal.querySelector('#maintenance-form');
        const titleElement = modal.querySelector('.modal-title');

        // Reset the form
        if (form) {
            form.reset();
            // Remove any previous ID data attribute
            form.removeAttribute('data-id');
        }

        // Check if we're editing an existing record or creating a new one
        let isEditing = false;
        let maintenanceRecord = null;

        if (idOrVehicleId) {
            console.log('ID or Vehicle ID provided:', idOrVehicleId, 'Type:', typeof idOrVehicleId);
        }

        // Check if this is a maintenance ID
        if (idOrVehicleId) {
            // Log all maintenance records to debug
            console.log('All maintenance records:', window.maintenanceRecords);

            // First, try to find if this ID exists in the maintenance records
            // If it does, we're editing an existing record
            const records = window.maintenanceRecords || (typeof maintenanceRecords !== 'undefined' ? maintenanceRecords : null);

            if (records && Array.isArray(records)) {
                // Try to find the record by ID
                const recordExists = records.some(record => {
                    // Check if the ID matches exactly
                    if (record['Maintenance ID'] === idOrVehicleId) {
                        return true;
                    }

                    // If the ID is a number, try to match it as a string
                    if (typeof idOrVehicleId === 'number' || !isNaN(idOrVehicleId)) {
                        return record['Maintenance ID'] === idOrVehicleId.toString();
                    }

                    return false;
                });

                if (recordExists) {
                    console.log('Editing maintenance record with ID:', idOrVehicleId);
                    isEditing = true;
                } else {
                    console.log('Creating new maintenance record for vehicle ID:', idOrVehicleId);
                    isEditing = false;
                }
            } else {
                console.log('No maintenance records available, assuming new record');
                isEditing = false;
            }

            // Find the maintenance record
            if (isEditing && records && Array.isArray(records)) {
                console.log('Searching for maintenance record with ID:', idOrVehicleId);
                console.log('Available maintenance records:', records.map(r => ({ id: r['Maintenance ID'], vehicle: r['Vehicle ID'] })));

                // Try to find the record by ID
                maintenanceRecord = records.find(record => {
                    // Check if the ID matches exactly
                    if (record['Maintenance ID'] === idOrVehicleId) {
                        return true;
                    }

                    // If the ID is a number, try to match it as a string
                    if (typeof idOrVehicleId === 'number' || !isNaN(idOrVehicleId)) {
                        return record['Maintenance ID'] === idOrVehicleId.toString();
                    }

                    return false;
                });

                console.log('Found maintenance record:', maintenanceRecord);
            } else {
                console.error('maintenanceRecords is not available or not an array');
            }

            if (!maintenanceRecord) {
                console.error('Maintenance record not found:', idOrVehicleId);
                showNotification('Maintenance record not found', 'error');
                return;
            }

            // Set the form title to indicate editing
            if (titleElement) {
                titleElement.textContent = 'Edit Maintenance Record';
            }

            // Set the form data attribute with the record ID
            if (form) {
                form.setAttribute('data-id', idOrVehicleId);
            }

            // Populate the form with the record data
            console.log('Populating form with maintenance record data:', maintenanceRecord);
            if (form) {
                // Set vehicle
                const vehicleSelect = form.querySelector('#maintenance-vehicle');
                if (vehicleSelect) {
                    console.log('Setting vehicle select value to:', maintenanceRecord['Vehicle ID']);
                    console.log('Available options:', Array.from(vehicleSelect.options).map(opt => ({ value: opt.value, text: opt.text })));

                    // Try different ways to match the vehicle ID
                    const vehicleId = maintenanceRecord['Vehicle ID'];

                    // First try direct match
                    vehicleSelect.value = vehicleId || '';

                    // If that didn't work, try to find the option by value
                    if (!vehicleSelect.value && vehicleId) {
                        // Try to find the option with the matching value
                        const option = Array.from(vehicleSelect.options).find(opt => {
                            return opt.value === vehicleId ||
                                   opt.value === vehicleId.toString() ||
                                   opt.text === vehicleId ||
                                   opt.text === vehicleId.toString();
                        });

                        if (option) {
                            vehicleSelect.value = option.value;
                        } else {
                            // If still not found, try to find a vehicle with matching ID or license plate
                            const vehicle = vehicles.find(v => {
                                return v['Vehicle ID'] === vehicleId ||
                                       v.id === vehicleId ||
                                       v['License Plate'] === maintenanceRecord['License Plate'];
                            });

                            if (vehicle) {
                                const vehicleIdToUse = vehicle['Vehicle ID'] || vehicle.id;
                                vehicleSelect.value = vehicleIdToUse;
                            }
                        }
                    }

                    console.log('After setting, vehicle select value is:', vehicleSelect.value);

                    // If still not set, recreate the options with the vehicle included
                    if (!vehicleSelect.value && vehicleId) {
                        console.log('Vehicle not found in options, recreating options');

                        // Find the vehicle in the vehicles array
                        const vehicle = vehicles.find(v => v['Vehicle ID'] === vehicleId || v.id === vehicleId);

                        // Recreate options
                        vehicleSelect.innerHTML = `
                            <option value="">Select Vehicle</option>
                            ${vehicles.map(v => {
                                const vId = v['Vehicle ID'] || v.id;
                                const licensePlate = v['License Plate'] || vId;
                                return `<option value="${vId}" ${vId === vehicleId ? 'selected' : ''}>${licensePlate}</option>`;
                            }).join('')}
                        `;

                        // If the vehicle wasn't in the array, add it
                        if (!vehicle && maintenanceRecord['License Plate']) {
                            const option = document.createElement('option');
                            option.value = vehicleId;
                            option.text = maintenanceRecord['License Plate'] || vehicleId;
                            option.selected = true;
                            vehicleSelect.appendChild(option);
                        }
                    }
                } else {
                    console.error('Vehicle select element not found in the form');
                }

                // Set date
                const dateInput = form.querySelector('#maintenance-date');
                if (dateInput && maintenanceRecord['Service Date']) {
                    dateInput.value = maintenanceRecord['Service Date'];
                }

                // Set service type
                const typeSelect = form.querySelector('#maintenance-type');
                if (typeSelect) {
                    typeSelect.value = maintenanceRecord['Service Type'] || '';
                }

                // Set description/notes
                const descriptionTextarea = form.querySelector('#maintenance-description');
                if (descriptionTextarea) {
                    descriptionTextarea.value = maintenanceRecord['Notes'] || '';
                }

                // Set odometer reading
                const odometerInput = form.querySelector('#maintenance-odometer');
                if (odometerInput) {
                    odometerInput.value = maintenanceRecord['Odometer Reading'] || '';
                }

                // Set next service odometer (not directly available in the record)
                // We'll leave this blank or could calculate based on other data

                // Set total cost
                const costInput = form.querySelector('#maintenance-cost');
                if (costInput) {
                    costInput.value = maintenanceRecord['Total Cost'] || '';
                }
            }

            // Store the vehicle ID
            selectedVehicleId = maintenanceRecord['Vehicle ID'];
        } else if (!isEditing) {
            // This is a vehicle ID or null, so we're creating a new record
            selectedVehicleId = idOrVehicleId;

            // Set the form title to indicate adding
            if (titleElement) {
                titleElement.textContent = 'Add Maintenance Record';
            }

            // Set selected vehicle if provided
            if (idOrVehicleId) {
                const vehicleSelect = form.querySelector('#maintenance-vehicle');
                if (vehicleSelect) {
                    vehicleSelect.value = idOrVehicleId;
                }
            }

            // Set today's date as default
            const dateInput = form.querySelector('#maintenance-date');
            if (dateInput) {
                dateInput.valueAsDate = new Date();
            }
        }

        // Show the modal
        modal.style.display = 'block';

        // Add close event listener
        const closeBtn = modal.querySelector('.close-btn');
        if (closeBtn) {
            closeBtn.onclick = function() {
                modal.style.display = 'none';
            }
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        }

    } catch (error) {
        console.error('Error opening maintenance modal:', error);
        showNotification('Error opening maintenance modal', 'error');
    }
}

// Function to close maintenance modal
export function closeMaintenanceModal() {
    try {
        const modal = document.getElementById('maintenance-modal');
        if (modal) {
            modal.style.display = 'none';
        }
    } catch (error) {
        console.error('Error closing maintenance modal:', error);
    }
}

// Function to handle maintenance form submission
export function handleMaintenanceSubmit(event) {
    try {
        // Get the save button to show spinner
        const saveButton = document.getElementById('save-maintenance-btn');
        if (saveButton) {
            toggleButtonSpinner(saveButton, true, 'Saving...');
        }
        // Get the form
        const form = document.getElementById('maintenance-form');
        if (!form) {
            showNotification('Maintenance form not found', 'error');
            return;
        }

        // Get form data
        const vehicleId = form.querySelector('#maintenance-vehicle')?.value;
        const date = form.querySelector('#maintenance-date')?.value;
        const type = form.querySelector('#maintenance-type')?.value;
        const description = form.querySelector('#maintenance-description')?.value;
        const cost = parseFloat(form.querySelector('#maintenance-cost')?.value || '0');
        const odometer = parseInt(form.querySelector('#maintenance-odometer')?.value || '0');
        const nextOdometer = parseInt(form.querySelector('#next-maintenance-odometer')?.value || '0');

        console.log('Form data:', {
            vehicleId,
            date,
            type,
            description,
            cost,
            odometer,
            nextOdometer,
            recordId: form.getAttribute('data-id')
        });

        // Validate required fields
        if (!vehicleId || !date || !type) {
            showNotification('Please fill all required fields', 'error');
            return;
        }

        // Get vehicle license plate from vehicles array
        let licensePlate = '';
        if (vehicles && vehicles.length > 0) {
            const vehicle = vehicles.find(v => v.id === vehicleId || v['Vehicle ID'] === vehicleId);
            if (vehicle) {
                licensePlate = vehicle['License Plate'] || '';
            }
        }

        // Calculate next service date (3 months from service date)
        const serviceDate = new Date(date);
        const nextServiceDate = new Date(serviceDate);
        nextServiceDate.setMonth(serviceDate.getMonth() + 3);

        // Format next service date as YYYY-MM-DD
        const nextServiceDateStr = nextServiceDate.toISOString().split('T')[0];

        // Check if we're editing an existing record or creating a new one
        const recordId = form.getAttribute('data-id');
        let maintenanceData;

        if (recordId) {
            // We're editing an existing record
            console.log('Updating existing maintenance record with ID:', recordId);

            // Find the existing record
            let existingRecord = null;
            if (window.maintenanceRecords && Array.isArray(window.maintenanceRecords)) {
                existingRecord = window.maintenanceRecords.find(record =>
                    record['Maintenance ID'] === recordId);
            }

            if (!existingRecord) {
                console.error('Existing maintenance record not found:', recordId);
                showNotification('Maintenance record not found', 'error');
                return;
            }

            // Create updated maintenance record object
            maintenanceData = {
                ...existingRecord, // Keep existing data
                'Vehicle ID': vehicleId,
                'License Plate': licensePlate,
                'Service Type': type,
                'Service Center': 'In-house Service',
                'Odometer Reading': odometer.toString(),
                'Parts Cost': Math.round(cost * 0.6), // Estimate 60% of total cost for parts
                'Labor Cost': Math.round(cost * 0.4), // Estimate 40% of total cost for labor
                'Total Cost': cost,
                'Service Date': date,
                'Next Service Date': nextServiceDateStr,
                'Notes': description || ''
            };

            // Update the record in the local array
            if (window.maintenanceRecords && Array.isArray(window.maintenanceRecords)) {
                const index = window.maintenanceRecords.findIndex(record =>
                    record['Maintenance ID'] === recordId);
                if (index !== -1) {
                    window.maintenanceRecords[index] = maintenanceData;
                }
            }

            // Send data to server for update
            console.log('Updating maintenance record:', maintenanceData);
            saveMaintenanceToServer(maintenanceData, 'updateMaintenance');
        } else {
            // We're creating a new record
            console.log('Creating new maintenance record');

            // Create new maintenance record object
            maintenanceData = {
                'Maintenance ID': 'M-' + Date.now(),
                'Vehicle ID': vehicleId,
                'License Plate': licensePlate,
                'Service Type': type,
                'Service Center': 'In-house Service',
                'Odometer Reading': odometer.toString(),
                'Parts Cost': Math.round(cost * 0.6), // Estimate 60% of total cost for parts
                'Labor Cost': Math.round(cost * 0.4), // Estimate 40% of total cost for labor
                'Total Cost': cost,
                'Service Date': date,
                'Next Service Date': nextServiceDateStr,
                'Notes': description || ''
            };

            // Send data to server for creation
            console.log('Saving new maintenance record:', maintenanceData);
            saveMaintenanceToServer(maintenanceData, 'addMaintenance');
        }
    } catch (error) {
        console.error('Error submitting maintenance form:', error);
        showNotification('Error saving maintenance record', 'error');

        // Reset the save button spinner in case of error
        const saveButton = document.getElementById('save-maintenance-btn');
        if (saveButton) {
            toggleButtonSpinner(saveButton, false, 'Save');
        }
    }
}

// Function to save maintenance record to server
async function saveMaintenanceToServer(maintenanceData, action = 'addMaintenance') {
    try {
        // Show loading notification
        const isUpdate = action === 'updateMaintenance';
        showNotification(`${isUpdate ? 'Updating' : 'Saving'} maintenance record...`, 'info');

        console.log(`${isUpdate ? 'Updating' : 'Saving'} maintenance data:`, maintenanceData);

        // Create a unique callback name
        const callbackName = 'jsonpCallback_' + Date.now();

        // Create a promise to handle the JSONP response
        const jsonpPromise = new Promise((resolve, reject) => {
            // Define the callback function
            window[callbackName] = function(data) {
                // Clean up by removing the script tag and the callback function
                delete window[callbackName];
                document.body.removeChild(script);
                resolve(data);
            };

            // Handle errors
            const handleError = () => {
                delete window[callbackName];
                document.body.removeChild(script);
                reject(new Error('Failed to load script'));
            };

            // Create the script tag
            const script = document.createElement('script');
            script.src = `${API_URL}?action=${action}&data=${encodeURIComponent(JSON.stringify(maintenanceData))}&callback=${callbackName}`;
            script.onerror = handleError;
            document.body.appendChild(script);

            // Set a timeout in case the script never loads
            setTimeout(() => {
                if (window[callbackName]) {
                    handleError();
                }
            }, 10000); // 10 seconds timeout
        });

        console.log(`Sending maintenance data to server using JSONP (action: ${action})`);

        try {
            // Wait for the JSONP response
            const result = await jsonpPromise;
            console.log('Server response:', result);

            // Process the result
            if (result && result.status === 'success') {
                console.log(`Maintenance record ${isUpdate ? 'updated' : 'saved'} successfully`);
            } else {
                console.error('Error from server:', result);
                showNotification(`Error ${isUpdate ? 'updating' : 'saving'} maintenance record: ${result?.message || 'Unknown error'}`, 'error');
                return; // Exit early on error
            }
        } catch (jsonpError) {
            console.error('JSONP error:', jsonpError);
            showNotification(`Error communicating with server: ${jsonpError.message}`, 'warning');
            // Continue with local updates even if JSONP fails
        }

        // Update or add to maintenance records array
        if (typeof window.maintenanceRecords !== 'undefined') {
            if (isUpdate) {
                // For updates, the record should already be updated in the array in handleMaintenanceSubmit
                console.log('Maintenance record already updated in local array');
            } else {
                // For new records, add to the array
                window.maintenanceRecords.push(maintenanceData);
            }
        }

        // Reset the save button spinner
        const saveButton = document.getElementById('save-maintenance-btn');
        if (saveButton) {
            toggleButtonSpinner(saveButton, false, 'Save');
        }

        // Close modal and show success message
        closeMaintenanceModal();
        showNotification(`Maintenance record ${isUpdate ? 'updated' : 'saved'} successfully`, 'success');

        // Refresh maintenance table
        if (typeof window.renderMaintenanceTable === 'function') {
            window.renderMaintenanceTable();
        }

        // Refresh upcoming services
        if (typeof window.renderUpcomingServices === 'function') {
            window.renderUpcomingServices('all');
        }
    } catch (error) {
        console.error('Error saving maintenance record to server:', error);
        showNotification('Error saving maintenance record: ' + error.message, 'error');

        // Reset the save button spinner in case of error
        const saveButton = document.getElementById('save-maintenance-btn');
        if (saveButton) {
            toggleButtonSpinner(saveButton, false, 'Save');
        }
    }
}
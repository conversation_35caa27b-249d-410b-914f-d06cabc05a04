# دليل حل المشاكل - Troubleshooting Guide

## 🚨 المشكلة الشائعة: خطأ MIME Type

### الخطأ:
```
Failed to load module script: Expected a JavaScript-or-Wasm module script but the server responded with a MIME type of "text/html". Strict MIME type checking is enforced for module scripts per HTML spec.
```

### السبب:
هذا الخطأ يحدث عادة لأحد الأسباب التالية:
1. تشغيل الملفات من `file://` بدلاً من خادم ويب
2. وجود مراجع لملفات محذوفة أو غير موجودة
3. مشاكل في cache المتصفح
4. خادم الويب لا يرسل MIME type صحيح للملفات JavaScript

## ✅ الحلول

### 1. تشغيل خادم ويب محلي
**الحل الأفضل والأكثر فعالية:**

#### باستخدام Python:
```bash
# Python 3
python -m http.server 8000

# Python 2
python -m SimpleHTTPServer 8000
```

#### باستخدام Node.js:
```bash
# تثبيت serve
npm install -g serve

# تشغيل الخادم
serve . -p 8000
```

#### باستخدام PHP:
```bash
php -S localhost:8000
```

#### باستخدام Live Server (VS Code):
1. ثبت إضافة "Live Server" في VS Code
2. انقر بالزر الأيمن على `index.html`
3. اختر "Open with Live Server"

### 2. مسح Cache المتصفح
1. اضغط `Ctrl + Shift + R` (Windows/Linux) أو `Cmd + Shift + R` (Mac)
2. أو افتح Developer Tools → Application → Storage → Clear Storage

### 3. التحقق من الملفات
تأكد من وجود جميع الملفات المطلوبة:
- `main.js`
- `router.js`
- `components/index.js`
- `services/index.js`
- `store/index.js`
- `utils/index.js`
- `pages/index.js`

### 4. اختبار النظام
استخدم ملفات الاختبار المتوفرة:
- `test.html` - اختبار شامل
- `simple-test.html` - اختبار بسيط
- `debug.js` - تشخيص متقدم

## 🔧 خطوات التشخيص

### 1. فتح Developer Tools
1. اضغط `F12` أو `Ctrl + Shift + I`
2. انتقل إلى تبويب "Console"
3. ابحث عن رسائل الخطأ

### 2. فحص Network Tab
1. انتقل إلى تبويب "Network"
2. أعد تحميل الصفحة
3. ابحث عن الملفات التي فشلت في التحميل (باللون الأحمر)

### 3. فحص Sources Tab
1. انتقل إلى تبويب "Sources"
2. تأكد من وجود جميع الملفات في القائمة

## 🌐 متطلبات المتصفح

### المتصفحات المدعومة:
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

### الميزات المطلوبة:
- ES6 Modules
- Fetch API
- LocalStorage
- CSS Grid & Flexbox

## 📱 اختبار على الأجهزة المختلفة

### Desktop:
- تأكد من عمل النظام على شاشة كبيرة
- اختبر تغيير حجم النافذة

### Mobile:
- افتح Developer Tools
- فعل "Device Toolbar" (Ctrl + Shift + M)
- اختبر أحجام شاشات مختلفة

### Tablet:
- اختبر على أحجام شاشات متوسطة
- تأكد من عمل اللمس بشكل صحيح

## 🔍 أدوات التشخيص المتقدم

### 1. استخدام Console Commands:
```javascript
// فحص تحميل الوحدات
import('./main.js').then(console.log).catch(console.error);

// فحص localStorage
console.log(localStorage);

// فحص Service Workers
navigator.serviceWorker.getRegistrations().then(console.log);
```

### 2. فحص Network Issues:
```javascript
// اختبار الاتصال
fetch('./main.js')
  .then(response => console.log('Status:', response.status))
  .catch(error => console.error('Error:', error));
```

### 3. فحص Module Loading:
```javascript
// اختبار تحميل وحدة
try {
  const module = await import('./components/Modal.js');
  console.log('Module loaded:', module);
} catch (error) {
  console.error('Module loading failed:', error);
}
```

## 🛠️ إعدادات الخادم

### Apache (.htaccess):
```apache
AddType application/javascript .js
AddType application/json .json
AddType text/css .css
```

### Nginx:
```nginx
location ~* \.js$ {
    add_header Content-Type application/javascript;
}
```

### IIS (web.config):
```xml
<configuration>
  <system.webServer>
    <staticContent>
      <mimeMap fileExtension=".js" mimeType="application/javascript" />
    </staticContent>
  </system.webServer>
</configuration>
```

## 📞 الحصول على المساعدة

### إذا استمرت المشكلة:
1. تأكد من تشغيل خادم ويب محلي
2. امسح cache المتصفح تماماً
3. جرب متصفح مختلف
4. تأكد من وجود جميع الملفات
5. افحص console للأخطاء التفصيلية

### معلومات مفيدة للدعم:
- نوع المتصفح والإصدار
- نظام التشغيل
- طريقة تشغيل الملفات (خادم أم file://)
- رسائل الخطأ الكاملة من Console
- لقطة شاشة من Network tab

## ✅ التحقق من نجاح الحل

عند حل المشكلة، يجب أن ترى:
1. تحميل الصفحة بدون أخطاء في Console
2. ظهور واجهة تسجيل الدخول
3. إمكانية تسجيل الدخول بـ `admin` / `admin123`
4. الانتقال إلى لوحة التحكم بنجاح

## 🎯 نصائح للمطورين

### أفضل الممارسات:
1. استخدم دائماً خادم ويب للتطوير
2. فعل HTTPS للميزات المتقدمة
3. استخدم أدوات التطوير للتشخيص
4. اختبر على متصفحات مختلفة
5. احتفظ بنسخة احتياطية من الملفات

### تجنب هذه الأخطاء:
1. تشغيل الملفات من file://
2. تعديل الملفات أثناء تشغيل الخادم
3. تجاهل رسائل الخطأ في Console
4. عدم مسح Cache عند التحديث
5. استخدام مسارات مطلقة بدلاً من نسبية

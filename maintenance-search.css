/* Professional Maintenance Controls Styles */

/* Main container */
.maintenance-search-container {
    margin: 20px 0 30px;
    width: 100%;
    font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
}

/* Controls wrapper with shadow and rounded corners */
.maintenance-controls-wrapper {
    background-color: #ffffff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    transition: box-shadow 0.3s ease;
    max-width: 1200px;
    margin: 0 auto;
}

.maintenance-controls-wrapper:hover {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.12);
}

/* Main controls container */
.maintenance-controls {
    display: flex;
    justify-content: space-between;
    align-items: stretch;
    flex-wrap: wrap;
    padding: 20px;
    border-bottom: 1px solid #eaeaea;
    gap: 20px;
}

/* Control groups */
.control-group {
    display: flex;
    flex-direction: column;
    flex: 1;
    min-width: 200px;
    position: relative;
}

/* Control labels */
.control-label {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    color: #555;
    font-weight: 600;
    font-size: 14px;
}

.control-label i {
    margin-right: 8px;
    color: #0057ff;
    font-size: 16px;
}

/* Search box */
.search-box {
    display: flex;
    width: 100%;
}

.search-box input {
    flex: 1;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 4px 0 0 4px;
    font-size: 14px;
    transition: border-color 0.2s, box-shadow 0.2s;
}

.search-box input:focus {
    outline: none;
    border-color: #0057ff;
    box-shadow: 0 0 0 3px rgba(0, 87, 255, 0.1);
}

.search-box button {
    padding: 0 15px;
    background-color: #0057ff;
    color: white;
    border: none;
    border-radius: 0 4px 4px 0;
    cursor: pointer;
    transition: background-color 0.2s;
}

.search-box button:hover {
    background-color: #0046cc;
}

/* Filter box */
.filter-box {
    width: 100%;
}

.filter-box select {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    background-color: white;
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12'%3E%3Cpath fill='%23555555' d='M6 8.825L1.175 4 2.05 3.125 6 7.075 9.95 3.125 10.825 4z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 15px center;
    transition: border-color 0.2s, box-shadow 0.2s;
}

.filter-box select:focus {
    outline: none;
    border-color: #0057ff;
    box-shadow: 0 0 0 3px rgba(0, 87, 255, 0.1);
}

/* Export button */
.export-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 0.5rem 1rem;
    background-color: #00c853;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    font-size: 0.9rem;
    transition: background-color 0.2s, transform 0.1s;
    height: 36px;
    box-sizing: border-box;
    width: auto;
    min-height: auto;
}

.export-btn:hover {
    background-color: #00b248;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 200, 83, 0.3);
}

.export-btn:active {
    transform: translateY(1px);
}

.export-btn i {
    font-size: 0.9rem;
}

/* Active filters section */
.active-filters {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    padding: 12px 20px;
    background-color: #f8f9fa;
    gap: 10px;
}

.filter-label {
    color: #666;
    font-size: 13px;
    font-weight: 500;
}

.filter-tag {
    display: inline-flex;
    align-items: center;
    background-color: #e9f0ff;
    border: 1px solid #d0e0ff;
    border-radius: 50px;
    padding: 5px 12px;
    font-size: 13px;
    color: #0057ff;
    margin-right: 8px;
}

.filter-tag .filter-value {
    font-weight: 600;
    max-width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.filter-tag .clear-filter {
    background: none;
    border: none;
    color: #0057ff;
    cursor: pointer;
    margin-left: 8px;
    padding: 0;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    transition: background-color 0.2s;
}

.filter-tag .clear-filter:hover {
    background-color: rgba(0, 87, 255, 0.1);
}

#clear-all-filters {
    font-size: 12px;
    padding: 4px 10px;
    margin-left: auto;
    background: none;
    border: 1px solid #ddd;
    border-radius: 4px;
    color: #666;
    cursor: pointer;
    transition: background-color 0.2s, color 0.2s;
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

#clear-all-filters:hover {
    background-color: #f1f1f1;
    color: #333;
}

/* Responsive styles */
@media (max-width: 992px) {
    .maintenance-controls {
        padding: 15px;
        gap: 15px;
    }

    .control-group {
        min-width: 180px;
    }
}

@media (max-width: 768px) {
    .maintenance-controls {
        flex-direction: column;
        padding: 15px;
    }

    .control-group {
        width: 100%;
        min-width: 100%;
        margin-bottom: 15px;
    }

    .control-group:last-child {
        margin-bottom: 0;
    }

    .active-filters {
        padding: 10px 15px;
    }

    .filter-tag {
        margin-bottom: 5px;
    }

    #clear-all-filters {
        margin-left: 0;
        margin-top: 5px;
        width: 100%;
        justify-content: center;
    }
}

/* Dark mode support */
body.dark-mode .maintenance-controls-wrapper {
    background-color: #111b30;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

body.dark-mode .maintenance-controls {
    border-bottom: 1px solid #1d283a;
}

body.dark-mode .control-label {
    color: #cbd5e1;
}

body.dark-mode .control-label i {
    color: #4d84ff;
}

body.dark-mode .search-box input,
body.dark-mode .filter-box select {
    background-color: #1e293b;
    border-color: #1d283a;
    color: #f8fafc;
}

body.dark-mode .search-box input:focus,
body.dark-mode .filter-box select:focus {
    border-color: #4d84ff;
    box-shadow: 0 0 0 3px rgba(77, 132, 255, 0.2);
}

body.dark-mode .filter-box select {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12'%3E%3Cpath fill='%23cbd5e1' d='M6 8.825L1.175 4 2.05 3.125 6 7.075 9.95 3.125 10.825 4z'/%3E%3C/svg%3E");
}

body.dark-mode .active-filters {
    background-color: #0f172a;
}

body.dark-mode .filter-label {
    color: #cbd5e1;
}

body.dark-mode .filter-tag {
    background-color: #1e293b;
    border-color: #1d283a;
    color: #4d84ff;
}

body.dark-mode #clear-all-filters {
    border-color: #1d283a;
    color: #cbd5e1;
}

body.dark-mode #clear-all-filters:hover {
    background-color: #1e293b;
    color: #f8fafc;
}

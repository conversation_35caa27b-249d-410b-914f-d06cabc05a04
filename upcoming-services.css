/* Upcoming Services Table Styles */
.upcoming-services {
    margin: 1rem 0;
}

.upcoming-services .services-filter {
    margin-bottom: 10px;
    display: flex;
    gap: 10px;
    justify-content: flex-start;
}

.upcoming-services .filter-btn {
    padding: 8px 15px;
    border: none;
    border-radius: 5px;
    background-color: #f0f0f0;
    color: #333;
    cursor: pointer;
    font-size: 0.9em;
    transition: background-color 0.3s ease;
}

.upcoming-services .filter-btn.active,
.upcoming-services .filter-btn:hover {
    background-color: #e0e0e0;
}

/* Table Row Status Colors */
#upcoming-services-table tr.critical-row {
    background-color: rgba(239, 68, 68, 0.1);
}

#upcoming-services-table tr.warning-row {
    background-color: rgba(245, 158, 11, 0.1);
}

#upcoming-services-table tr.good-row {
    background-color: rgba(34, 197, 94, 0.1);
}

/* Service Type Colors */
#upcoming-services-table tr.maintenance-service {
    background-color: #e8f5e9;
}

#upcoming-services-table tr.tires-service {
    background-color: #fff3e0;
}

#upcoming-services-table tr.license-service {
    background-color: #e3f2fd;
}

/* Status Indicator */
.status-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.9rem;
}

.status-indicator i {
    font-size: 1rem;
}

.status-indicator.critical {
    background-color: rgba(239, 68, 68, 0.1);
    color: #ef4444;
}

.status-indicator.warning {
    background-color: rgba(245, 158, 11, 0.1);
    color: #f59e0b;
}

.status-indicator.good {
    background-color: rgba(34, 197, 94, 0.1);
    color: #22c55e;
}

.status-indicator.neutral {
    background-color: rgba(107, 114, 128, 0.1);
    color: #6b7280;
}

.status-indicator span {
    font-weight: 500;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-end;
}

/* Dark Mode Styles */
body.dark-mode .status-indicator.critical {
    background-color: rgba(239, 68, 68, 0.2);
}

body.dark-mode .status-indicator.warning {
    background-color: rgba(245, 158, 11, 0.2);
}

body.dark-mode .status-indicator.good {
    background-color: rgba(34, 197, 94, 0.2);
}

body.dark-mode .status-indicator.neutral {
    background-color: rgba(107, 114, 128, 0.2);
}

body.dark-mode #upcoming-services-table tr.critical-row {
    background-color: rgba(239, 68, 68, 0.15);
}

body.dark-mode #upcoming-services-table tr.warning-row {
    background-color: rgba(245, 158, 11, 0.15);
}

body.dark-mode #upcoming-services-table tr.good-row {
    background-color: rgba(34, 197, 94, 0.15);
}

/* Table Styles */
#upcoming-services-table {
    width: 100%;
    border-collapse: collapse;
    margin: 1rem 0;
}

#upcoming-services-table th,
#upcoming-services-table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

#upcoming-services-table th {
    font-weight: 600;
    background-color: var(--card-bg-color);
    color: var(--text-color-dark);
}

#upcoming-services-table tbody tr:hover {
    background-color: var(--hover-color);
}

/* Service Type Filter */
.service-type-filter {
    display: flex;
    gap: 0.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .upcoming-services .services-filter {
        flex-wrap: wrap;
    }

    #upcoming-services-table {
        font-size: 0.9rem;
    }

    .status-indicator {
        font-size: 0.8rem;
    }
}
/**
 * Store Hooks - خطافات المخزن
 * يوفر خطافات مخصصة للتفاعل مع مخزن التطبيق
 */

import appStore from './store.js';
import { selectors } from './selectors.js';
import { actions } from './actions.js';

/**
 * خطاف للاشتراك في جزء من الحالة
 */
export function useSelector(selector) {
    let currentValue = selector(appStore.getState());
    let subscribers = [];

    // الاشتراك في تغييرات الحالة
    const unsubscribe = appStore.subscribe(selector, (newValue) => {
        currentValue = newValue;
        // إشعار جميع المشتركين
        subscribers.forEach(callback => callback(newValue));
    });

    return {
        // الحصول على القيمة الحالية
        get value() {
            return currentValue;
        },

        // الاشتراك في التغييرات
        subscribe(callback) {
            subscribers.push(callback);
            // إرجاع دالة لإلغاء الاشتراك
            return () => {
                subscribers = subscribers.filter(cb => cb !== callback);
            };
        },

        // إلغاء الاشتراك الرئيسي
        unsubscribe
    };
}

/**
 * خطاف للحصول على الإجراءات
 */
export function useActions() {
    return actions;
}

/**
 * خطاف للحصول على المحددات
 */
export function useSelectors() {
    return selectors;
}

/**
 * خطاف للمستخدم الحالي
 */
export function useCurrentUser() {
    const userSelector = useSelector(selectors.user.getCurrentUser);
    const authSelector = useSelector(selectors.user.isAuthenticated);

    return {
        user: userSelector.value,
        isAuthenticated: authSelector.value,
        login: actions.user.login,
        logout: actions.user.logout,
        updateUser: actions.user.updateUser,
        subscribe: userSelector.subscribe,
        unsubscribe: () => {
            userSelector.unsubscribe();
            authSelector.unsubscribe();
        }
    };
}

/**
 * خطاف لحالة التطبيق
 */
export function useAppState() {
    const loadingSelector = useSelector(selectors.app.isLoading);
    const pageSelector = useSelector(selectors.app.getCurrentPage);
    const notificationsSelector = useSelector(selectors.app.getNotifications);

    return {
        isLoading: loadingSelector.value,
        currentPage: pageSelector.value,
        notifications: notificationsSelector.value,
        setLoading: actions.app.setLoading,
        setCurrentPage: actions.app.setCurrentPage,
        addNotification: actions.app.addNotification,
        removeNotification: actions.app.removeNotification,
        subscribe: (callback) => {
            const unsubscribers = [
                loadingSelector.subscribe(callback),
                pageSelector.subscribe(callback),
                notificationsSelector.subscribe(callback)
            ];
            return () => unsubscribers.forEach(unsub => unsub());
        },
        unsubscribe: () => {
            loadingSelector.unsubscribe();
            pageSelector.unsubscribe();
            notificationsSelector.unsubscribe();
        }
    };
}

/**
 * خطاف للمركبات
 */
export function useVehicles() {
    const vehiclesSelector = useSelector(selectors.data.getAllVehicles);
    const statsSelector = useSelector(selectors.stats.getVehicleStats);

    return {
        vehicles: vehiclesSelector.value,
        stats: statsSelector.value,
        setVehicles: actions.data.setVehicles,
        addVehicle: actions.data.addVehicle,
        updateVehicle: actions.data.updateVehicle,
        removeVehicle: actions.data.removeVehicle,
        getById: (id) => selectors.data.getVehicleById(id)(appStore.getState()),
        getByBranch: (branch) => selectors.data.getVehiclesByBranch(branch)(appStore.getState()),
        getActive: () => selectors.data.getActiveVehicles(appStore.getState()),
        getInMaintenance: () => selectors.data.getVehiclesInMaintenance(appStore.getState()),
        search: (term) => selectors.search.searchVehicles(term)(appStore.getState()),
        filter: (filters) => selectors.search.filterVehicles(filters)(appStore.getState()),
        subscribe: vehiclesSelector.subscribe,
        unsubscribe: () => {
            vehiclesSelector.unsubscribe();
            statsSelector.unsubscribe();
        }
    };
}

/**
 * خطاف للسائقين
 */
export function useDrivers() {
    const driversSelector = useSelector(selectors.data.getAllDrivers);
    const statsSelector = useSelector(selectors.stats.getDriverStats);

    return {
        drivers: driversSelector.value,
        stats: statsSelector.value,
        setDrivers: actions.data.setDrivers,
        addDriver: actions.data.addDriver,
        updateDriver: actions.data.updateDriver,
        getById: (id) => selectors.data.getDriverById(id)(appStore.getState()),
        getActive: () => selectors.data.getActiveDrivers(appStore.getState()),
        search: (term) => selectors.search.searchDrivers(term)(appStore.getState()),
        subscribe: driversSelector.subscribe,
        unsubscribe: () => {
            driversSelector.unsubscribe();
            statsSelector.unsubscribe();
        }
    };
}

/**
 * خطاف للصيانة
 */
export function useMaintenance() {
    const maintenanceSelector = useSelector(selectors.data.getMaintenanceRecords);
    const statsSelector = useSelector(selectors.stats.getMaintenanceStats);

    return {
        records: maintenanceSelector.value,
        stats: statsSelector.value,
        setRecords: actions.data.setMaintenanceRecords,
        addRecord: actions.data.addMaintenanceRecord,
        getByVehicle: (vehicleId) => selectors.data.getMaintenanceByVehicle(vehicleId)(appStore.getState()),
        subscribe: maintenanceSelector.subscribe,
        unsubscribe: () => {
            maintenanceSelector.unsubscribe();
            statsSelector.unsubscribe();
        }
    };
}

/**
 * خطاف للوقود
 */
export function useFuel() {
    const fuelSelector = useSelector(selectors.data.getFuelRecords);
    const statsSelector = useSelector(selectors.stats.getFuelStats);

    return {
        records: fuelSelector.value,
        stats: statsSelector.value,
        setRecords: actions.data.setFuelRecords,
        addRecord: actions.data.addFuelRecord,
        getByVehicle: (vehicleId) => selectors.data.getFuelByVehicle(vehicleId)(appStore.getState()),
        subscribe: fuelSelector.subscribe,
        unsubscribe: () => {
            fuelSelector.unsubscribe();
            statsSelector.unsubscribe();
        }
    };
}

/**
 * خطاف للنوافذ المنبثقة
 */
export function useModal(modalId) {
    const modalSelector = useSelector(selectors.ui.getModalState(modalId));

    return {
        isOpen: modalSelector.value.isOpen,
        data: modalSelector.value.data,
        open: (data = null) => actions.ui.setModal(modalId, true, data),
        close: () => actions.ui.setModal(modalId, false, null),
        toggle: (data = null) => {
            const currentState = modalSelector.value;
            actions.ui.setModal(modalId, !currentState.isOpen, data);
        },
        subscribe: modalSelector.subscribe,
        unsubscribe: modalSelector.unsubscribe
    };
}

/**
 * خطاف للنماذج
 */
export function useForm(formId) {
    const formSelector = useSelector(selectors.ui.getFormState(formId));

    return {
        state: formSelector.value,
        setState: (state) => actions.ui.setFormState(formId, state),
        updateField: (field, value) => {
            const currentState = formSelector.value;
            actions.ui.setFormState(formId, {
                ...currentState,
                [field]: value
            });
        },
        reset: () => actions.ui.setFormState(formId, {}),
        subscribe: formSelector.subscribe,
        unsubscribe: formSelector.unsubscribe
    };
}

/**
 * خطاف للجداول
 */
export function useTable(tableId) {
    const tableSelector = useSelector(selectors.ui.getTableState(tableId));

    return {
        state: tableSelector.value,
        setState: (state) => actions.ui.setTableState(tableId, state),
        setPage: (page) => {
            const currentState = tableSelector.value;
            actions.ui.setTableState(tableId, {
                ...currentState,
                currentPage: page
            });
        },
        setSort: (field, direction) => {
            const currentState = tableSelector.value;
            actions.ui.setTableState(tableId, {
                ...currentState,
                sortField: field,
                sortDirection: direction
            });
        },
        setPageSize: (size) => {
            const currentState = tableSelector.value;
            actions.ui.setTableState(tableId, {
                ...currentState,
                pageSize: size,
                currentPage: 1 // إعادة تعيين للصفحة الأولى
            });
        },
        subscribe: tableSelector.subscribe,
        unsubscribe: tableSelector.unsubscribe
    };
}

/**
 * خطاف للفلاتر
 */
export function useFilters(section) {
    const filtersSelector = useSelector(selectors.ui.getFilters(section));

    return {
        filters: filtersSelector.value,
        setFilters: (filters) => actions.ui.setFilters(section, filters),
        updateFilter: (key, value) => {
            const currentFilters = filtersSelector.value;
            actions.ui.setFilters(section, {
                ...currentFilters,
                [key]: value
            });
        },
        removeFilter: (key) => {
            const currentFilters = filtersSelector.value;
            const newFilters = { ...currentFilters };
            delete newFilters[key];
            actions.ui.setFilters(section, newFilters);
        },
        clearFilters: () => actions.ui.clearFilters(section),
        hasActiveFilters: selectors.ui.hasActiveFilters(section)(appStore.getState()),
        subscribe: filtersSelector.subscribe,
        unsubscribe: filtersSelector.unsubscribe
    };
}

/**
 * خطاف للإحصائيات
 */
export function useStats() {
    const vehicleStatsSelector = useSelector(selectors.stats.getVehicleStats);
    const driverStatsSelector = useSelector(selectors.stats.getDriverStats);
    const maintenanceStatsSelector = useSelector(selectors.stats.getMaintenanceStats);
    const fuelStatsSelector = useSelector(selectors.stats.getFuelStats);

    return {
        vehicles: vehicleStatsSelector.value,
        drivers: driverStatsSelector.value,
        maintenance: maintenanceStatsSelector.value,
        fuel: fuelStatsSelector.value,
        subscribe: (callback) => {
            const unsubscribers = [
                vehicleStatsSelector.subscribe(callback),
                driverStatsSelector.subscribe(callback),
                maintenanceStatsSelector.subscribe(callback),
                fuelStatsSelector.subscribe(callback)
            ];
            return () => unsubscribers.forEach(unsub => unsub());
        },
        unsubscribe: () => {
            vehicleStatsSelector.unsubscribe();
            driverStatsSelector.unsubscribe();
            maintenanceStatsSelector.unsubscribe();
            fuelStatsSelector.unsubscribe();
        }
    };
}

/**
 * خطاف للبحث
 */
export function useSearch() {
    return {
        searchVehicles: (term) => selectors.search.searchVehicles(term)(appStore.getState()),
        searchDrivers: (term) => selectors.search.searchDrivers(term)(appStore.getState()),
        filterVehicles: (filters) => selectors.search.filterVehicles(filters)(appStore.getState())
    };
}

/**
 * خطاف شامل للمخزن
 */
export function useStore() {
    return {
        getState: () => appStore.getState(),
        setState: (updates, actionType) => appStore.setState(updates, actionType),
        subscribe: (selector, callback) => appStore.subscribe(selector, callback),
        actions,
        selectors,
        hooks: {
            useSelector,
            useCurrentUser,
            useAppState,
            useVehicles,
            useDrivers,
            useMaintenance,
            useFuel,
            useModal,
            useForm,
            useTable,
            useFilters,
            useStats,
            useSearch
        }
    };
}

// تصدير جميع الخطافات
export const hooks = {
    useSelector,
    useActions,
    useSelectors,
    useCurrentUser,
    useAppState,
    useVehicles,
    useDrivers,
    useMaintenance,
    useFuel,
    useModal,
    useForm,
    useTable,
    useFilters,
    useStats,
    useSearch,
    useStore
};

// تصدير افتراضي
export default hooks;

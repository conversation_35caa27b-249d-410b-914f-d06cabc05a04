/**
 * Vehicles Page - صفحة إدارة المركبات
 * يوفر واجهة شاملة لإدارة المركبات وعرض تفاصيلها
 */

import { getVehicles, addVehicle, updateVehicle, deleteVehicle, getDrivers } from '../../services/dataService.js';
import { showNotification, formatNumber, getBranchName, isValidEmail } from '../../utils/utility.js';
import { VEHICLE_STATUS, VEHICLE_STATUS_LABELS, VEHICLE_TYPES, VEHICLE_TYPE_LABELS, FUEL_TYPES, FUEL_TYPE_LABELS, BRANCHES, BRANCH_LABELS } from '../../utils/constants.js';
import { hasPermission, canPerformAction } from '../../utils/permissions.js';
import { Modal, createModal } from '../../components/Modal.js';
import { showSpinner, hideSpinner } from '../../components/Spinner.js';
import appStore, { storeSelectors } from '../../store/store.js';

// متغيرات الحالة
let vehiclesData = [];
let driversData = [];
let filteredVehicles = [];
let currentFilters = {
    search: '',
    status: '',
    branch: '',
    type: ''
};

// إعدادات الجدول
let tableSettings = {
    sortBy: 'License Plate',
    sortDirection: 'asc',
    currentPage: 1,
    itemsPerPage: 10,
    visibleColumns: getDefaultVisibleColumns()
};

/**
 * تهيئة صفحة المركبات
 */
export async function initializeVehicles() {
    try {
        console.log('Initializing vehicles page...');
        
        // إنشاء هيكل الصفحة
        createVehiclesPageStructure();
        
        // تحميل البيانات
        await loadVehiclesData();
        
        // عرض البيانات
        renderVehiclesTable();
        
        // ربط الأحداث
        bindVehiclesEvents();
        
        console.log('Vehicles page initialized successfully');
        
    } catch (error) {
        console.error('Error initializing vehicles page:', error);
        showNotification('فشل في تحميل صفحة المركبات', 'error');
    }
}

/**
 * إنشاء هيكل صفحة المركبات
 */
function createVehiclesPageStructure() {
    const vehiclesPage = document.getElementById('vehicles-page');
    if (!vehiclesPage) return;
    
    vehiclesPage.innerHTML = `
        <div class="page-header">
            <h1 class="page-title">إدارة المركبات</h1>
            <div class="page-actions">
                <button id="add-vehicle-btn" class="btn btn-primary">
                    <i class="fas fa-plus"></i> إضافة مركبة
                </button>
                <button id="export-vehicles-btn" class="btn btn-success">
                    <i class="fas fa-download"></i> تصدير
                </button>
                <button id="manage-columns-btn" class="btn btn-secondary">
                    <i class="fas fa-columns"></i> إدارة الأعمدة
                </button>
            </div>
        </div>
        
        <!-- فلاتر البحث -->
        <div class="filter-bar">
            <div class="filter-group">
                <label for="vehicles-search">البحث:</label>
                <input type="text" id="vehicles-search" class="form-control" placeholder="البحث في المركبات...">
            </div>
            <div class="filter-group">
                <label for="status-filter">الحالة:</label>
                <select id="status-filter" class="form-control">
                    <option value="">جميع الحالات</option>
                    ${Object.entries(VEHICLE_STATUS_LABELS).map(([key, label]) => 
                        `<option value="${key}">${label}</option>`
                    ).join('')}
                </select>
            </div>
            <div class="filter-group">
                <label for="branch-filter">الفرع:</label>
                <select id="branch-filter" class="form-control">
                    <option value="">جميع الفروع</option>
                    ${Object.entries(BRANCH_LABELS).map(([key, label]) => 
                        `<option value="${key}">${label}</option>`
                    ).join('')}
                </select>
            </div>
            <div class="filter-group">
                <label for="type-filter">النوع:</label>
                <select id="type-filter" class="form-control">
                    <option value="">جميع الأنواع</option>
                    ${Object.entries(VEHICLE_TYPE_LABELS).map(([key, label]) => 
                        `<option value="${key}">${label}</option>`
                    ).join('')}
                </select>
            </div>
            <div class="filter-actions">
                <button id="clear-filters-btn" class="btn btn-secondary">
                    <i class="fas fa-times"></i> مسح الفلاتر
                </button>
            </div>
        </div>
        
        <!-- إحصائيات سريعة -->
        <div class="quick-stats">
            <div class="stat-item">
                <span class="stat-label">إجمالي المركبات:</span>
                <span id="total-vehicles-stat" class="stat-value">0</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">المركبات النشطة:</span>
                <span id="active-vehicles-stat" class="stat-value">0</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">في الصيانة:</span>
                <span id="maintenance-vehicles-stat" class="stat-value">0</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">متوقفة:</span>
                <span id="inactive-vehicles-stat" class="stat-value">0</span>
            </div>
        </div>
        
        <!-- جدول المركبات -->
        <div class="section">
            <div class="section-body">
                <div class="table-responsive">
                    <table id="vehicles-table" class="table">
                        <thead>
                            <tr id="vehicles-table-header">
                                <!-- سيتم إنشاء الهيدر ديناميكياً -->
                            </tr>
                        </thead>
                        <tbody id="vehicles-table-body">
                            <!-- سيتم إنشاء الصفوف ديناميكياً -->
                        </tbody>
                    </table>
                </div>
                
                <!-- التنقل بين الصفحات -->
                <div class="pagination-container">
                    <div class="pagination-info">
                        <span id="pagination-info-text">عرض 0 من 0 مركبة</span>
                    </div>
                    <div class="pagination-controls">
                        <button id="prev-page-btn" class="btn btn-secondary" disabled>
                            <i class="fas fa-chevron-right"></i> السابق
                        </button>
                        <span id="page-numbers"></span>
                        <button id="next-page-btn" class="btn btn-secondary" disabled>
                            التالي <i class="fas fa-chevron-left"></i>
                        </button>
                    </div>
                    <div class="page-size-selector">
                        <label for="page-size-select">عرض:</label>
                        <select id="page-size-select" class="form-control">
                            <option value="10">10</option>
                            <option value="25">25</option>
                            <option value="50">50</option>
                            <option value="100">100</option>
                        </select>
                        <span>مركبة</span>
                    </div>
                </div>
            </div>
        </div>
    `;
}

/**
 * تحميل بيانات المركبات
 */
async function loadVehiclesData() {
    try {
        showSpinner('جاري تحميل المركبات...');
        
        // تحميل المركبات والسائقين
        const [vehicles, drivers] = await Promise.all([
            getVehicles(),
            getDrivers()
        ]);
        
        vehiclesData = vehicles || [];
        driversData = drivers || [];
        filteredVehicles = [...vehiclesData];
        
        console.log('Vehicles data loaded:', {
            vehicles: vehiclesData.length,
            drivers: driversData.length
        });
        
    } catch (error) {
        console.error('Error loading vehicles data:', error);
        showNotification('فشل في تحميل بيانات المركبات', 'error');
        vehiclesData = [];
        driversData = [];
        filteredVehicles = [];
    } finally {
        hideSpinner();
    }
}

/**
 * عرض جدول المركبات
 */
function renderVehiclesTable() {
    try {
        // تطبيق الفلاتر
        applyFilters();
        
        // تطبيق الترتيب
        applySorting();
        
        // عرض الهيدر
        renderTableHeader();
        
        // عرض البيانات
        renderTableBody();
        
        // تحديث التنقل
        updatePagination();
        
        // تحديث الإحصائيات
        updateQuickStats();
        
    } catch (error) {
        console.error('Error rendering vehicles table:', error);
        showNotification('فشل في عرض جدول المركبات', 'error');
    }
}

/**
 * عرض هيدر الجدول
 */
function renderTableHeader() {
    const headerRow = document.getElementById('vehicles-table-header');
    if (!headerRow) return;
    
    const columns = getTableColumns();
    
    headerRow.innerHTML = columns.map(column => {
        if (!tableSettings.visibleColumns[column.key]) return '';
        
        const sortIcon = tableSettings.sortBy === column.key 
            ? (tableSettings.sortDirection === 'asc' ? 'fa-sort-up' : 'fa-sort-down')
            : 'fa-sort';
            
        return `
            <th class="sortable" data-column="${column.key}">
                ${column.label}
                <i class="fas ${sortIcon} sort-icon"></i>
            </th>
        `;
    }).join('');
}

/**
 * عرض محتوى الجدول
 */
function renderTableBody() {
    const tbody = document.getElementById('vehicles-table-body');
    if (!tbody) return;
    
    const startIndex = (tableSettings.currentPage - 1) * tableSettings.itemsPerPage;
    const endIndex = startIndex + tableSettings.itemsPerPage;
    const pageVehicles = filteredVehicles.slice(startIndex, endIndex);
    
    if (pageVehicles.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="100%" class="text-center">
                    <div class="empty-state">
                        <i class="fas fa-car"></i>
                        <h3>لا توجد مركبات</h3>
                        <p>لم يتم العثور على مركبات تطابق المعايير المحددة</p>
                    </div>
                </td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = pageVehicles.map(vehicle => {
        return createVehicleRow(vehicle);
    }).join('');
}

/**
 * إنشاء صف مركبة
 */
function createVehicleRow(vehicle) {
    const columns = getTableColumns();
    
    return `
        <tr data-vehicle-id="${vehicle['Vehicle ID'] || vehicle.id}">
            ${columns.map(column => {
                if (!tableSettings.visibleColumns[column.key]) return '';
                return `<td>${getColumnValue(vehicle, column.key)}</td>`;
            }).join('')}
        </tr>
    `;
}

/**
 * الحصول على قيمة العمود
 */
function getColumnValue(vehicle, columnKey) {
    switch (columnKey) {
        case 'licensePlate':
            return vehicle['License Plate'] || '-';
            
        case 'vehicleType':
            const type = vehicle['Vehicle Type'];
            return VEHICLE_TYPE_LABELS[type] || type || '-';
            
        case 'currentKm':
            return formatNumber(vehicle['Current Km']) || '0';
            
        case 'vehicleStatus':
            const status = vehicle['Vehicle Status'];
            const statusLabel = VEHICLE_STATUS_LABELS[status] || status || 'غير محدد';
            return `<span class="status-indicator ${status?.toLowerCase()}">${statusLabel}</span>`;
            
        case 'nextMaintenanceKm':
            return formatNumber(vehicle['Next Maintenance Km']) || '-';
            
        case 'branch':
            return getBranchName(vehicle) || '-';
            
        case 'driverName':
            return vehicle['Driver Name'] || '-';
            
        case 'actions':
            return createActionButtons(vehicle);
            
        default:
            return vehicle[columnKey] || '-';
    }
}

/**
 * إنشاء أزرار الإجراءات
 */
function createActionButtons(vehicle) {
    const vehicleId = vehicle['Vehicle ID'] || vehicle.id;
    const currentUser = appStore.getState().user.currentUser;
    
    let buttons = [];
    
    // زر عرض التفاصيل
    buttons.push(`
        <button class="btn btn-sm btn-info view-vehicle-btn" data-vehicle-id="${vehicleId}" title="عرض التفاصيل">
            <i class="fas fa-eye"></i>
        </button>
    `);
    
    // زر التعديل (حسب الصلاحيات)
    if (canPerformAction(currentUser?.role, 'editVehicle')) {
        buttons.push(`
            <button class="btn btn-sm btn-warning edit-vehicle-btn" data-vehicle-id="${vehicleId}" title="تعديل">
                <i class="fas fa-edit"></i>
            </button>
        `);
    }
    
    // زر الحذف (حسب الصلاحيات)
    if (canPerformAction(currentUser?.role, 'deleteVehicle')) {
        buttons.push(`
            <button class="btn btn-sm btn-danger delete-vehicle-btn" data-vehicle-id="${vehicleId}" title="حذف">
                <i class="fas fa-trash"></i>
            </button>
        `);
    }
    
    return `<div class="action-buttons">${buttons.join('')}</div>`;
}

/**
 * تطبيق الفلاتر
 */
function applyFilters() {
    filteredVehicles = vehiclesData.filter(vehicle => {
        // فلتر البحث
        if (currentFilters.search) {
            const searchTerm = currentFilters.search.toLowerCase();
            const searchableFields = [
                vehicle['License Plate'],
                vehicle['Vehicle Type'],
                vehicle['Driver Name'],
                getBranchName(vehicle),
                vehicle['Vehicle ID']
            ];
            
            const matches = searchableFields.some(field => 
                field?.toString().toLowerCase().includes(searchTerm)
            );
            
            if (!matches) return false;
        }
        
        // فلتر الحالة
        if (currentFilters.status && vehicle['Vehicle Status'] !== currentFilters.status) {
            return false;
        }
        
        // فلتر الفرع
        if (currentFilters.branch && getBranchName(vehicle) !== currentFilters.branch) {
            return false;
        }
        
        // فلتر النوع
        if (currentFilters.type && vehicle['Vehicle Type'] !== currentFilters.type) {
            return false;
        }
        
        return true;
    });
    
    // إعادة تعيين الصفحة الحالية
    tableSettings.currentPage = 1;
}

/**
 * ربط الأحداث
 */
function bindVehiclesEvents() {
    // زر إضافة مركبة
    const addBtn = document.getElementById('add-vehicle-btn');
    if (addBtn) {
        addBtn.addEventListener('click', () => openAddVehicleModal());
    }
    
    // فلاتر البحث
    const searchInput = document.getElementById('vehicles-search');
    if (searchInput) {
        searchInput.addEventListener('input', (e) => {
            currentFilters.search = e.target.value;
            renderVehiclesTable();
        });
    }
    
    // فلاتر الحالة والفرع والنوع
    ['status-filter', 'branch-filter', 'type-filter'].forEach(filterId => {
        const filter = document.getElementById(filterId);
        if (filter) {
            filter.addEventListener('change', (e) => {
                const filterType = filterId.replace('-filter', '');
                currentFilters[filterType] = e.target.value;
                renderVehiclesTable();
            });
        }
    });
    
    // مسح الفلاتر
    const clearFiltersBtn = document.getElementById('clear-filters-btn');
    if (clearFiltersBtn) {
        clearFiltersBtn.addEventListener('click', clearAllFilters);
    }
    
    // أحداث الجدول
    bindTableEvents();
}

/**
 * الحصول على الأعمدة الافتراضية المرئية
 */
function getDefaultVisibleColumns() {
    return {
        licensePlate: true,
        vehicleType: true,
        currentKm: true,
        vehicleStatus: true,
        nextMaintenanceKm: true,
        branch: true,
        driverName: true,
        actions: true
    };
}

/**
 * الحصول على تعريف الأعمدة
 */
function getTableColumns() {
    return [
        { key: 'licensePlate', label: 'رقم اللوحة' },
        { key: 'vehicleType', label: 'نوع المركبة' },
        { key: 'currentKm', label: 'الكيلومترات الحالية' },
        { key: 'vehicleStatus', label: 'الحالة' },
        { key: 'nextMaintenanceKm', label: 'الصيانة القادمة' },
        { key: 'branch', label: 'الفرع' },
        { key: 'driverName', label: 'السائق' },
        { key: 'actions', label: 'الإجراءات' }
    ];
}

/**
 * دالة الرندر الرئيسية للصفحة
 */
export async function render(container) {
    if (container) {
        container.innerHTML = '';
        await initializeVehicles();
    } else {
        await initializeVehicles();
    }
}

// تصدير الدوال للاستخدام الخارجي
export {
    loadVehiclesData,
    renderVehiclesTable,
    applyFilters
};

export default {
    render,
    initializeVehicles,
    loadVehiclesData,
    renderVehiclesTable
};

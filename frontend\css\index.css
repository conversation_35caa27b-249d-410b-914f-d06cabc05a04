/**
 * Main CSS Index - الفهرس الرئيسي لملفات CSS
 * يستورد جميع ملفات CSS بالترتيب الصحيح
 */

/* ===== استيراد الخطوط ===== */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap');

/* ===== الملفات الأساسية ===== */
@import url('./main.css');

/* ===== التخطيط ===== */
@import url('./layout.css');

/* ===== المكونات ===== */
@import url('./components.css');

/* ===== الصفحات ===== */
@import url('./pages.css');

/* ===== الأدوات المساعدة ===== */
@import url('./utilities.css');

/* ===== تخصيصات إضافية ===== */

/* تحسينات للطباعة */
@media print {
    .no-print {
        display: none !important;
    }
    
    .print-only {
        display: block !important;
    }
    
    body {
        font-size: 12pt;
        line-height: 1.4;
        color: #000;
        background: #fff;
    }
    
    .sidebar,
    .header,
    .modal-overlay,
    .notification {
        display: none !important;
    }
    
    .main-wrapper {
        margin: 0 !important;
    }
    
    .page-content {
        padding: 0 !important;
    }
    
    .card {
        border: 1px solid #000;
        box-shadow: none;
        page-break-inside: avoid;
    }
    
    .table {
        border-collapse: collapse;
    }
    
    .table th,
    .table td {
        border: 1px solid #000;
        padding: 8pt;
    }
    
    .btn {
        display: none !important;
    }
    
    a[href]:after {
        content: " (" attr(href) ")";
        font-size: 10pt;
        color: #666;
    }
    
    .page-break {
        page-break-before: always;
    }
}

/* تحسينات للشاشات عالية الدقة */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .icon,
    .logo {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* تحسينات للوضع المظلم */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-primary: #1e293b;
        --bg-secondary: #334155;
        --bg-tertiary: #475569;
        --text-primary: #f1f5f9;
        --text-secondary: #cbd5e1;
        --text-muted: #94a3b8;
        --border-color: #475569;
        --border-dark: #64748b;
    }
    
    .auto-dark-mode {
        background-color: var(--bg-primary);
        color: var(--text-primary);
    }
}

/* تحسينات للحركة المخفضة */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
    
    .fade-in,
    .slide-in-up,
    .slide-in-down,
    .slide-in-left,
    .slide-in-right,
    .zoom-in,
    .bounce,
    .pulse,
    .shake {
        animation: none !important;
    }
}

/* تحسينات للتباين العالي */
@media (prefers-contrast: high) {
    :root {
        --border-color: #000;
        --text-secondary: #000;
        --text-muted: #333;
    }
    
    .btn {
        border-width: 2px;
    }
    
    .card {
        border-width: 2px;
    }
    
    .form-control {
        border-width: 2px;
    }
    
    .form-control:focus {
        border-width: 3px;
    }
}

/* تحسينات لإمكانية الوصول */
.sr-only {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    padding: 0 !important;
    margin: -1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    white-space: nowrap !important;
    border: 0 !important;
}

.sr-only-focusable:focus {
    position: static !important;
    width: auto !important;
    height: auto !important;
    padding: inherit !important;
    margin: inherit !important;
    overflow: visible !important;
    clip: auto !important;
    white-space: normal !important;
}

/* تحسين التركيز للوحة المفاتيح */
.focus-visible:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* إخفاء outline للماوس */
.focus-visible:focus:not(.focus-visible) {
    outline: none;
}

/* تحسينات للنصوص العربية */
.arabic-text {
    font-family: 'Noto Sans Arabic', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    direction: rtl;
    text-align: right;
}

.english-text {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    direction: ltr;
    text-align: left;
}

/* تحسينات للأرقام العربية */
.arabic-numbers {
    font-feature-settings: "lnum" 1;
}

.english-numbers {
    font-feature-settings: "tnum" 1;
}

/* تحسينات للتمرير */
.smooth-scroll {
    scroll-behavior: smooth;
}

.custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: var(--border-color) transparent;
}

.custom-scrollbar::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: var(--border-dark);
}

/* تحسينات للتحديد */
::selection {
    background: rgba(37, 99, 235, 0.2);
    color: var(--text-primary);
}

::-moz-selection {
    background: rgba(37, 99, 235, 0.2);
    color: var(--text-primary);
}

/* تحسينات للصور */
img {
    max-width: 100%;
    height: auto;
}

.img-fluid {
    max-width: 100%;
    height: auto;
}

.img-thumbnail {
    padding: 0.25rem;
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    max-width: 100%;
    height: auto;
}

/* تحسينات للفيديو */
.embed-responsive {
    position: relative;
    display: block;
    width: 100%;
    padding: 0;
    overflow: hidden;
}

.embed-responsive::before {
    display: block;
    content: "";
}

.embed-responsive-16by9::before {
    padding-top: 56.25%;
}

.embed-responsive-4by3::before {
    padding-top: 75%;
}

.embed-responsive-1by1::before {
    padding-top: 100%;
}

.embed-responsive iframe,
.embed-responsive embed,
.embed-responsive object,
.embed-responsive video {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 0;
}

/* تحسينات للطباعة الخاصة */
.page-break-before {
    page-break-before: always;
}

.page-break-after {
    page-break-after: always;
}

.page-break-inside-avoid {
    page-break-inside: avoid;
}

/* تحسينات للأداء */
.will-change-transform {
    will-change: transform;
}

.will-change-opacity {
    will-change: opacity;
}

.will-change-scroll {
    will-change: scroll-position;
}

/* تحسينات للتفاعل */
.interactive {
    cursor: pointer;
    transition: all var(--transition-fast);
}

.interactive:hover {
    transform: translateY(-1px);
}

.interactive:active {
    transform: translateY(0);
}

/* تحسينات للحالات الخاصة */
.loading {
    pointer-events: none;
    opacity: 0.6;
}

.disabled {
    pointer-events: none;
    opacity: 0.5;
    cursor: not-allowed;
}

.readonly {
    background-color: var(--bg-secondary);
    cursor: default;
}

/* تحسينات للرسائل */
.alert {
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-md);
    border: 1px solid transparent;
    border-radius: var(--radius-md);
}

.alert-primary {
    color: var(--primary-color);
    background-color: rgba(37, 99, 235, 0.1);
    border-color: rgba(37, 99, 235, 0.2);
}

.alert-success {
    color: var(--success-color);
    background-color: rgba(16, 185, 129, 0.1);
    border-color: rgba(16, 185, 129, 0.2);
}

.alert-warning {
    color: var(--warning-color);
    background-color: rgba(245, 158, 11, 0.1);
    border-color: rgba(245, 158, 11, 0.2);
}

.alert-danger {
    color: var(--error-color);
    background-color: rgba(239, 68, 68, 0.1);
    border-color: rgba(239, 68, 68, 0.2);
}

.alert-info {
    color: var(--info-color);
    background-color: rgba(59, 130, 246, 0.1);
    border-color: rgba(59, 130, 246, 0.2);
}

/* تحسينات للشارات */
.badge {
    display: inline-block;
    padding: 0.25em 0.6em;
    font-size: 0.75em;
    font-weight: 700;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 0.375rem;
}

.badge-primary {
    color: var(--text-light);
    background-color: var(--primary-color);
}

.badge-secondary {
    color: var(--text-primary);
    background-color: var(--bg-secondary);
}

.badge-success {
    color: var(--text-light);
    background-color: var(--success-color);
}

.badge-warning {
    color: var(--text-primary);
    background-color: var(--warning-color);
}

.badge-danger {
    color: var(--text-light);
    background-color: var(--error-color);
}

.badge-info {
    color: var(--text-light);
    background-color: var(--info-color);
}

/* تحسينات للتقدم */
.progress {
    display: flex;
    height: 1rem;
    overflow: hidden;
    background-color: var(--bg-secondary);
    border-radius: var(--radius-md);
}

.progress-bar {
    display: flex;
    flex-direction: column;
    justify-content: center;
    overflow: hidden;
    color: var(--text-light);
    text-align: center;
    white-space: nowrap;
    background-color: var(--primary-color);
    transition: width var(--transition-normal);
}

/* تحسينات نهائية */
.visually-hidden {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    padding: 0 !important;
    margin: -1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    white-space: nowrap !important;
    border: 0 !important;
}

.stretched-link::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1;
    content: "";
}

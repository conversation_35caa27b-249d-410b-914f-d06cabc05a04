/**
 * Maintenance Page - صفحة إدارة الصيانة
 * يوفر واجهة شاملة لإدارة سجلات الصيانة والخدمات القادمة
 */

import { getMaintenanceRecords, addMaintenanceRecord, getVehicles } from '../../services/dataService.js';
import { showNotification, formatDate, formatNumber, getBranchName } from '../../utils/utility.js';
import { SERVICE_TYPES, SERVICE_TYPE_LABELS, SERVICE_PRIORITY, SERVICE_PRIORITY_LABELS } from '../../utils/constants.js';
import { getUpcomingServices, filterServices, formatServiceRemaining, getServiceTypeLabel } from '../../utils/upcoming-services.js';
import { hasPermission, canPerformAction } from '../../utils/permissions.js';
import { Modal, createModal } from '../../components/Modal.js';
import { showSpinner, hideSpinner } from '../../components/Spinner.js';
import appStore, { storeSelectors } from '../../store/store.js';

// متغيرات الحالة
let maintenanceData = [];
let vehiclesData = [];
let upcomingServices = [];
let currentFilters = {
    search: '',
    serviceType: '',
    priority: '',
    dateFrom: '',
    dateTo: ''
};

// إعدادات الجدول
let tableSettings = {
    sortBy: 'serviceDate',
    sortDirection: 'desc',
    currentPage: 1,
    itemsPerPage: 10
};

/**
 * تهيئة صفحة الصيانة
 */
export async function initializeMaintenance() {
    try {
        console.log('Initializing maintenance page...');
        
        // إنشاء هيكل الصفحة
        createMaintenancePageStructure();
        
        // تحميل البيانات
        await loadMaintenanceData();
        
        // عرض البيانات
        renderMaintenancePage();
        
        // ربط الأحداث
        bindMaintenanceEvents();
        
        console.log('Maintenance page initialized successfully');
        
    } catch (error) {
        console.error('Error initializing maintenance page:', error);
        showNotification('فشل في تحميل صفحة الصيانة', 'error');
    }
}

/**
 * إنشاء هيكل صفحة الصيانة
 */
function createMaintenancePageStructure() {
    const maintenancePage = document.getElementById('maintenance-page');
    if (!maintenancePage) return;
    
    maintenancePage.innerHTML = `
        <div class="page-header">
            <h1 class="page-title">إدارة الصيانة</h1>
            <div class="page-actions">
                <button id="add-maintenance-btn" class="btn btn-primary">
                    <i class="fas fa-plus"></i> إضافة سجل صيانة
                </button>
                <button id="export-maintenance-btn" class="btn btn-success">
                    <i class="fas fa-download"></i> تصدير
                </button>
            </div>
        </div>
        
        <!-- فلاتر البحث -->
        <div class="filter-bar">
            <div class="filter-group">
                <label for="maintenance-search">البحث:</label>
                <input type="text" id="maintenance-search" class="form-control" placeholder="البحث في سجلات الصيانة...">
            </div>
            <div class="filter-group">
                <label for="service-type-filter">نوع الخدمة:</label>
                <select id="service-type-filter" class="form-control">
                    <option value="">جميع الأنواع</option>
                    ${Object.entries(SERVICE_TYPE_LABELS).map(([key, label]) => 
                        `<option value="${key}">${label}</option>`
                    ).join('')}
                </select>
            </div>
            <div class="filter-group">
                <label for="priority-filter">الأولوية:</label>
                <select id="priority-filter" class="form-control">
                    <option value="">جميع الأولويات</option>
                    ${Object.entries(SERVICE_PRIORITY_LABELS).map(([key, label]) => 
                        `<option value="${key}">${label}</option>`
                    ).join('')}
                </select>
            </div>
            <div class="filter-group">
                <label for="date-from-filter">من تاريخ:</label>
                <input type="date" id="date-from-filter" class="form-control">
            </div>
            <div class="filter-group">
                <label for="date-to-filter">إلى تاريخ:</label>
                <input type="date" id="date-to-filter" class="form-control">
            </div>
            <div class="filter-actions">
                <button id="clear-maintenance-filters-btn" class="btn btn-secondary">
                    <i class="fas fa-times"></i> مسح الفلاتر
                </button>
            </div>
        </div>
        
        <!-- إحصائيات سريعة -->
        <div class="quick-stats">
            <div class="stat-item">
                <span class="stat-label">إجمالي السجلات:</span>
                <span id="total-maintenance-stat" class="stat-value">0</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">هذا الشهر:</span>
                <span id="this-month-maintenance-stat" class="stat-value">0</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">الخدمات الحرجة:</span>
                <span id="critical-services-stat" class="stat-value">0</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">الخدمات القادمة:</span>
                <span id="upcoming-services-stat" class="stat-value">0</span>
            </div>
        </div>
        
        <!-- تبويبات -->
        <div class="nav-tabs">
            <button class="nav-tab active" data-tab="maintenance-records">سجلات الصيانة</button>
            <button class="nav-tab" data-tab="upcoming-services">الخدمات القادمة</button>
        </div>
        
        <!-- محتوى التبويبات -->
        <div class="tab-content">
            <!-- تبويب سجلات الصيانة -->
            <div id="maintenance-records-tab" class="tab-pane active">
                <div class="section">
                    <div class="section-body">
                        <div class="table-responsive">
                            <table id="maintenance-records-table" class="table">
                                <thead>
                                    <tr>
                                        <th class="sortable" data-column="licensePlate">رقم اللوحة</th>
                                        <th class="sortable" data-column="serviceDate">تاريخ الخدمة</th>
                                        <th class="sortable" data-column="serviceType">نوع الخدمة</th>
                                        <th class="sortable" data-column="serviceCenter">مركز الخدمة</th>
                                        <th class="sortable" data-column="odometerReading">قراءة العداد</th>
                                        <th class="sortable" data-column="nextServiceDate">الخدمة القادمة</th>
                                        <th class="sortable" data-column="totalCost">التكلفة الإجمالية</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="maintenance-records-body">
                                    <!-- سيتم إنشاء الصفوف ديناميكياً -->
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- التنقل بين الصفحات -->
                        <div class="pagination-container">
                            <div class="pagination-info">
                                <span id="maintenance-pagination-info">عرض 0 من 0 سجل</span>
                            </div>
                            <div class="pagination-controls">
                                <button id="maintenance-prev-page" class="btn btn-secondary" disabled>
                                    <i class="fas fa-chevron-right"></i> السابق
                                </button>
                                <span id="maintenance-page-numbers"></span>
                                <button id="maintenance-next-page" class="btn btn-secondary" disabled>
                                    التالي <i class="fas fa-chevron-left"></i>
                                </button>
                            </div>
                            <div class="page-size-selector">
                                <label for="maintenance-page-size">عرض:</label>
                                <select id="maintenance-page-size" class="form-control">
                                    <option value="10">10</option>
                                    <option value="25">25</option>
                                    <option value="50">50</option>
                                    <option value="100">100</option>
                                </select>
                                <span>سجل</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- تبويب الخدمات القادمة -->
            <div id="upcoming-services-tab" class="tab-pane">
                <div class="section">
                    <div class="section-header">
                        <h2 class="section-title">الخدمات القادمة</h2>
                        <div class="section-actions">
                            <select id="upcoming-services-filter" class="form-control">
                                <option value="all">جميع الخدمات</option>
                                <option value="critical">الخدمات الحرجة</option>
                                <option value="upcoming">الخدمات القادمة</option>
                                <option value="maintenance">الصيانة</option>
                                <option value="tires">الإطارات</option>
                                <option value="license">تجديد الرخصة</option>
                            </select>
                        </div>
                    </div>
                    <div class="section-body">
                        <div class="table-responsive">
                            <table id="upcoming-services-table" class="table">
                                <thead>
                                    <tr>
                                        <th>المركبة</th>
                                        <th>نوع الخدمة</th>
                                        <th>الفرع</th>
                                        <th>الحالة</th>
                                        <th>المتبقي</th>
                                        <th>التاريخ المتوقع</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="upcoming-services-body">
                                    <!-- سيتم إنشاء الصفوف ديناميكياً -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

/**
 * تحميل بيانات الصيانة
 */
async function loadMaintenanceData() {
    try {
        showSpinner('جاري تحميل بيانات الصيانة...');
        
        // تحميل سجلات الصيانة والمركبات
        const [maintenance, vehicles] = await Promise.all([
            getMaintenanceRecords(),
            getVehicles()
        ]);
        
        maintenanceData = maintenance || [];
        vehiclesData = vehicles || [];
        
        // حساب الخدمات القادمة
        upcomingServices = getUpcomingServices(vehiclesData);
        
        console.log('Maintenance data loaded:', {
            maintenance: maintenanceData.length,
            vehicles: vehiclesData.length,
            upcomingServices: upcomingServices.length
        });
        
    } catch (error) {
        console.error('Error loading maintenance data:', error);
        showNotification('فشل في تحميل بيانات الصيانة', 'error');
        maintenanceData = [];
        vehiclesData = [];
        upcomingServices = [];
    } finally {
        hideSpinner();
    }
}

/**
 * عرض صفحة الصيانة
 */
function renderMaintenancePage() {
    try {
        // عرض سجلات الصيانة
        renderMaintenanceRecords();
        
        // عرض الخدمات القادمة
        renderUpcomingServicesTable();
        
        // تحديث الإحصائيات
        updateMaintenanceStats();
        
    } catch (error) {
        console.error('Error rendering maintenance page:', error);
        showNotification('فشل في عرض صفحة الصيانة', 'error');
    }
}

/**
 * عرض سجلات الصيانة
 */
function renderMaintenanceRecords() {
    const tbody = document.getElementById('maintenance-records-body');
    if (!tbody) return;
    
    // تطبيق الفلاتر والترتيب
    const filteredRecords = applyMaintenanceFilters();
    const sortedRecords = applySorting(filteredRecords);
    
    // التنقل بين الصفحات
    const startIndex = (tableSettings.currentPage - 1) * tableSettings.itemsPerPage;
    const endIndex = startIndex + tableSettings.itemsPerPage;
    const pageRecords = sortedRecords.slice(startIndex, endIndex);
    
    if (pageRecords.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center">
                    <div class="empty-state">
                        <i class="fas fa-tools"></i>
                        <h3>لا توجد سجلات صيانة</h3>
                        <p>لم يتم العثور على سجلات تطابق المعايير المحددة</p>
                    </div>
                </td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = pageRecords.map(record => createMaintenanceRow(record)).join('');
    
    // تحديث معلومات التنقل
    updateMaintenancePagination(sortedRecords.length);
}

/**
 * إنشاء صف سجل صيانة
 */
function createMaintenanceRow(record) {
    const vehicle = vehiclesData.find(v => v['Vehicle ID'] === record['Vehicle ID']);
    const licensePlate = vehicle ? vehicle['License Plate'] : record['Vehicle ID'];
    
    return `
        <tr data-record-id="${record.id || record['Record ID']}">
            <td>${licensePlate}</td>
            <td>${formatDate(record['Service Date'])}</td>
            <td>${SERVICE_TYPE_LABELS[record['Service Type']] || record['Service Type']}</td>
            <td>${record['Service Center'] || '-'}</td>
            <td>${formatNumber(record['Odometer Reading'])}</td>
            <td>${formatDate(record['Next Service Date'])}</td>
            <td>${formatNumber(record['Total Cost'])} ريال</td>
            <td>
                <div class="action-buttons">
                    <button class="btn btn-sm btn-info view-maintenance-btn" data-record-id="${record.id}" title="عرض التفاصيل">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-warning edit-maintenance-btn" data-record-id="${record.id}" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-danger delete-maintenance-btn" data-record-id="${record.id}" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `;
}

/**
 * عرض جدول الخدمات القادمة
 */
function renderUpcomingServicesTable() {
    const tbody = document.getElementById('upcoming-services-body');
    if (!tbody) return;
    
    // تطبيق فلتر الخدمات القادمة
    const filter = document.getElementById('upcoming-services-filter')?.value || 'all';
    const filteredServices = filterServices(upcomingServices, filter);
    
    if (filteredServices.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center">
                    <div class="empty-state">
                        <i class="fas fa-calendar-check"></i>
                        <h3>لا توجد خدمات قادمة</h3>
                        <p>جميع المركبات في حالة جيدة</p>
                    </div>
                </td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = filteredServices.map(service => createUpcomingServiceRow(service)).join('');
}

/**
 * إنشاء صف خدمة قادمة
 */
function createUpcomingServiceRow(service) {
    return `
        <tr class="${service.statusInfo.class}-row">
            <td>${service.vehicle.plate}</td>
            <td>${getServiceTypeLabel(service.type)}</td>
            <td>${service.vehicle.branch}</td>
            <td>
                <div class="status-indicator ${service.statusInfo.class}">
                    <i class="fas fa-${service.statusInfo.icon}"></i>
                    <span>${service.statusInfo.text}</span>
                </div>
            </td>
            <td>${formatServiceRemaining(service)}</td>
            <td>${service.expectedDate}</td>
            <td>
                <div class="action-buttons">
                    <button class="btn btn-sm btn-primary schedule-service-btn" data-vehicle-id="${service.vehicle.id}" title="جدولة الخدمة">
                        <i class="fas fa-calendar-plus"></i>
                    </button>
                    <button class="btn btn-sm btn-info view-vehicle-btn" data-vehicle-id="${service.vehicle.id}" title="عرض المركبة">
                        <i class="fas fa-car"></i>
                    </button>
                </div>
            </td>
        </tr>
    `;
}

/**
 * ربط الأحداث
 */
function bindMaintenanceEvents() {
    // زر إضافة سجل صيانة
    const addBtn = document.getElementById('add-maintenance-btn');
    if (addBtn) {
        addBtn.addEventListener('click', () => openAddMaintenanceModal());
    }
    
    // التبويبات
    const tabButtons = document.querySelectorAll('.nav-tab');
    tabButtons.forEach(tab => {
        tab.addEventListener('click', (e) => {
            switchTab(e.target.dataset.tab);
        });
    });
    
    // فلاتر البحث
    bindFilterEvents();
    
    // أحداث الجدول
    bindTableEvents();
}

/**
 * تطبيق فلاتر الصيانة
 */
function applyMaintenanceFilters() {
    return maintenanceData.filter(record => {
        // فلتر البحث
        if (currentFilters.search) {
            const searchTerm = currentFilters.search.toLowerCase();
            const vehicle = vehiclesData.find(v => v['Vehicle ID'] === record['Vehicle ID']);
            const licensePlate = vehicle ? vehicle['License Plate'] : '';
            
            const searchableFields = [
                licensePlate,
                record['Service Type'],
                record['Service Center'],
                record['Vehicle ID']
            ];
            
            const matches = searchableFields.some(field => 
                field?.toString().toLowerCase().includes(searchTerm)
            );
            
            if (!matches) return false;
        }
        
        // فلتر نوع الخدمة
        if (currentFilters.serviceType && record['Service Type'] !== currentFilters.serviceType) {
            return false;
        }
        
        // فلتر التاريخ
        if (currentFilters.dateFrom || currentFilters.dateTo) {
            const serviceDate = new Date(record['Service Date']);
            
            if (currentFilters.dateFrom && serviceDate < new Date(currentFilters.dateFrom)) {
                return false;
            }
            
            if (currentFilters.dateTo && serviceDate > new Date(currentFilters.dateTo)) {
                return false;
            }
        }
        
        return true;
    });
}

/**
 * دالة الرندر الرئيسية للصفحة
 */
export async function render(container) {
    if (container) {
        container.innerHTML = '';
        await initializeMaintenance();
    } else {
        await initializeMaintenance();
    }
}

// تصدير الدوال للاستخدام الخارجي
export {
    loadMaintenanceData,
    renderMaintenancePage,
    renderMaintenanceRecords,
    renderUpcomingServicesTable
};

export default {
    render,
    initializeMaintenance,
    loadMaintenanceData,
    renderMaintenancePage
};

/**
 * Fleet Management System - Main CSS
 * ملف CSS الرئيسي لنظام إدارة الأسطول
 */

/* استيراد الخطوط */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap');
@import url('https://fonts.cdnfonts.com/css/sf-pro-display');
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap');

/* استيراد ملفات CSS الفرعية */
@import './components.css';
@import './layout.css';
/* @import './pages.css'; */
/* @import './themes.css'; */
/* @import './responsive.css'; */

/* المتغيرات العامة */
:root {
    /* الألوان الأساسية */
    --primary-color: #0057ff;
    --secondary-color: #222222;
    --success-color: #00b478;
    --danger-color: #ff3c4c;
    --warning-color: #ffa500;
    --info-color: #17a2b8;
    
    /* ألوان الخلفية والنص */
    --background-color: #ffffff;
    --text-color: #3d3d3d;
    --text-color-light: #666666;
    --text-color-dark: #1b1b1b;
    --border-color: #e8e8e8;
    --hover-color: #f9f9f9;
    --card-bg-color: #ffffff;
    
    /* أبعاد التخطيط */
    --sidebar-width: 250px;
    --header-height: 60px;
    --border-radius: 4px;
    --border-radius-lg: 12px;
    
    /* الظلال والتأثيرات */
    --box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    --box-shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.15);
    --transition-speed: 0.2s;
    
    /* ألوان RGB للشفافية */
    --primary-color-rgb: 0, 87, 255;
    --warning-color-rgb: 255, 165, 0;
    --danger-color-rgb: 255, 60, 76;
    --success-color-rgb: 0, 180, 120;
}

/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
    background-color: #fafafa;
    color: var(--text-color);
    line-height: 1.6;
    direction: ltr;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    overflow-x: hidden;
}

/* الروابط */
a {
    text-decoration: none;
    color: inherit;
    transition: color var(--transition-speed);
}

a:hover {
    color: var(--primary-color);
}

/* القوائم */
ul, ol {
    list-style: none;
}

/* الصور */
img {
    max-width: 100%;
    height: auto;
    display: block;
}

/* الأزرار الأساسية */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--border-radius);
    font-family: inherit;
    font-weight: 600;
    font-size: 0.95rem;
    cursor: pointer;
    transition: all var(--transition-speed);
    text-decoration: none;
    white-space: nowrap;
    user-select: none;
    position: relative;
    overflow: hidden;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--box-shadow);
}

.btn:active {
    transform: translateY(0);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* أنواع الأزرار */
.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: #0046cc;
}

.btn-secondary {
    background-color: var(--secondary-color);
    color: white;
}

.btn-secondary:hover {
    background-color: #333333;
}

.btn-success {
    background-color: var(--success-color);
    color: white;
}

.btn-success:hover {
    background-color: #009960;
}

.btn-danger {
    background-color: var(--danger-color);
    color: white;
}

.btn-danger:hover {
    background-color: #e63946;
}

.btn-warning {
    background-color: var(--warning-color);
    color: white;
}

.btn-warning:hover {
    background-color: #e69500;
}

.btn-info {
    background-color: var(--info-color);
    color: white;
}

.btn-info:hover {
    background-color: #138496;
}

/* أحجام الأزرار */
.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: 1.125rem;
}

/* أزرار الأيقونات */
.btn-icon {
    padding: 0.75rem;
    border-radius: 50%;
    width: 2.5rem;
    height: 2.5rem;
}

/* النماذج */
.form-group {
    margin-bottom: 1rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-color-dark);
}

.form-control {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: all var(--transition-speed);
    background-color: var(--background-color);
    color: var(--text-color);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.1);
}

.form-control:disabled {
    background-color: #f8f9fa;
    opacity: 0.6;
    cursor: not-allowed;
}

/* الجداول */
.table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    background-color: var(--card-bg-color);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.table th {
    background-color: #f8f9fa;
    padding: 1rem;
    font-weight: 600;
    text-align: left;
    border-bottom: 2px solid var(--border-color);
    color: var(--text-color-dark);
}

.table td {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    color: var(--text-color);
}

.table tbody tr:hover {
    background-color: var(--hover-color);
}

.table tbody tr:last-child td {
    border-bottom: none;
}

/* البطاقات */
.card {
    background-color: var(--card-bg-color);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    overflow: hidden;
    transition: all var(--transition-speed);
}

.card:hover {
    box-shadow: var(--box-shadow-lg);
}

.card-header {
    padding: 1rem 1.5rem;
    background-color: #f8f9fa;
    border-bottom: 1px solid var(--border-color);
    font-weight: 600;
}

.card-body {
    padding: 1.5rem;
}

.card-footer {
    padding: 1rem 1.5rem;
    background-color: #f8f9fa;
    border-top: 1px solid var(--border-color);
}

/* الشارات */
.badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    white-space: nowrap;
}

.badge-primary {
    background-color: rgba(var(--primary-color-rgb), 0.1);
    color: var(--primary-color);
}

.badge-success {
    background-color: rgba(var(--success-color-rgb), 0.1);
    color: var(--success-color);
}

.badge-warning {
    background-color: rgba(var(--warning-color-rgb), 0.1);
    color: var(--warning-color);
}

.badge-danger {
    background-color: rgba(var(--danger-color-rgb), 0.1);
    color: var(--danger-color);
}

/* الأدوات المساعدة */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.d-inline-flex { display: inline-flex; }

.justify-content-center { justify-content: center; }
.justify-content-between { justify-content: space-between; }
.justify-content-end { justify-content: flex-end; }

.align-items-center { align-items: center; }
.align-items-start { align-items: flex-start; }
.align-items-end { align-items: flex-end; }

.gap-1 { gap: 0.25rem; }
.gap-2 { gap: 0.5rem; }
.gap-3 { gap: 1rem; }
.gap-4 { gap: 1.5rem; }

.m-0 { margin: 0; }
.m-1 { margin: 0.25rem; }
.m-2 { margin: 0.5rem; }
.m-3 { margin: 1rem; }
.m-4 { margin: 1.5rem; }

.p-0 { padding: 0; }
.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 1rem; }
.p-4 { padding: 1.5rem; }

/* الحاويات المرنة */
.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

.container-fluid {
    width: 100%;
    padding: 0 1rem;
}

/* الشبكة */
.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -0.5rem;
}

.col {
    flex: 1;
    padding: 0 0.5rem;
}

.col-auto {
    flex: 0 0 auto;
    padding: 0 0.5rem;
}

/* الانتقالات والحركات */
.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

.slide-in {
    animation: slideIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from { transform: translateY(-20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* التمرير المخصص */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

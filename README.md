# Fleet Management System

## Overview

This fleet management system provides comprehensive tools for managing your vehicle fleet, including:

- Real-time dashboard with fleet statistics
- Vehicle management
- Maintenance tracking
- Fuel consumption monitoring
- Driver management
- Comprehensive reporting
- User management with role-based access

## Features

### Dashboard

- Overview statistics of your fleet
- Visual charts showing vehicle status and distribution
- Maintenance compliance status
- Upcoming services alerts

### Vehicle Management

- Complete vehicle inventory
- Track vehicle status and location
- Maintenance history
- Assigned drivers

### Maintenance Tracking

- Schedule and record maintenance services
- Preventive maintenance reminders
- Service history
- Maintenance costs tracking

### Fuel Management

- Record fuel consumption
- Track fuel economy
- Cost analysis
- Consumption reporting

### Driver Management

- Driver database with contact information
- License tracking with expiry alerts
- Vehicle assignment

### Reporting

- Generate detailed reports:
  - Vehicle status reports
  - Maintenance reports
  - Fuel consumption reports
  - Driver reports
- Export to HTML and PDF formats

### System Features

- Role-based access control (Admin, Manager, Employee)
- Dark mode support
- Responsive design for all devices
- Interactive charts and statistics

## Technical Requirements

- Modern browser (Chrome, Firefox, Edge, Safari)
- Internet connection for API data
- Google Sheets backend integration

## Installation

1. Clone the repository
2. Configure the API connection in `script.js`
3. Deploy to your web server
4. Access the system through your browser

## Credits

Developed by <PERSON>

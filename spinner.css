/* Loading Spinner Styles */
.spinner-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity 0.3s ease;
}

.spinner {
    width: 70px;
    height: 70px;
    position: relative;
}

.spinner-logo {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 40px;
    height: 40px;
    background-image: url('assets/img/logo.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}

.spinner-circle {
    position: absolute;
    width: 100%;
    height: 100%;
    border: 4px solid transparent;
    border-radius: 50%;
    animation: spin 1.2s linear infinite;
}

.spinner-circle-primary {
    border-top-color: #007bff;
    animation-delay: 0s;
}

.spinner-circle-secondary {
    width: 80%;
    height: 80%;
    top: 10%;
    left: 10%;
    border-right-color: #6c757d;
    animation-delay: -0.3s;
}

.spinner-circle-success {
    width: 60%;
    height: 60%;
    top: 20%;
    left: 20%;
    border-bottom-color: #28a745;
    animation-delay: -0.6s;
}

.spinner-text {
    position: absolute;
    bottom: -30px;
    left: 50%;
    transform: translateX(-50%);
    font-family: 'Roboto', sans-serif;
    font-size: 14px;
    color: #333;
    white-space: nowrap;
    font-weight: 500;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* Spinner for specific sections */
.section-spinner {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
    width: 100%;
}

.section-spinner .spinner {
    width: 50px;
    height: 50px;
}

.section-spinner .spinner-text {
    bottom: -25px;
    font-size: 12px;
}

/* Table loading spinner */
.table-spinner {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px 0;
    width: 100%;
}

.table-spinner .spinner {
    width: 40px;
    height: 40px;
}

.table-spinner .spinner-circle {
    border-width: 3px;
}

.table-spinner .spinner-text {
    bottom: -20px;
    font-size: 11px;
}

/* Button spinner */
.btn-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    margin-right: 5px;
    vertical-align: middle;
}

.btn-spinner .spinner-circle {
    border-width: 2px;
}

.btn-spinner .spinner-logo,
.btn-spinner .spinner-text {
    display: none;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .spinner-container {
        background-color: rgba(33, 37, 41, 0.8);
    }
    
    .spinner-text {
        color: #f8f9fa;
    }
}

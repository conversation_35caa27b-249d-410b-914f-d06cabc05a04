/**
 * Modal Component - نافذة منبثقة قابلة لإعادة الاستخدام
 * يوفر وظائف فتح وإغلاق النوافذ المنبثقة مع إدارة الأحداث
 */

export class Modal {
    constructor(modalId, options = {}) {
        this.modalId = modalId;
        this.modal = document.getElementById(modalId);
        this.options = {
            closeOnBackdrop: true,
            closeOnEscape: true,
            ...options
        };
        
        this.init();
    }

    init() {
        if (!this.modal) {
            console.error(`Modal with ID ${this.modalId} not found`);
            return;
        }

        this.setupEventListeners();
    }

    setupEventListeners() {
        // إغلاق النافذة عند النقر على زر الإغلاق
        const closeButtons = this.modal.querySelectorAll('.close-btn, [data-dismiss="modal"]');
        closeButtons.forEach(btn => {
            btn.addEventListener('click', () => this.close());
        });

        // إغلاق النافذة عند النقر خارجها
        if (this.options.closeOnBackdrop) {
            this.modal.addEventListener('click', (e) => {
                if (e.target === this.modal) {
                    this.close();
                }
            });
        }

        // إغلاق النافذة عند الضغط على Escape
        if (this.options.closeOnEscape) {
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && this.isOpen()) {
                    this.close();
                }
            });
        }
    }

    open() {
        if (!this.modal) return;
        
        this.modal.style.display = 'flex';
        this.modal.classList.add('active');
        document.body.classList.add('modal-open');
        
        // تركيز على أول عنصر قابل للتفاعل
        const focusableElement = this.modal.querySelector('input, button, select, textarea');
        if (focusableElement) {
            setTimeout(() => focusableElement.focus(), 100);
        }

        // إطلاق حدث فتح النافذة
        this.modal.dispatchEvent(new CustomEvent('modal:open'));
    }

    close() {
        if (!this.modal) return;
        
        this.modal.style.display = 'none';
        this.modal.classList.remove('active');
        document.body.classList.remove('modal-open');

        // إطلاق حدث إغلاق النافذة
        this.modal.dispatchEvent(new CustomEvent('modal:close'));
    }

    isOpen() {
        return this.modal && this.modal.style.display !== 'none' && this.modal.classList.contains('active');
    }

    setContent(content) {
        const modalBody = this.modal.querySelector('.modal-body');
        if (modalBody) {
            modalBody.innerHTML = content;
        }
    }

    setTitle(title) {
        const modalTitle = this.modal.querySelector('.modal-title');
        if (modalTitle) {
            modalTitle.textContent = title;
        }
    }
}

// دوال مساعدة للاستخدام السريع
export function openModal(modalId) {
    const modal = new Modal(modalId);
    modal.open();
    return modal;
}

export function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'none';
        modal.classList.remove('active');
        document.body.classList.remove('modal-open');
    }
}

// إنشاء نافذة منبثقة ديناميكية
export function createModal(id, title, content, options = {}) {
    const modalHTML = `
        <div id="${id}" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title">${title}</h3>
                    <button class="close-btn" data-dismiss="modal">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    ${content}
                </div>
                ${options.showFooter ? `
                <div class="modal-footer">
                    <button class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <button class="btn btn-primary" id="${id}-confirm">تأكيد</button>
                </div>
                ` : ''}
            </div>
        </div>
    `;

    // إضافة النافذة إلى الصفحة
    document.body.insertAdjacentHTML('beforeend', modalHTML);
    
    return new Modal(id, options);
}

export default Modal;

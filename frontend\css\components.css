/**
 * Components CSS - تنسيق المكونات
 * يحتوي على أنماط جميع المكونات القابلة لإعادة الاستخدام
 */

/* ===== بطاقات الإحصائيات ===== */
.stat-card {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.stat-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.stat-card.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.02); }
}

.stat-card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-md);
}

.stat-card-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: var(--text-light);
}

.stat-card-icon.blue { background: linear-gradient(135deg, #3b82f6, #1d4ed8); }
.stat-card-icon.green { background: linear-gradient(135deg, #10b981, #059669); }
.stat-card-icon.orange { background: linear-gradient(135deg, #f59e0b, #d97706); }
.stat-card-icon.red { background: linear-gradient(135deg, #ef4444, #dc2626); }
.stat-card-icon.purple { background: linear-gradient(135deg, #8b5cf6, #7c3aed); }

.stat-card-value {
    font-size: var(--font-3xl);
    font-weight: var(--font-bold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.stat-card-title {
    font-size: var(--font-sm);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
}

.stat-card-subtitle {
    font-size: var(--font-xs);
    color: var(--text-muted);
}

.stat-card-trend {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-sm);
    font-weight: var(--font-medium);
}

.stat-card-trend.up {
    color: var(--success-color);
}

.stat-card-trend.down {
    color: var(--error-color);
}

.stat-card-trend.neutral {
    color: var(--text-secondary);
}

.stat-card-loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-lg);
}

/* ===== النوافذ المنبثقة ===== */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.modal-overlay.show {
    opacity: 1;
    visibility: visible;
}

.modal {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
    transform: scale(0.9);
    transition: transform var(--transition-normal);
}

.modal-overlay.show .modal {
    transform: scale(1);
}

.modal.small { width: 400px; }
.modal.medium { width: 600px; }
.modal.large { width: 800px; }
.modal.extra-large { width: 1000px; }

.modal-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-title {
    font-size: var(--font-lg);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
}

.modal-close {
    background: none;
    border: none;
    font-size: var(--font-xl);
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.modal-close:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

.modal-body {
    padding: var(--spacing-lg);
    max-height: 60vh;
    overflow-y: auto;
}

.modal-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
}

/* ===== مؤشرات التحميل ===== */
.spinner {
    display: inline-block;
    position: relative;
}

.spinner-border {
    width: 2rem;
    height: 2rem;
    border: 0.25em solid currentColor;
    border-left-color: transparent;
    border-radius: 50%;
    animation: spinner-border 0.75s linear infinite;
}

@keyframes spinner-border {
    to { transform: rotate(360deg); }
}

.spinner-dots {
    display: flex;
    gap: var(--spacing-xs);
}

.spinner-dots .dot {
    width: 8px;
    height: 8px;
    background: currentColor;
    border-radius: 50%;
    animation: spinner-dots 1.4s ease-in-out infinite both;
}

.spinner-dots .dot:nth-child(1) { animation-delay: -0.32s; }
.spinner-dots .dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes spinner-dots {
    0%, 80%, 100% { transform: scale(0); }
    40% { transform: scale(1); }
}

.spinner-pulse {
    width: 2rem;
    height: 2rem;
    background: currentColor;
    border-radius: 50%;
    animation: spinner-pulse 1s ease-in-out infinite;
}

@keyframes spinner-pulse {
    0% { transform: scale(0); opacity: 1; }
    100% { transform: scale(1); opacity: 0; }
}

.spinner-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    gap: var(--spacing-md);
}

.spinner-message {
    font-size: var(--font-lg);
    color: var(--text-secondary);
    text-align: center;
}

/* أحجام مؤشرات التحميل */
.spinner-sm { font-size: 0.875rem; }
.spinner-lg { font-size: 1.5rem; }

/* ===== الجداول التفاعلية ===== */
.table-container {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
}

.table-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: var(--bg-secondary);
}

.table-title {
    font-size: var(--font-lg);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
}

.table-actions {
    display: flex;
    gap: var(--spacing-sm);
    align-items: center;
}

.table-search {
    position: relative;
}

.table-search input {
    padding-right: 2.5rem;
    width: 250px;
}

.table-search-icon {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
}

.table-filters {
    display: flex;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-tertiary);
}

.table-filter {
    min-width: 150px;
}

.table-wrapper {
    overflow-x: auto;
}

.table th {
    position: relative;
    cursor: pointer;
    user-select: none;
}

.table th:hover {
    background: var(--bg-tertiary);
}

.table th.sortable::after {
    content: '↕';
    position: absolute;
    left: 0.5rem;
    opacity: 0.5;
}

.table th.sort-asc::after {
    content: '↑';
    opacity: 1;
}

.table th.sort-desc::after {
    content: '↓';
    opacity: 1;
}

.table-row-actions {
    display: flex;
    gap: var(--spacing-xs);
    justify-content: center;
}

.table-row-actions .btn {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-xs);
}

.table-pagination {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.pagination-info {
    font-size: var(--font-sm);
    color: var(--text-secondary);
}

.pagination-controls {
    display: flex;
    gap: var(--spacing-xs);
    align-items: center;
}

.pagination-btn {
    padding: var(--spacing-xs) var(--spacing-sm);
    border: 1px solid var(--border-color);
    background: var(--bg-primary);
    color: var(--text-primary);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.pagination-btn:hover:not(:disabled) {
    background: var(--bg-secondary);
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-btn.active {
    background: var(--primary-color);
    color: var(--text-light);
    border-color: var(--primary-color);
}

/* ===== الإشعارات ===== */
.notification {
    position: fixed;
    top: var(--spacing-lg);
    left: var(--spacing-lg);
    min-width: 300px;
    max-width: 500px;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    padding: var(--spacing-lg);
    z-index: 1050;
    transform: translateX(-100%);
    transition: transform var(--transition-normal);
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    border-right: 4px solid var(--success-color);
}

.notification.warning {
    border-right: 4px solid var(--warning-color);
}

.notification.error {
    border-right: 4px solid var(--error-color);
}

.notification.info {
    border-right: 4px solid var(--info-color);
}

.notification-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-sm);
}

.notification-icon {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-sm);
    color: var(--text-light);
}

.notification.success .notification-icon {
    background: var(--success-color);
}

.notification.warning .notification-icon {
    background: var(--warning-color);
}

.notification.error .notification-icon {
    background: var(--error-color);
}

.notification.info .notification-icon {
    background: var(--info-color);
}

.notification-close {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0;
    font-size: var(--font-lg);
}

.notification-title {
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.notification-message {
    color: var(--text-secondary);
    font-size: var(--font-sm);
    line-height: 1.4;
}

.notification-progress {
    height: 2px;
    background: var(--border-color);
    border-radius: 1px;
    overflow: hidden;
    margin-top: var(--spacing-sm);
}

.notification-progress-bar {
    height: 100%;
    background: var(--primary-color);
    transition: width linear;
}

/* ===== الرسوم البيانية ===== */
.chart-container {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
}

.chart-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-lg);
}

.chart-title {
    font-size: var(--font-lg);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
}

.chart-controls {
    display: flex;
    gap: var(--spacing-sm);
}

.chart-wrapper {
    position: relative;
    height: 300px;
}

.chart-loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.8);
    border-radius: var(--radius-lg);
}

.chart-legend {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-md);
    margin-top: var(--spacing-lg);
    justify-content: center;
}

.chart-legend-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-sm);
}

.chart-legend-color {
    width: 12px;
    height: 12px;
    border-radius: 2px;
}

/* ===== حالات فارغة ===== */
.empty-state {
    text-align: center;
    padding: var(--spacing-2xl);
    color: var(--text-secondary);
}

.empty-state-icon {
    font-size: 4rem;
    color: var(--text-muted);
    margin-bottom: var(--spacing-lg);
}

.empty-state-title {
    font-size: var(--font-xl);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.empty-state-message {
    font-size: var(--font-base);
    margin-bottom: var(--spacing-lg);
}

/* ===== الاستجابة للمكونات ===== */
@media (max-width: 768px) {
    .stat-card {
        padding: var(--spacing-md);
    }
    
    .stat-card-value {
        font-size: var(--font-2xl);
    }
    
    .modal {
        width: 95vw;
        margin: var(--spacing-md);
    }
    
    .modal-body {
        padding: var(--spacing-md);
    }
    
    .table-header {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: stretch;
    }
    
    .table-actions {
        justify-content: space-between;
    }
    
    .table-search input {
        width: 100%;
    }
    
    .table-filters {
        flex-direction: column;
    }
    
    .notification {
        left: var(--spacing-sm);
        right: var(--spacing-sm);
        min-width: auto;
    }
    
    .chart-header {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: stretch;
    }
    
    .chart-legend {
        justify-content: flex-start;
    }
}

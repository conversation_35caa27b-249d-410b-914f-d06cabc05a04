// Google Apps Script for Fleet Management System
// Connects with Google Sheets as a database

// Spreadsheet configuration
const SPREADSHEET_CONFIG = {
  ID: '1Tx6YVrFbpjzNn66fTmARMauPILuGQk7S63ZityyFA28',
  SHEETS: {
    USERS: 'Users',
    VEHICLES: 'Vehicles',
    DRIVERS: 'Drivers',
    MAINTENANCE: 'Maintenance',
    FUEL: 'Fuel',
    DASHBOARD: 'Vehicles'
  }
};

// Role definitions
const ROLES = {
  SUPER_ADMIN: 'Super Admin',
  GENERAL_MANAGER: 'General Manager',
  OPERATIONS_MANAGER: 'Operations Manager',
  FLEET_MANAGER: 'Fleet Manager',
  FLEET_SUPERVISOR: 'Fleet Supervisor',
  FLEET_STAFF: 'Fleet Staff',
  DRIVER: 'Driver'
};

// Permissions definitions
const PERMISSIONS = {
  FULL_SYSTEM_CONTROL: 'Full system control',
  <PERSON>NA<PERSON>_SYSTEM_CODE: 'Manage system code',
  MANAGE_USERS: 'Manage users',
  ASSIGN_SUPERVISORS_MANAGERS: 'Assign supervisors and managers',
  MANAGE_VEHICLES_ALL_BRANCHES: 'Manage vehicles (all branches)',
  MANAGE_VEHICLES_BRANCH: 'Manage vehicles (branch only)',
  REGISTER_MAINTENANCE: 'Register maintenance',
  RECEIVE_MAINTENANCE_ALERTS: 'Receive maintenance alerts',
  UPDATE_MILEAGE_ALL_BRANCHES: 'Update mileage (all branches)',
  UPDATE_MILEAGE_BRANCH: 'Update mileage (branch only)',
  UPDATE_MILEAGE_ASSIGNED: 'Update mileage (assigned vehicle only)',
  VIEW_REPORTS: 'View reports',
  GRANT_TEMPORARY_PERMISSIONS: 'Grant temporary permissions'
};

// Role-based permissions matrix
const ROLE_PERMISSIONS = {
  [ROLES.SUPER_ADMIN]: [
    PERMISSIONS.FULL_SYSTEM_CONTROL,
    PERMISSIONS.MANAGE_SYSTEM_CODE,
    PERMISSIONS.MANAGE_USERS,
    PERMISSIONS.ASSIGN_SUPERVISORS_MANAGERS,
    PERMISSIONS.MANAGE_VEHICLES_ALL_BRANCHES,
    PERMISSIONS.MANAGE_VEHICLES_BRANCH,
    PERMISSIONS.REGISTER_MAINTENANCE,
    PERMISSIONS.RECEIVE_MAINTENANCE_ALERTS,
    PERMISSIONS.UPDATE_MILEAGE_ALL_BRANCHES,
    PERMISSIONS.UPDATE_MILEAGE_BRANCH,
    PERMISSIONS.VIEW_REPORTS,
    PERMISSIONS.GRANT_TEMPORARY_PERMISSIONS
  ],
  [ROLES.GENERAL_MANAGER]: [
    PERMISSIONS.MANAGE_USERS,
    PERMISSIONS.ASSIGN_SUPERVISORS_MANAGERS,
    PERMISSIONS.MANAGE_VEHICLES_ALL_BRANCHES,
    PERMISSIONS.MANAGE_VEHICLES_BRANCH,
    PERMISSIONS.REGISTER_MAINTENANCE,
    PERMISSIONS.RECEIVE_MAINTENANCE_ALERTS,
    PERMISSIONS.UPDATE_MILEAGE_ALL_BRANCHES,
    PERMISSIONS.UPDATE_MILEAGE_BRANCH,
    PERMISSIONS.VIEW_REPORTS,
    PERMISSIONS.GRANT_TEMPORARY_PERMISSIONS
  ],
  [ROLES.OPERATIONS_MANAGER]: [
    PERMISSIONS.ASSIGN_SUPERVISORS_MANAGERS,
    PERMISSIONS.MANAGE_VEHICLES_ALL_BRANCHES,
    PERMISSIONS.MANAGE_VEHICLES_BRANCH,
    PERMISSIONS.REGISTER_MAINTENANCE,
    PERMISSIONS.RECEIVE_MAINTENANCE_ALERTS,
    PERMISSIONS.UPDATE_MILEAGE_ALL_BRANCHES,
    PERMISSIONS.UPDATE_MILEAGE_BRANCH,
    PERMISSIONS.VIEW_REPORTS
  ],
  [ROLES.FLEET_MANAGER]: [
    PERMISSIONS.ASSIGN_SUPERVISORS_MANAGERS,
    PERMISSIONS.MANAGE_VEHICLES_ALL_BRANCHES,
    PERMISSIONS.MANAGE_VEHICLES_BRANCH,
    PERMISSIONS.REGISTER_MAINTENANCE,
    PERMISSIONS.RECEIVE_MAINTENANCE_ALERTS,
    PERMISSIONS.UPDATE_MILEAGE_ALL_BRANCHES,
    PERMISSIONS.UPDATE_MILEAGE_BRANCH,
    PERMISSIONS.VIEW_REPORTS
  ],
  [ROLES.FLEET_SUPERVISOR]: [
    PERMISSIONS.MANAGE_VEHICLES_BRANCH,
    PERMISSIONS.REGISTER_MAINTENANCE,
    PERMISSIONS.RECEIVE_MAINTENANCE_ALERTS,
    PERMISSIONS.UPDATE_MILEAGE_ALL_BRANCHES,
    PERMISSIONS.UPDATE_MILEAGE_BRANCH
  ],
  [ROLES.FLEET_STAFF]: [
    PERMISSIONS.UPDATE_MILEAGE_ALL_BRANCHES,
    PERMISSIONS.UPDATE_MILEAGE_BRANCH
  ],
  [ROLES.DRIVER]: [
    PERMISSIONS.UPDATE_MILEAGE_ASSIGNED
  ]
};

// Function to check if a user has a specific permission
function hasPermission(userRole, permission) {
  if (!userRole || !permission) return false;

  // Get permissions for the user's role
  const permissions = ROLE_PERMISSIONS[userRole] || [];

  // Check if the permission exists in the user's role permissions
  return permissions.includes(permission);
}

// Dashboard column names
// Helper function to verify column names match
function verifyColumnNames(sheet) {
  try {
    if (!sheet) {
      Logger.log('Sheet is undefined');
      return [];
    }
    const headers = sheet.getRange(1, 1, 1, sheet.getLastColumn()).getValues()[0];
    const expectedColumns = Object.values(DASHBOARD_COLUMNS);

    Logger.log('Actual column names in sheet:');
    Logger.log(headers);

    Logger.log('Expected column names:');
    Logger.log(expectedColumns);

    const missingColumns = expectedColumns.filter(col => !headers.includes(col));
    const extraColumns = headers.filter(col => !expectedColumns.includes(col));

    if (missingColumns.length > 0) {
      Logger.log('Missing columns: ' + missingColumns.join(', '));
    }
    if (extraColumns.length > 0) {
      Logger.log('Extra columns: ' + extraColumns.join(', '));
    }

    return headers;
  } catch (error) {
    Logger.log('Error in verifyColumnNames: ' + error.toString());
    return [];
  }
}

// Define column names exactly as they appear in the sheet
const DASHBOARD_COLUMNS = {
  VEHICLE_ID: 'Vehicle ID',
  LICENSE_PLATE: 'License Plate',
  SERVICE_TYPE: 'Service Type',
  VEHICLE_TYPE: 'Vehicle Type',
  MODEL: 'Model',
  COLOR: 'Color',
  VIN_NUMBER: 'VIN Number',
  FUEL_TYPE: 'Fuel Type',
  CURRENT_KM: 'Current Km',
  VEHICLE_STATUS: 'Vehicle Status',
  INACTIVE: 'Inactive',
  LAST_MAINTENANCE_KM: 'Last Maintenance Km',
  LAST_MAINTENANCE_DATE: 'Last Maintenance Date',
  NEXT_MAINTENANCE_KM: 'Next Maintenance Km',
  KM_TO_MAINTENANCE: 'Km to next maintenance',
  LAST_TIRE_CHANGE_KM: 'Last tire change Km',
  LAST_TIRE_CHANGE_DATE: 'Last tire change Data',
  NEXT_TIRE_CHANGE_KM: 'Next Tire Change Km',
  KM_TO_TIRE_CHANGE: 'Km left for tire change',
  LICENSE_RENEWAL_DATE: 'License Renewal Date',
  DAYS_TO_LICENSE: 'Days to renew license',
  INSURANCE_EXPIRY: 'Insurance Expiry Date',
  BRANCH: 'Branch',
  CURRENT_LOCATION: 'Current Location',
  DRIVER_NAME: 'Driver Name',
  DRIVER_CONTACT: 'Driver Contact',
  NOTES: 'Notes'
};

// Log column names for verification
function logColumnNames() {
  const { vehiclesSheet } = getSheets();
  const headers = vehiclesSheet.getRange(1, 1, 1, vehiclesSheet.getLastColumn()).getValues()[0];
  Logger.log('Actual Sheet Headers: ' + headers.join(', '));

  // Compare with DASHBOARD_COLUMNS
  const dashboardColumnValues = Object.values(DASHBOARD_COLUMNS);
  Logger.log('Missing columns in sheet: ' +
    dashboardColumnValues.filter(col => !headers.includes(col)).join(', '));
  Logger.log('Extra columns in sheet: ' +
    headers.filter(col => !dashboardColumnValues.includes(col)).join(', '));
}

// Get spreadsheet and worksheets
function getSheets() {
  try {
    const ss = SpreadsheetApp.openById(SPREADSHEET_CONFIG.ID);

    // Check and create sheets if they don't exist
    let usersSheet = ss.getSheetByName(SPREADSHEET_CONFIG.SHEETS.USERS);
    let vehiclesSheet = ss.getSheetByName(SPREADSHEET_CONFIG.SHEETS.VEHICLES);
    let driversSheet = ss.getSheetByName(SPREADSHEET_CONFIG.SHEETS.DRIVERS);
    let maintenanceSheet = ss.getSheetByName(SPREADSHEET_CONFIG.SHEETS.MAINTENANCE);
    let fuelSheet = ss.getSheetByName(SPREADSHEET_CONFIG.SHEETS.FUEL);
    let dashboardSheet = ss.getSheetByName(SPREADSHEET_CONFIG.SHEETS.DASHBOARD);

    // Create missing sheets and initialize with headers
    if (!usersSheet) {
      Logger.log('Creating Users sheet');
      usersSheet = ss.insertSheet(SPREADSHEET_CONFIG.SHEETS.USERS);
      usersSheet.appendRow(['ID', 'Name', 'Email', 'Password', 'Role', 'Status', 'Manager', 'Branch']);
    }

    if (!vehiclesSheet) {
      Logger.log('Creating Vehicles sheet');
      vehiclesSheet = ss.insertSheet(SPREADSHEET_CONFIG.SHEETS.VEHICLES);
      // Add headers based on DASHBOARD_COLUMNS
      vehiclesSheet.appendRow(Object.values(DASHBOARD_COLUMNS));
    }

    if (!driversSheet) {
      Logger.log('Creating Drivers sheet');
      driversSheet = ss.insertSheet(SPREADSHEET_CONFIG.SHEETS.DRIVERS);
      driversSheet.appendRow(['ID', 'Name', 'LicenseNumber', 'LicenseExpiry', 'Vehicle', 'Phone']);
    }

    if (!maintenanceSheet) {
      Logger.log('Creating Maintenance sheet');
      maintenanceSheet = ss.insertSheet(SPREADSHEET_CONFIG.SHEETS.MAINTENANCE);
      maintenanceSheet.appendRow([
        'Maintenance ID', 'Vehicle ID', 'License Plate', 'Service Type',
        'Service Center', 'Odometer Reading', 'Parts Cost', 'Labor Cost',
        'Total Cost', 'Service Date', 'Next Service Date', 'Notes'
      ]);
    }

    if (!fuelSheet) {
      Logger.log('Creating Fuel sheet');
      fuelSheet = ss.insertSheet(SPREADSHEET_CONFIG.SHEETS.FUEL);
      fuelSheet.appendRow([
        'Fuel ID', 'Vehicle ID', 'Date', 'Odometer', 'Fuel Amount',
        'Fuel Type', 'Cost', 'Station', 'Driver', 'Notes'
      ]);
    }

    if (!dashboardSheet) {
      Logger.log('Creating Vehicles sheet');
      dashboardSheet = ss.insertSheet(SPREADSHEET_CONFIG.SHEETS.DASHBOARD);
      dashboardSheet.appendRow(['Summary', 'Value', 'Last Updated']);
    }

    return {
      usersSheet,
      vehiclesSheet,
      driversSheet,
      maintenanceSheet,
      fuelSheet,
      dashboardSheet
    };
  } catch (error) {
    Logger.log('Error in getSheets: ' + error.toString());
    throw error;
  }
}
// Enable JSONP by wrapping response
function corsWrapper(result) {
  // Check if callback parameter is provided
  var callback = null;
  if (typeof arguments[1] !== 'undefined' && arguments[1]) {
    callback = arguments[1];
  }

  // Create JSON output
  var output = ContentService.createTextOutput();

  // If callback is provided, wrap the result in the callback function (JSONP)
  if (callback) {
    output.setContent(callback + '(' + JSON.stringify(result) + ')');
    output.setMimeType(ContentService.MimeType.JAVASCRIPT);
  } else {
    // Regular JSON response
    output.setContent(JSON.stringify(result));
    output.setMimeType(ContentService.MimeType.JSON);
  }

  return output;
}

// Web server to handle GET requests
function doGet(e) {
  try {
    const action = e.parameter.action;
    const callback = e.parameter.callback; // Get callback parameter for JSONP
    let result = {};

    if (action === 'login') {
      result = handleLogin(e.parameter.email, e.parameter.password);
    } else if (action === 'validateToken') {
      result = validateTokenHandler(e.parameter.token);
    } else if (action === 'getDropdownOptions') {
      result = getDropdownOptions();
    } else if (action === 'getVehicles') {
      result = getVehicles(e.parameter.managerId);
    } else if (action === 'getDrivers') {
      result = getDrivers(e.parameter.managerId);
    } else if (action === 'getMaintenance') {
      result = getMaintenance(e.parameter.managerId);
    } else if (action === 'getFuel') {
      result = getFuel(e.parameter.managerId);
    } else if (action === 'getUsers') {
      result = getUsers(e.parameter.userRole);
    } else if (action === 'getDashboard') {
      result = getDashboardData();
    } else if (action === 'addMaintenance') {
      // Handle addMaintenance in GET request
      const maintenanceParam = {
        data: e.parameter.data
      };
      result = addMaintenance(maintenanceParam);

      // Log the result for debugging
      Logger.log('addMaintenance result: ' + JSON.stringify(result));
    } else if (action === 'addVehicle') {
      result = addVehicle(e.parameter);
    } else if (action === 'updateVehicle') {
      result = updateVehicle(e.parameter);
    } else if (action === 'deleteVehicle') {
      result = deleteVehicle(e.parameter.id);
    } else if (action === 'updateMaintenance') {
      result = updateMaintenance(e.parameter);
    } else if (action === 'deleteMaintenance') {
      result = deleteMaintenance(e.parameter.id);
    } else {
      result = { status: 'error', message: 'Invalid action' };
    }

    // Return the result wrapped with the callback if provided (JSONP)
    return corsWrapper(result, callback);
  } catch (error) {
    Logger.log('Error in doGet: ' + error.toString());
    const output = ContentService.createTextOutput();
    output.setContent(JSON.stringify({
      status: 'error',
      message: error.toString()
    }));
    output.setMimeType(ContentService.MimeType.JSON);
    return output;
  }
}

// Handle POST requests (for add, update, delete operations)
function doPost(e) {
  try {
    // Check if we have parameters
    if (!e.parameter || !e.parameter.action) {
      return corsWrapper({
        status: 'error',
        message: 'No action specified'
      });
    }

    const action = e.parameter.action;
    let result = {};

    // Handle different actions
    switch (action) {
      case 'updateVehicle':
        result = updateVehicle(e.parameter);
        break;
      case 'addVehicle':
        result = addVehicle(e.parameter);
        break;
      case 'deleteVehicle':
        result = deleteVehicle(e.parameter.id);
        break;
      case 'addMaintenance':
        result = addMaintenance(e.parameter);
        break;
      case 'updateMaintenance':
        result = updateMaintenance(e.parameter);
        break;
      case 'deleteMaintenance':
        result = deleteMaintenance(e.parameter.id);
        break;
      case 'addFuel':
        result = addFuel(e.parameter);
        break;
      case 'updateFuel':
        result = updateFuel(e.parameter);
        break;
      case 'deleteFuel':
        result = deleteFuel(e.parameter.id);
        break;
      case 'addDriver':
        result = addDriver(e.parameter);
        break;
      case 'updateDriver':
        result = updateDriver(e.parameter);
        break;
      case 'deleteDriver':
        result = deleteDriver(e.parameter.id);
        break;
      case 'addUser':
        result = addUser(e.parameter);
        break;
      case 'updateUser':
        result = updateUser(e.parameter);
        break;
      case 'deleteUser':
        result = deleteUser(e.parameter.id, e.parameter.currentUserRole);
        break;
      case 'updateDashboard':
        result = updateDashboardData(e.parameter);
        break;
      default:
        result = { status: 'error', message: 'Invalid action' };
    }

    return corsWrapper(result);
  } catch (error) {
    Logger.log('Error in doPost: ' + error.toString());
    return corsWrapper({
      status: 'error',
      message: error.toString()
    });
  }
}

// Handle login
function handleLogin(email, password) {
  try {
    if (!email || !password) {
      return { status: 'error', message: 'Email and password are required' };
    }

    const { usersSheet } = getSheets();
    const data = usersSheet.getDataRange().getValues();
    const headers = data[0];

    // Find column indexes
    const idIndex = headers.indexOf('ID');
    const nameIndex = headers.indexOf('Name');
    const emailIndex = headers.indexOf('Email');
    const passwordIndex = headers.indexOf('Password');
    const roleIndex = headers.indexOf('Role');
    const statusIndex = headers.indexOf('Status');
    const branchIndex = headers.indexOf('Branch');

    // Validate column indexes
    if (idIndex === -1 || nameIndex === -1 || emailIndex === -1 ||
        passwordIndex === -1 || roleIndex === -1) {
      return { status: 'error', message: 'Invalid sheet structure' };
    }

    // Skip the first row (column headers)
    for (let i = 1; i < data.length; i++) {
      if (data[i][emailIndex] === email) {
        // Check if user is active
        if (statusIndex !== -1 && data[i][statusIndex] === 'Inactive') {
          return { status: 'error', message: 'Account is inactive' };
        }

        // In a real application, password should be hashed and verified securely
        // Here we assume the password is stored in column 3
        if (data[i][passwordIndex] === password) {
          // Create a token for the user
          const token = generateToken(data[i][idIndex]);

          // Get user role and map old roles to new roles if needed
          let userRole = data[i][roleIndex];
          if (userRole === 'admin') {
            userRole = ROLES.SUPER_ADMIN;
          } else if (userRole === 'manager') {
            userRole = ROLES.FLEET_MANAGER;
          } else if (userRole === 'employee') {
            userRole = ROLES.FLEET_STAFF;
          }

          // Create user object with all available data
          const userData = {
            id: data[i][idIndex],
            name: data[i][nameIndex],
            email: data[i][emailIndex],
            role: userRole,
            token: token
          };

          // Add branch if available
          if (branchIndex !== -1) {
            userData.branch = data[i][branchIndex];
          }

          return {
            status: 'success',
            data: userData
          };
        } else {
          return { status: 'error', message: 'Incorrect password' };
        }
      }
    }

    return { status: 'error', message: 'Email not found' };
  } catch (error) {
    Logger.log('Error in handleLogin: ' + error.toString());
    return { status: 'error', message: error.toString() };
  }
}

// Generate a token for the user
function generateToken(userId) {
  // In a real application, use a more secure method like JWT
  return Utilities.base64EncodeWebSafe(userId + '|' + new Date().getTime());
}

// Validate token handler
function validateTokenHandler(token) {
  try {
    const user = validateToken(token);
    if (user) {
      return {
        status: 'success',
        data: user
      };
    } else {
      return { status: 'error', message: 'Invalid or expired token' };
    }
  } catch (error) {
    Logger.log('Error in validateTokenHandler: ' + error.toString());
    return { status: 'error', message: error.toString() };
  }
}

// Validate token
function validateToken(token) {
  try {
    // In a real application, verify the token more securely
    const decoded = Utilities.base64DecodeWebSafe(token);
    const parts = decoded.split('|');
    const userId = parts[0];
    const timestamp = parts[1];

    // Check expiration time (e.g., valid for 24 hours)
    const currentTime = new Date().getTime();
    const tokenTime = parseInt(timestamp);
    const tokenLifetime = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

    if (currentTime - tokenTime > tokenLifetime) {
      return null; // Token expired
    }

    // Verify user exists in database
    const { usersSheet } = getSheets();
    const data = usersSheet.getDataRange().getValues();
    const headers = data[0];

    // Find column indexes
    const idIndex = headers.indexOf('ID');
    const nameIndex = headers.indexOf('Name');
    const emailIndex = headers.indexOf('Email');
    const roleIndex = headers.indexOf('Role');
    const statusIndex = headers.indexOf('Status');
    const branchIndex = headers.indexOf('Branch');

    // Validate column indexes
    if (idIndex === -1 || nameIndex === -1 || emailIndex === -1 || roleIndex === -1) {
      return null;
    }

    for (let i = 1; i < data.length; i++) {
      if (data[i][idIndex] === userId) {
        // Check if user is active
        if (statusIndex !== -1 && data[i][statusIndex] === 'Inactive') {
          return null; // User is inactive
        }

        // Get user role and map old roles to new roles if needed
        let userRole = data[i][roleIndex];
        if (userRole === 'admin') {
          userRole = ROLES.SUPER_ADMIN;
        } else if (userRole === 'manager') {
          userRole = ROLES.FLEET_MANAGER;
        } else if (userRole === 'employee') {
          userRole = ROLES.FLEET_STAFF;
        }

        // Create user object with all available data
        const userData = {
          id: data[i][idIndex],
          name: data[i][nameIndex],
          email: data[i][emailIndex],
          role: userRole
        };

        // Add branch if available
        if (branchIndex !== -1) {
          userData.branch = data[i][branchIndex];
        }

        return userData;
      }
    }

    return null; // User not found
  } catch (e) {
    Logger.log('Error in validateToken: ' + e.toString());
    return null; // Error decoding token
  }
}
// Get dropdown options from Vehicles sheet
function getDropdownOptions() {
  try {
    Logger.log('Getting dropdown options...');
    const sheets = getSheets();
    if (!sheets || !sheets.vehiclesSheet) {
      Logger.log('vehiclesSheet not found');
      throw new Error('Vehicles sheet not found');
    }

    const vehiclesSheet = sheets.vehiclesSheet;
    const data = vehiclesSheet.getDataRange().getValues();
    if (!data || !data.length) {
      Logger.log('No data found in vehicles sheet');
      throw new Error('No data found in vehicles sheet');
    }

    const headers = data[0];
    Logger.log('Headers found: ' + headers.join(', '));

    // Find column indexes for dropdown fields
    const serviceTypeIndex = headers.indexOf('Service Type');
    const vehicleTypeIndex = headers.indexOf('Vehicle Type');
    const modelIndex = headers.indexOf('Model');
    const colorIndex = headers.indexOf('Color');
    const fuelTypeIndex = headers.indexOf('Fuel Type');
    const inactiveIndex = headers.indexOf('Inactive');
    const branchIndex = headers.indexOf('Branch');
    const currentLocationIndex = headers.indexOf('Current Location');
    const driverNameIndex = headers.indexOf('Driver Name');

    Logger.log('Column indexes:', {
      serviceType: serviceTypeIndex,
      vehicleType: vehicleTypeIndex,
      model: modelIndex,
      color: colorIndex,
      fuelType: fuelTypeIndex,
      inactive: inactiveIndex,
      branch: branchIndex,
      currentLocation: currentLocationIndex,
      driverName: driverNameIndex
    });

    // Get unique values for each column
    const options = {
      serviceType: getUniqueValues(data, serviceTypeIndex),
      vehicleType: getUniqueValues(data, vehicleTypeIndex),
      model: getUniqueValues(data, modelIndex),
      color: getUniqueValues(data, colorIndex),
      fuelType: getUniqueValues(data, fuelTypeIndex),
      inactive: getUniqueValues(data, inactiveIndex),
      branch: getUniqueValues(data, branchIndex),
      currentLocation: getUniqueValues(data, currentLocationIndex),
      driverName: getUniqueValues(data, driverNameIndex)
    };

    Logger.log('Dropdown options:', options);

    return { status: 'success', data: options };
  } catch (error) {
    Logger.log('Error in getDropdownOptions: ' + error.toString());
    return { status: 'error', message: error.toString() };
  }
}

// Helper function to get unique values from a column
function getUniqueValues(data, columnIndex) {
  try {
    if (!data || !data.length) {
      Logger.log('No data provided to getUniqueValues');
      return [];
    }

    if (columnIndex === -1) {
      Logger.log('Column index not found');
      return [];
    }

    // Get all values from the column (skip header row)
    const values = data.slice(1).map(row => {
      if (!row || columnIndex >= row.length) {
        Logger.log(`Invalid row or column index: ${columnIndex}`);
        return null;
      }
      return row[columnIndex];
    });

    // Filter out null/undefined/empty values and get unique ones
    const uniqueValues = [...new Set(values.filter(value => value && value.toString().trim()))];
    Logger.log(`Found ${uniqueValues.length} unique values for column index ${columnIndex}`);
    return uniqueValues;
  } catch (error) {
    Logger.log('Error in getUniqueValues: ' + error.toString());
    return [];
  }
}


// Get vehicles
function getVehicles(managerId) {
  try {
    const { vehiclesSheet } = getSheets();
    const data = vehiclesSheet.getDataRange().getValues();
    const headers = data[0];
    const vehicles = [];

    // Find column indexes
    const idIndex = headers.indexOf('ID');
    const serialIndex = headers.indexOf('Serial');
    const typeIndex = headers.indexOf('Type');
    const statusIndex = headers.indexOf('Status');
    const lastMaintenanceIndex = headers.indexOf('LastMaintenance');
    const currentKilometersIndex = headers.indexOf('CurrentKilometers');
    const driverIndex = headers.indexOf('Driver');
    const managerIndex = headers.indexOf('Manager');
    const branchIndex = headers.indexOf('Branch');

    // Validate column indexes
    if (idIndex === -1) {
      return { status: 'error', message: 'Invalid vehicles sheet structure - ID column not found' };
    }

    // Skip the first row (column headers)
    for (let i = 1; i < data.length; i++) {
      // If managerId is provided, filter vehicles by manager
      if (managerId && managerIndex !== -1 && data[i][managerIndex] !== managerId) {
        continue;
      }

      // Create vehicle object with available data
      const vehicle = {
        id: data[i][idIndex]
      };

      // Add other fields if they exist
      if (serialIndex !== -1) vehicle.serial = data[i][serialIndex];
      if (typeIndex !== -1) vehicle.type = data[i][typeIndex];
      if (statusIndex !== -1) vehicle.status = data[i][statusIndex];
      if (lastMaintenanceIndex !== -1) vehicle.lastMaintenance = data[i][lastMaintenanceIndex];
      if (currentKilometersIndex !== -1) vehicle.currentKilometers = data[i][currentKilometersIndex];
      if (driverIndex !== -1) vehicle.driver = data[i][driverIndex];
      if (managerIndex !== -1) vehicle.manager = data[i][managerIndex];
      if (branchIndex !== -1) vehicle.branch = data[i][branchIndex];

      vehicles.push(vehicle);
    }

    return { status: 'success', data: vehicles };
  } catch (error) {
    Logger.log('Error in getVehicles: ' + error.toString());
    return { status: 'error', message: error.toString() };
  }
}

// Get drivers
function getDrivers(_managerId) { // Prefix with underscore to indicate it's not used
  try {
    const { driversSheet } = getSheets();
    const driversData = driversSheet.getDataRange().getValues();

    const driversHeaders = driversData[0];
    const drivers = [];

    // Find column indexes for drivers based on the actual structure in the Drivers sheet
    const driverCodeIndex = driversHeaders.indexOf('Code');
    const driverNameENIndex = driversHeaders.indexOf('Driver Name (EN)');
    const driverNameARIndex = driversHeaders.indexOf('Driver Name (AR)');
    const workNumberIndex = driversHeaders.indexOf('Work number');
    const personalNumberIndex = driversHeaders.indexOf('Personal number');
    const userNameIndex = driversHeaders.indexOf('User name');
    const branchIndex = driversHeaders.indexOf('Branch');
    const userProfileIndex = driversHeaders.indexOf('User Profile Details');

    // Validate required column indexes
    if (driverNameENIndex === -1) {
      return { status: 'error', message: 'Invalid drivers sheet structure - Missing Driver Name (EN) column' };
    }

    // Note: We could filter drivers by manager here if needed
    // For now, we'll just return all drivers regardless of managerId

    // Skip the first row (column headers)
    for (let i = 1; i < driversData.length; i++) {
      // Create driver object with available fields
      const driverObj = {
        id: driverCodeIndex !== -1 ? driversData[i][driverCodeIndex] : i,
        'Driver Name (EN)': driversData[i][driverNameENIndex],
        name: driversData[i][driverNameENIndex] // For backward compatibility
      };

      // Add additional fields if available
      if (driverNameARIndex !== -1) {
        driverObj['Driver Name (AR)'] = driversData[i][driverNameARIndex];
      }

      if (workNumberIndex !== -1) {
        driverObj['Work number'] = driversData[i][workNumberIndex];
      }

      if (personalNumberIndex !== -1) {
        driverObj['Personal number'] = driversData[i][personalNumberIndex];
      }

      if (userNameIndex !== -1) {
        driverObj['User name'] = driversData[i][userNameIndex];
      }

      if (branchIndex !== -1) {
        driverObj['Branch'] = driversData[i][branchIndex];
      }

      if (userProfileIndex !== -1) {
        driverObj['User Profile Details'] = driversData[i][userProfileIndex];
      }

      drivers.push(driverObj);
    }

    return { status: 'success', data: drivers };
  } catch (error) {
    Logger.log('Error in getDrivers: ' + error.toString());
    return { status: 'error', message: error.toString() };
  }
}

// Get maintenance records
function getMaintenance(managerId) {
  try {
    Logger.log('Starting getMaintenance function...');
    const { maintenanceSheet, vehiclesSheet } = getSheets();
    Logger.log('Got sheets successfully');

    // Add sample data if sheet is empty
    // Initialize sheets with proper headers if empty
    if (maintenanceSheet.getLastRow() <= 1) {
      Logger.log('Initializing maintenance sheet with headers');
      const maintenanceHeaders = [
        'Maintenance ID', 'Vehicle ID', 'License Plate', 'Service Type',
        'Service Center', 'Odometer Reading', 'Parts Cost', 'Labor Cost',
        'Total Cost', 'Service Date', 'Next Service Date', 'Notes'
      ];
      maintenanceSheet.getRange(1, 1, 1, maintenanceHeaders.length).setValues([maintenanceHeaders]);

      // Add sample maintenance record
      const sampleMaintenanceData = [
        'M001', 'A008864', '273  د ي ج', 'Oil Change',
        'AutoFix Garage', 120500, 300, 200,
        500, new Date('2025-03-10'), new Date('2025-06-10'), 'Regular maintenance'
      ];
      maintenanceSheet.getRange(2, 1, 1, sampleMaintenanceData.length).setValues([sampleMaintenanceData]);
    }

    if (vehiclesSheet.getLastRow() <= 1) {
      Logger.log('Initializing vehicles sheet with headers');
      const vehicleHeaders = [
        'Vehicle ID', 'License Plate', 'Type', 'Model', 'Manager ID'
      ];
      vehiclesSheet.getRange(1, 1, 1, vehicleHeaders.length).setValues([vehicleHeaders]);

      // Add sample vehicle record
      const sampleVehicleData = [
        'A008864', '273  د ي ج', 'Truck', 'Toyota', 'MGR001'
      ];
      vehiclesSheet.getRange(2, 1, 1, sampleVehicleData.length).setValues([sampleVehicleData]);
    }

    const maintenanceData = maintenanceSheet.getDataRange().getValues();
    const vehiclesData = vehiclesSheet.getDataRange().getValues();

    const maintenanceHeaders = maintenanceData[0];
    const vehiclesHeaders = vehiclesData[0];

    const maintenanceRecords = [];

    // Find column indexes for maintenance based on the provided column names
    const maintenanceIdIndex = maintenanceHeaders.indexOf('Maintenance ID');
    const vehicleIdIndex = maintenanceHeaders.indexOf('Vehicle ID');
    const licensePlateIndex = maintenanceHeaders.indexOf('License Plate');
    const serviceTypeIndex = maintenanceHeaders.indexOf('Service Type');
    const serviceCenterIndex = maintenanceHeaders.indexOf('Service Center');
    const odometerReadingIndex = maintenanceHeaders.indexOf('Odometer Reading');
    const partsCostIndex = maintenanceHeaders.indexOf('Parts Cost');
    const laborCostIndex = maintenanceHeaders.indexOf('Labor Cost');
    const totalCostIndex = maintenanceHeaders.indexOf('Total Cost');
    const serviceDateIndex = maintenanceHeaders.indexOf('Service Date');
    const nextServiceDateIndex = maintenanceHeaders.indexOf('Next Service Date');
    const notesIndex = maintenanceHeaders.indexOf('Notes');

    // Log the column indexes for debugging
    Logger.log('Maintenance column indexes:');
    Logger.log('Maintenance ID: ' + maintenanceIdIndex);
    Logger.log('Vehicle ID: ' + vehicleIdIndex);
    Logger.log('License Plate: ' + licensePlateIndex);
    Logger.log('Service Type: ' + serviceTypeIndex);

    // Validate maintenance column indexes
    if (maintenanceIdIndex === -1 || vehicleIdIndex === -1 || serviceDateIndex === -1 ||
        serviceTypeIndex === -1) {
      return { status: 'error', message: 'Invalid maintenance sheet structure. Required columns not found.' };
    }

    // Find column indexes for vehicles
    const vehicleIdColumnIndex = vehiclesHeaders.indexOf('Vehicle ID'); // Changed from 'ID' to 'Vehicle ID'
    const managerIdColumnIndex = vehiclesHeaders.indexOf('Manager ID'); // Changed from 'Manager' to 'Manager ID'

    Logger.log('Vehicle sheet headers:', vehiclesHeaders);
    Logger.log('Vehicle ID column index:', vehicleIdColumnIndex);
    Logger.log('Manager ID column index:', managerIdColumnIndex);

    // Skip vehicle validation if not filtering by manager
    if (managerId) {
        if (vehicleIdColumnIndex === -1 || managerIdColumnIndex === -1) {
            return { status: 'error', message: 'Invalid vehicles sheet structure. Vehicle ID or Manager ID columns not found.' };
        }
    }

    // Collect vehicles by manager if managerId is provided and columns exist
    const managerVehicles = [];
    if (managerId && vehicleIdColumnIndex !== -1 && managerIdColumnIndex !== -1) {
      Logger.log('Filtering by manager ID: ' + managerId);
      for (let i = 1; i < vehiclesData.length; i++) {
        if (vehiclesData[i][managerIdColumnIndex] === managerId) {
          managerVehicles.push(vehiclesData[i][vehicleIdColumnIndex]);
        }
      }
      Logger.log('Found vehicles for manager: ' + managerVehicles.join(', '));
    } else {
      Logger.log('Not filtering by manager - processing all maintenance records');
    }

    // Process maintenance records
    for (let i = 1; i < maintenanceData.length; i++) {
      const vehicleId = maintenanceData[i][vehicleIdIndex];
      Logger.log('Processing maintenance record for vehicle: ' + vehicleId);

      // If managerId is provided and we have vehicle data, filter by manager
      if (managerId && managerVehicles.length > 0 && !managerVehicles.includes(vehicleId)) {
        Logger.log('Skipping record - vehicle not assigned to manager');
        continue;
      }

      // Create maintenance record object with the exact column structure
      const record = {
        "Maintenance ID": String(maintenanceData[i][maintenanceIdIndex] || ''),
        "Vehicle ID": String(vehicleId || ''),
        "License Plate": licensePlateIndex !== -1 ? String(maintenanceData[i][licensePlateIndex] || '') : '',
        "Service Type": serviceTypeIndex !== -1 ? String(maintenanceData[i][serviceTypeIndex] || '') : '',
        "Service Center": serviceCenterIndex !== -1 ? String(maintenanceData[i][serviceCenterIndex] || '') : '',
        "Odometer Reading": odometerReadingIndex !== -1 ? Number(maintenanceData[i][odometerReadingIndex] || 0) : 0,
        "Parts Cost": partsCostIndex !== -1 ? Number(maintenanceData[i][partsCostIndex] || 0) : 0,
        "Labor Cost": laborCostIndex !== -1 ? Number(maintenanceData[i][laborCostIndex] || 0) : 0,
        "Total Cost": totalCostIndex !== -1 ? Number(maintenanceData[i][totalCostIndex] || 0) : 0,
        "Service Date": serviceDateIndex !== -1 ? (maintenanceData[i][serviceDateIndex] ? Utilities.formatDate(new Date(maintenanceData[i][serviceDateIndex]), "GMT", "yyyy-MM-dd") : '') : '',
        "Next Service Date": nextServiceDateIndex !== -1 ? (maintenanceData[i][nextServiceDateIndex] ? Utilities.formatDate(new Date(maintenanceData[i][nextServiceDateIndex]), "GMT", "yyyy-MM-dd") : '') : '',
        "Notes": notesIndex !== -1 ? String(maintenanceData[i][notesIndex] || '') : ''
      };

      // Also add id property for backward compatibility
      record.id = String(maintenanceData[i][maintenanceIdIndex] || '');

      Logger.log('Created record: ' + JSON.stringify(record));
      maintenanceRecords.push(record);
    }

    Logger.log('Returning maintenance records:', maintenanceRecords.length);
    return { status: 'success', data: maintenanceRecords };
  } catch (error) {
    Logger.log('Error in getMaintenance: ' + error.toString());
    return { status: 'error', message: error.toString() };
  }
}

// Get fuel records
function getFuel(managerId) {
  try {
    const ss = SpreadsheetApp.getActiveSpreadsheet();
    const fuelSheet = ss.getSheetByName('Fuel');

    if (!fuelSheet) {
      return { status: 'error', message: 'Fuel sheet not found' };
    }

    const data = getSheetData(fuelSheet);

    // Add unique identifiers and ensure structure consistency
    const processedData = data.map((record, index) => {
      // Ensure each record has a unique ID even if not in the spreadsheet
      return {
        ...record,
        id: record.id || record['Vehicle ID'] || `fuel-${index+1}`,
        // Make sure vehicle property matches Vehicle ID for compatibility
        vehicle: record['Vehicle ID']
      };
    });

    // Filter by manager if needed
    let filteredData = processedData;
    if (managerId && managerId !== 'all') {
      const vehiclesSheet = ss.getSheetByName('Vehicles');
      if (vehiclesSheet) {
        const vehiclesData = getSheetData(vehiclesSheet);
        const managerVehicles = vehiclesData
          .filter(v => v.Manager === managerId)
          .map(v => v.ID);

        filteredData = processedData.filter(record =>
          managerVehicles.includes(record['Vehicle ID'] || record.vehicle)
        );
      }
    }

    return { status: 'success', data: filteredData };
  } catch (error) {
    Logger.log('Error in getFuel: ' + error.toString());
    return { status: 'error', message: error.toString() };
  }
}

// Helper function to get sheet data with headers
function getSheetData(sheet) {
  const range = sheet.getDataRange();
  const values = range.getValues();

  if (values.length <= 1) {
    return [];
  }

  const headers = values[0];
  const data = [];

  for (let i = 1; i < values.length; i++) {
    const row = values[i];
    const record = {};

    for (let j = 0; j < headers.length; j++) {
      record[headers[j]] = row[j];
    }

    data.push(record);
  }

  return data;
}

// Get users (for users with permission to view users)
function getUsers(userRole) {
  try {
    // Check if user has permission to view users
    const allowedRolesToViewUsers = [
      ROLES.SUPER_ADMIN,
      ROLES.GENERAL_MANAGER,
      ROLES.OPERATIONS_MANAGER,
      ROLES.FLEET_MANAGER,
      ROLES.FLEET_SUPERVISOR
    ];

    if (userRole && !allowedRolesToViewUsers.includes(userRole)) {
      return {
        status: 'error',
        message: 'You do not have permission to view users. Only Super Admin, General Manager, Operations Manager, Fleet Manager, and Fleet Supervisor can view users.'
      };
    }

    const { usersSheet } = getSheets();
    const data = usersSheet.getDataRange().getValues();
    const headers = data[0];
    const users = [];

    // Find column indexes
    const idIndex = headers.indexOf('ID');
    const nameIndex = headers.indexOf('Name');
    const emailIndex = headers.indexOf('Email');
    const roleIndex = headers.indexOf('Role');
    const managerIndex = headers.indexOf('Manager');
    const statusIndex = headers.indexOf('Status');
    const branchIndex = headers.indexOf('Branch');
    const passwordIndex = headers.indexOf('Password');

    // Validate required column indexes
    if (idIndex === -1 || nameIndex === -1 || emailIndex === -1 || roleIndex === -1) {
      return { status: 'error', message: 'Invalid users sheet structure - required columns not found' };
    }

    // Skip the first row (column headers)
    for (let i = 1; i < data.length; i++) {
      // Create user object with required fields
      const user = {
        id: data[i][idIndex],
        name: data[i][nameIndex],
        email: data[i][emailIndex],
        role: data[i][roleIndex]
      };

      // Add optional fields if they exist
      if (managerIndex !== -1) user.manager = data[i][managerIndex];
      if (statusIndex !== -1) user.status = data[i][statusIndex];
      if (branchIndex !== -1) user.branch = data[i][branchIndex];

      // Only include password for Super Admin
      if (passwordIndex !== -1 && userRole === ROLES.SUPER_ADMIN) {
        user.password = data[i][passwordIndex];
      }

      users.push(user);
    }

    return { status: 'success', data: users };
  } catch (error) {
    Logger.log('Error in getUsers: ' + error.toString());
    return { status: 'error', message: error.toString() };
  }
}

// Add a new vehicle
function addVehicle(vehicleData) {
  try {
    // Check if user has permission to add vehicles
    if (vehicleData.userRole) {
      const allowedRoles = [
        ROLES.SUPER_ADMIN,
        ROLES.GENERAL_MANAGER,
        ROLES.OPERATIONS_MANAGER,
        ROLES.FLEET_MANAGER
      ];

      // If user role is not in the allowed roles list, deny access
      if (!allowedRoles.includes(vehicleData.userRole)) {
        return {
          status: 'error',
          message: 'You do not have permission to add vehicles. Only Super Admin, General Manager, Operations Manager, and Fleet Manager can add vehicles.'
        };
      }
    }

    const { vehiclesSheet } = getSheets();
    const data = vehiclesSheet.getDataRange().getValues();
    const headers = data[0];

    Logger.log('Adding vehicle with data:', vehicleData);
    Logger.log('Sheet headers:', headers);

    // Find column indexes using DASHBOARD_COLUMNS
    const idIndex = headers.indexOf(DASHBOARD_COLUMNS.VEHICLE_ID);
    const licensePlateIndex = headers.indexOf(DASHBOARD_COLUMNS.LICENSE_PLATE);
    const vehicleTypeIndex = headers.indexOf(DASHBOARD_COLUMNS.VEHICLE_TYPE);
    const serviceTypeIndex = headers.indexOf(DASHBOARD_COLUMNS.SERVICE_TYPE);
    const modelIndex = headers.indexOf(DASHBOARD_COLUMNS.MODEL);
    const colorIndex = headers.indexOf(DASHBOARD_COLUMNS.COLOR);
    const vinNumberIndex = headers.indexOf(DASHBOARD_COLUMNS.VIN_NUMBER);
    const currentKmIndex = headers.indexOf(DASHBOARD_COLUMNS.CURRENT_KM);
    const vehicleStatusIndex = headers.indexOf(DASHBOARD_COLUMNS.VEHICLE_STATUS);

    // Log column indexes for debugging
    Logger.log('Column indexes:', {
      id: idIndex,
      licensePlate: licensePlateIndex,
      vehicleType: vehicleTypeIndex,
      serviceType: serviceTypeIndex,
      model: modelIndex,
      color: colorIndex,
      vinNumber: vinNumberIndex,
      currentKm: currentKmIndex,
      status: vehicleStatusIndex
    });

    // Validate required column indexes
    if (idIndex === -1 || licensePlateIndex === -1 || vehicleTypeIndex === -1) {
      return {
        status: 'error',
        message: `Invalid vehicles sheet structure. Required columns not found. Available columns: ${headers.join(', ')}`
      };
    }

    // Check for duplicate license plate
    for (let i = 1; i < data.length; i++) {
      if (data[i][licensePlateIndex] === vehicleData.licensePlate) {
        return { status: 'error', message: 'License plate already exists' };
      }
    }

    // Create a unique ID - use 'A' prefix followed by a number
    const newId = 'A' + String(data.length).padStart(6, '0');

    Logger.log('Creating new vehicle with ID:', newId);

    // Create array matching the sheet's column order
    const rowData = new Array(headers.length).fill(''); // Initialize with empty strings

    // Fill in the data we have
    rowData[idIndex] = newId;
    rowData[licensePlateIndex] = vehicleData.licensePlate;
    rowData[vehicleTypeIndex] = vehicleData.vehicleType;
    rowData[serviceTypeIndex] = vehicleData.serviceType;
    rowData[modelIndex] = vehicleData.modelYear;
    rowData[colorIndex] = vehicleData.color;
    rowData[vinNumberIndex] = vehicleData.vinNumber;
    rowData[currentKmIndex] = vehicleData.currentKm;
    rowData[vehicleStatusIndex] = vehicleData.vehicleStatus;

    // Add a new row
    vehiclesSheet.appendRow(rowData);

    Logger.log('Vehicle added successfully with ID:', newId);

    return {
      status: 'success',
      message: 'Vehicle added successfully',
      data: {
        id: newId,
        licensePlate: vehicleData.licensePlate,
        vehicleType: vehicleData.vehicleType,
        serviceType: vehicleData.serviceType,
        model: vehicleData.modelYear,
        color: vehicleData.color,
        vinNumber: vehicleData.vinNumber,
        currentKm: vehicleData.currentKm,
        vehicleStatus: vehicleData.vehicleStatus
      }
    };
  } catch (error) {
    Logger.log('Error in addVehicle: ' + error.toString());
    return { status: 'error', message: error.toString() };
  }
}

// Update an existing vehicle
function updateVehicle(vehicleData) {
  try {
    Logger.log('Update Vehicle Data received: ' + JSON.stringify(vehicleData));

    if (!vehicleData || !vehicleData.id) {
      // Try vehicleId if id is not present
      if (vehicleData.vehicleId) {
        vehicleData.id = vehicleData.vehicleId;
      } else {
        return { status: 'error', message: 'Vehicle ID is required' };
      }
    }

    const { vehiclesSheet } = getSheets();
    const data = vehiclesSheet.getDataRange().getValues();
    const headers = verifyColumnNames(vehiclesSheet); // Verify column names match

    Logger.log('Sheet Headers: ' + JSON.stringify(headers));

    // Find column indexes for required fields
    const idIndex = headers.indexOf(DASHBOARD_COLUMNS.VEHICLE_ID);
    const licensePlateIndex = headers.indexOf(DASHBOARD_COLUMNS.LICENSE_PLATE);
    const vehicleTypeIndex = headers.indexOf(DASHBOARD_COLUMNS.VEHICLE_TYPE);
    const serviceTypeIndex = headers.indexOf(DASHBOARD_COLUMNS.SERVICE_TYPE);
    const modelIndex = headers.indexOf(DASHBOARD_COLUMNS.MODEL);
    const colorIndex = headers.indexOf(DASHBOARD_COLUMNS.COLOR);
    const vinNumberIndex = headers.indexOf(DASHBOARD_COLUMNS.VIN_NUMBER);
    const currentKmIndex = headers.indexOf(DASHBOARD_COLUMNS.CURRENT_KM);
    const vehicleStatusIndex = headers.indexOf(DASHBOARD_COLUMNS.VEHICLE_STATUS);

    Logger.log('Column Indexes:', {
      id: idIndex,
      licensePlate: licensePlateIndex,
      vehicleType: vehicleTypeIndex,
      serviceType: serviceTypeIndex,
      model: modelIndex,
      color: colorIndex,
      vinNumber: vinNumberIndex,
      currentKm: currentKmIndex,
      vehicleStatus: vehicleStatusIndex
    });

    // Validate required column indexes
    if (idIndex === -1) {
      Logger.log('Available columns:', headers);
      return {
        status: 'error',
        message: `Invalid vehicles sheet structure: '${DASHBOARD_COLUMNS.VEHICLE_ID}' column not found. Available columns: ${headers.join(', ')}`
      };
    }

    // Find the vehicle by ID
    let rowIndex = -1;
    Logger.log('Looking for vehicle ID:', vehicleData.id);
    Logger.log('First few rows of data:', data.slice(0, 3));
    Logger.log('ID column index:', idIndex);

    for (let i = 1; i < data.length; i++) {
      const currentId = String(data[i][idIndex]);
      Logger.log(`Comparing row ${i}: ${currentId} with ${String(vehicleData.id)}`);
      if (currentId === String(vehicleData.id)) {
        rowIndex = i;
        break;
      }
    }

    if (rowIndex === -1) {
      return { status: 'error', message: 'Vehicle not found' };
    }

    // Validate and prepare the update data
    if (!vehicleData || typeof vehicleData !== 'object') {
      return { status: 'error', message: 'Invalid vehicle data provided' };
    }

    // Log detailed information for debugging
    Logger.log('Attempting to update vehicle: ' + JSON.stringify(vehicleData));
    Logger.log('Row data before update: ' + JSON.stringify(data[rowIndex]));
    logColumnNames(); // Log column names to verify match

    // Prepare the update range and values
    const updateRange = vehiclesSheet.getRange(rowIndex + 1, 1, 1, headers.length);
    // Log headers and their indexes
    Logger.log('Headers and their indexes:');
    headers.forEach((header, idx) => {
      Logger.log(`${idx}: ${header}`);
    });

    const newValues = headers.map((header, index) => {
      if (index === idIndex) {
        Logger.log(`Setting ID: ${vehicleData.id}`);
        return vehicleData.id;
      }

      // Get the matching column key
      const columnKey = Object.entries(DASHBOARD_COLUMNS)
        .find(([_, value]) => value === header)?.[0];

      // Log the mapping
      Logger.log(`Processing column: ${header}`);
      Logger.log(`Found column key: ${columnKey}`);
      Logger.log(`Current value: ${data[rowIndex][index]}`);

      // Get the new value from vehicleData based on the header
      let newValue;

      // First try to get value using the exact column name
      newValue = vehicleData[header];

      // If not found, try the constant mapping
      if (newValue === undefined) {
        switch(header) {
          case 'License Plate': newValue = vehicleData.licensePlate; break;
          case 'Vehicle Type': newValue = vehicleData.vehicleType; break;
          case 'Service Type': newValue = vehicleData.serviceType; break;
          case 'Model': newValue = vehicleData.model; break;
          case 'Color': newValue = vehicleData.color; break;
          case 'VIN Number': newValue = vehicleData.vinNumber; break;
          case 'Current Km': newValue = vehicleData.currentKm; break;
          case 'Vehicle Status': newValue = vehicleData.status; break;
          case 'Inactive': newValue = vehicleData.inactive; break;
          case 'Last Maintenance Km': newValue = vehicleData.lastMaintenanceKm; break;
          case 'Last Maintenance Date': newValue = vehicleData.lastMaintenanceDate; break;
          case 'Next Maintenance Km': newValue = vehicleData.nextMaintenanceKm; break;
          case 'Km to next maintenance': newValue = vehicleData.kmToNextMaintenance; break;
          case 'Last tire change Km': newValue = vehicleData.lastTireChangeKm; break;
          case 'Last tire change Data': newValue = vehicleData.lastTireChangeDate; break;
          case 'Next Tire Change Km': newValue = vehicleData.nextTireChangeKm; break;
          case 'Km left for tire change': newValue = vehicleData.kmLeftForTireChange; break;
          case 'License Renewal Date': newValue = vehicleData.licenseRenewalDate; break;
          case 'Days to renew license': newValue = vehicleData.daysToRenewLicense; break;
          case 'Insurance Expiry Date': newValue = vehicleData.insuranceExpiryDate; break;
          case 'Branch': newValue = vehicleData.branch; break;
          case 'Current Location': newValue = vehicleData.currentLocation; break;
          case 'Driver Name': newValue = vehicleData.driverName; break;
          case 'Driver Contact': newValue = vehicleData.driverContact; break;
          case 'Notes': newValue = vehicleData.notes; break;
        }
      }

      Logger.log(`Setting ${header}: ${newValue}`);

      // Always use the new value from vehicleData if it exists in the update
      return newValue !== undefined ? newValue : data[rowIndex][index];
    });

    // Update all fields at once
    updateRange.setValues([newValues]);

    return {
      status: 'success',
      message: 'Vehicle updated successfully',
      data: vehicleData
    };
  } catch (error) {
    Logger.log('Error in updateVehicle: ' + error.toString());
    return { status: 'error', message: error.toString() };
  }
}

// Delete a vehicle
function deleteVehicle(vehicleId) {
  try {
    const { vehiclesSheet } = getSheets();
    const data = vehiclesSheet.getDataRange().getValues();
    const headers = data[0];

    // Find column index for ID
    const idIndex = headers.indexOf('ID');

    // Validate column index
    if (idIndex === -1) {
      return { status: 'error', message: 'Invalid vehicles sheet structure' };
    }

    // Find the vehicle by ID
    for (let i = 1; i < data.length; i++) {
      if (data[i][idIndex] === vehicleId) {
        // Delete the row
        vehiclesSheet.deleteRow(i + 1);

        // Update related drivers
        updateRelatedDrivers(vehicleId);

        // Delete related maintenance records
        deleteRelatedMaintenance(vehicleId);

        // Delete related fuel records
        deleteRelatedFuel(vehicleId);

        return {
          status: 'success',
          message: 'Vehicle deleted successfully'
        };
      }
    }

    return { status: 'error', message: 'Vehicle not found' };
  } catch (error) {
    Logger.log('Error in deleteVehicle: ' + error.toString());
    return { status: 'error', message: error.toString() };
  }
}

// Update drivers related to a deleted vehicle
function updateRelatedDrivers(vehicleId) {
  try {
    const { driversSheet } = getSheets();
    const data = driversSheet.getDataRange().getValues();
    const headers = data[0];

    // Find column index for vehicle
    const vehicleIndex = headers.indexOf('Vehicle');

    // Validate column index
    if (vehicleIndex === -1) {
      Logger.log('Invalid drivers sheet structure');
      return;
    }

    // Find drivers related to the vehicle
    for (let i = 1; i < data.length; i++) {
      if (data[i][vehicleIndex] === vehicleId) {
        // Update the driver by removing the vehicle association
        driversSheet.getRange(i + 1, vehicleIndex + 1).setValue('');
      }
    }
  } catch (error) {
    Logger.log('Error in updateRelatedDrivers: ' + error.toString());
  }
}

// Delete maintenance records related to a deleted vehicle
function deleteRelatedMaintenance(vehicleId) {
  try {
    const { maintenanceSheet } = getSheets();
    const data = maintenanceSheet.getDataRange().getValues();
    const headers = data[0];

    // Find column index for vehicle
    const vehicleIndex = headers.indexOf('Vehicle');

    // Validate column index
    if (vehicleIndex === -1) {
      Logger.log('Invalid maintenance sheet structure');
      return;
    }

    // Find and delete maintenance records related to the vehicle
    for (let i = data.length - 1; i >= 1; i--) {
      if (data[i][vehicleIndex] === vehicleId) {
        maintenanceSheet.deleteRow(i + 1);
      }
    }
  } catch (error) {
    Logger.log('Error in deleteRelatedMaintenance: ' + error.toString());
  }
}

// Delete fuel records related to a deleted vehicle
function deleteRelatedFuel(vehicleId) {
  try {
    const { fuelSheet } = getSheets();
    const data = fuelSheet.getDataRange().getValues();
    const headers = data[0];

    // Find column index for vehicle
    const vehicleIndex = headers.indexOf('Vehicle');

    // Validate column index
    if (vehicleIndex === -1) {
      Logger.log('Invalid fuel sheet structure');
      return;
    }

    // Find and delete fuel records related to the vehicle
    for (let i = data.length - 1; i >= 1; i--) {
      if (data[i][vehicleIndex] === vehicleId) {
        fuelSheet.deleteRow(i + 1);
      }
    }
  } catch (error) {
    Logger.log('Error in deleteRelatedFuel: ' + error.toString());
  }
}

// Add a new maintenance record
function addMaintenance(maintenanceData) {
  try {
    const { maintenanceSheet } = getSheets();

    // Parse data parameter if it's a string
    let parsedData = maintenanceData;
    if (maintenanceData.data && typeof maintenanceData.data === 'string') {
      try {
        parsedData = JSON.parse(maintenanceData.data);
      } catch (e) {
        Logger.log('Error parsing data parameter: ' + e.toString());
        return { status: 'error', message: 'Error parsing data: ' + e.toString() };
      }
    }

    // Log the received data for debugging
    Logger.log('Received maintenance data: ' + JSON.stringify(parsedData));

    // Validate required fields
    if (!parsedData['Vehicle ID'] || !parsedData['Service Date'] || !parsedData['Service Type']) {
      return { status: 'error', message: 'Missing required fields: Vehicle ID, Service Date, or Service Type' };
    }

    // Create a unique ID if not provided
    const maintenanceId = parsedData['Maintenance ID'] || ('M-' + Date.now());

    // Add a new row with the correct column structure matching Google Sheets
    maintenanceSheet.appendRow([
      maintenanceId,                                // Maintenance ID
      parsedData['Vehicle ID'],                    // Vehicle ID
      parsedData['License Plate'] || '',           // License Plate
      parsedData['Service Type'],                  // Service Type
      parsedData['Service Center'] || '',          // Service Center
      parsedData['Odometer Reading'] || '',        // Odometer Reading
      parsedData['Parts Cost'] || 0,               // Parts Cost
      parsedData['Labor Cost'] || 0,               // Labor Cost
      parsedData['Total Cost'] || 0,               // Total Cost
      parsedData['Service Date'],                  // Service Date
      parsedData['Next Service Date'] || '',       // Next Service Date
      parsedData['Notes'] || ''                    // Notes
    ]);

    // Update the vehicle's last maintenance date if that function exists
    if (typeof updateVehicleLastMaintenance === 'function') {
      updateVehicleLastMaintenance(parsedData['Vehicle ID'], parsedData['Service Date']);
    }

    return {
      status: 'success',
      message: 'Maintenance record added successfully',
      data: { ...parsedData, 'Maintenance ID': maintenanceId }
    };
  } catch (error) {
    Logger.log('Error in addMaintenance: ' + error.toString());
    return { status: 'error', message: error.toString() };
  }
}

// Update a vehicle's last maintenance date
function updateVehicleLastMaintenance(vehicleId, date) {
  try {
    const { vehiclesSheet } = getSheets();
    const data = vehiclesSheet.getDataRange().getValues();
    const headers = data[0];

    // Find column indexes
    const idIndex = headers.indexOf('ID');
    const lastMaintenanceIndex = headers.indexOf('LastMaintenance');
    const statusIndex = headers.indexOf('Status');

    // Validate column indexes
    if (idIndex === -1 || lastMaintenanceIndex === -1 || statusIndex === -1) {
      Logger.log('Invalid vehicles sheet structure');
      return;
    }

    // Find the vehicle and update the last maintenance date
    for (let i = 1; i < data.length; i++) {
      if (data[i][idIndex] === vehicleId) {
        vehiclesSheet.getRange(i + 1, lastMaintenanceIndex + 1).setValue(date);

        // If the vehicle is in maintenance, change its status to active
        if (data[i][statusIndex] === 'maintenance') {
          vehiclesSheet.getRange(i + 1, statusIndex + 1).setValue('active');
        }

        break;
      }
    }
  } catch (error) {
    Logger.log('Error in updateVehicleLastMaintenance: ' + error.toString());
  }
}

// Update an existing maintenance record
function updateMaintenance(maintenanceData) {
  try {
    const { maintenanceSheet } = getSheets();
    const data = maintenanceSheet.getDataRange().getValues();
    const headers = data[0];

    // Find column indexes
    const idIndex = headers.indexOf('ID');
    const vehicleIndex = headers.indexOf('Vehicle');
    const dateIndex = headers.indexOf('Date');
    const typeIndex = headers.indexOf('Type');
    const nextKilometersIndex = headers.indexOf('NextKilometers');
    const notesIndex = headers.indexOf('Notes');

    // Validate column indexes
    if (idIndex === -1 || vehicleIndex === -1 || dateIndex === -1 ||
        typeIndex === -1 || nextKilometersIndex === -1 || notesIndex === -1) {
      return { status: 'error', message: 'Invalid maintenance sheet structure' };
    }

    // Find the maintenance record by ID
    for (let i = 1; i < data.length; i++) {
      if (data[i][idIndex] === maintenanceData.id) {
        // Update the row
        maintenanceSheet.getRange(i + 1, vehicleIndex + 1).setValue(maintenanceData.vehicle);
        maintenanceSheet.getRange(i + 1, dateIndex + 1).setValue(maintenanceData.date);
        maintenanceSheet.getRange(i + 1, typeIndex + 1).setValue(maintenanceData.type);
        maintenanceSheet.getRange(i + 1, nextKilometersIndex + 1).setValue(maintenanceData.nextKilometers);
        maintenanceSheet.getRange(i + 1, notesIndex + 1).setValue(maintenanceData.notes || '');

        // Update the vehicle's last maintenance date
        updateVehicleLastMaintenance(maintenanceData.vehicle, maintenanceData.date);

        return {
          status: 'success',
          message: 'Maintenance record updated successfully',
          data: maintenanceData
        };
      }
    }

    return { status: 'error', message: 'Maintenance record not found' };
  } catch (error) {
    Logger.log('Error in updateMaintenance: ' + error.toString());
    return { status: 'error', message: error.toString() };
  }
}

// Delete a maintenance record
function deleteMaintenance(maintenanceId) {
  try {
    const { maintenanceSheet } = getSheets();
    const data = maintenanceSheet.getDataRange().getValues();
    const headers = data[0];

    // Find column index for ID
    const idIndex = headers.indexOf('ID');

    // Validate column index
    if (idIndex === -1) {
      return { status: 'error', message: 'Invalid maintenance sheet structure' };
    }

    // Find the maintenance record by ID
    for (let i = 1; i < data.length; i++) {
      if (data[i][idIndex] === maintenanceId) {
        // Delete the row
        maintenanceSheet.deleteRow(i + 1);

        return {
          status: 'success',
          message: 'Maintenance record deleted successfully'
        };
      }
    }

    return { status: 'error', message: 'Maintenance record not found' };
  } catch (error) {
    Logger.log('Error in deleteMaintenance: ' + error.toString());
    return { status: 'error', message: error.toString() };
  }
}

// Add a new fuel record
function addFuel(fuelData) {
  try {
    const { fuelSheet } = getSheets();
    const data = fuelSheet.getDataRange().getValues();

    // Validate required fields
    if (!fuelData.vehicle || !fuelData.date || !fuelData.amount || !fuelData.distance) {
      return { status: 'error', message: 'Missing required fields' };
    }

    // Calculate average consumption
    const consumption = parseFloat(fuelData.distance) / parseFloat(fuelData.amount);

    // Create a unique ID
    const newId = 'f' + (data.length);

    // Add a new row
    fuelSheet.appendRow([
      newId,
      fuelData.vehicle,
      fuelData.date,
      parseFloat(fuelData.amount),
      parseFloat(fuelData.distance),
      consumption,
      parseFloat(fuelData.cost || 0)
    ]);

    return {
      status: 'success',
      message: 'Fuel record added successfully',
      data: { ...fuelData, id: newId, consumption: consumption }
    };
  } catch (error) {
    Logger.log('Error in addFuel: ' + error.toString());
    return { status: 'error', message: error.toString() };
  }
}

// Update an existing fuel record
function updateFuel(fuelData) {
  try {
    const { fuelSheet } = getSheets();
    const data = fuelSheet.getDataRange().getValues();
    const headers = data[0];

    // Find column indexes
    const idIndex = headers.indexOf('ID');
    const vehicleIndex = headers.indexOf('Vehicle');
    const dateIndex = headers.indexOf('Date');
    const amountIndex = headers.indexOf('Amount');
    const distanceIndex = headers.indexOf('Distance');
    const consumptionIndex = headers.indexOf('Consumption');
    const costIndex = headers.indexOf('Cost');

    // Validate column indexes
    if (idIndex === -1 || vehicleIndex === -1 || dateIndex === -1 ||
        amountIndex === -1 || distanceIndex === -1 || consumptionIndex === -1 || costIndex === -1) {
      return { status: 'error', message: 'Invalid fuel sheet structure' };
    }

    // Find the fuel record by ID
    for (let i = 1; i < data.length; i++) {
      if (data[i][idIndex] === fuelData.id) {
        // Calculate average consumption
        const consumption = parseFloat(fuelData.distance) / parseFloat(fuelData.amount);

        // Update the row
        fuelSheet.getRange(i + 1, vehicleIndex + 1).setValue(fuelData.vehicle);
        fuelSheet.getRange(i + 1, dateIndex + 1).setValue(fuelData.date);
        fuelSheet.getRange(i + 1, amountIndex + 1).setValue(parseFloat(fuelData.amount));
        fuelSheet.getRange(i + 1, distanceIndex + 1).setValue(parseFloat(fuelData.distance));
        fuelSheet.getRange(i + 1, consumptionIndex + 1).setValue(consumption);
        fuelSheet.getRange(i + 1, costIndex + 1).setValue(parseFloat(fuelData.cost || 0));

        return {
          status: 'success',
          message: 'Fuel record updated successfully',
          data: { ...fuelData, consumption: consumption }
        };
      }
    }

    return { status: 'error', message: 'Fuel record not found' };
  } catch (error) {
    Logger.log('Error in updateFuel: ' + error.toString());
    return { status: 'error', message: error.toString() };
  }
}

// Delete a fuel record
function deleteFuel(fuelId) {
  try {
    const { fuelSheet } = getSheets();
    const data = fuelSheet.getDataRange().getValues();
    const headers = data[0];

    // Find column index for ID
    const idIndex = headers.indexOf('ID');

    // Validate column index
    if (idIndex === -1) {
      return { status: 'error', message: 'Invalid fuel sheet structure' };
    }

    // Find the fuel record by ID
    for (let i = 1; i < data.length; i++) {
      if (data[i][idIndex] === fuelId) {
        // Delete the row
        fuelSheet.deleteRow(i + 1);

        return {
          status: 'success',
          message: 'Fuel record deleted successfully'
        };
      }
    }

    return { status: 'error', message: 'Fuel record not found' };
  } catch (error) {
    Logger.log('Error in deleteFuel: ' + error.toString());
    return { status: 'error', message: error.toString() };
  }
}

// Add a new driver
function addDriver(driverData) {
  try {
    const { driversSheet } = getSheets();
    const data = driversSheet.getDataRange().getValues();
    const headers = data[0];

    // Find column indexes
    const licenseNumberIndex = headers.indexOf('LicenseNumber');

    // Validate column index
    if (licenseNumberIndex === -1) {
      return { status: 'error', message: 'Invalid drivers sheet structure' };
    }

    // Validate required fields
    if (!driverData.name || !driverData.licenseNumber || !driverData.licenseExpiry) {
      return { status: 'error', message: 'Missing required fields' };
    }

    // Check for duplicate license number
    for (let i = 1; i < data.length; i++) {
      if (data[i][licenseNumberIndex] === driverData.licenseNumber) {
        return { status: 'error', message: 'License number already exists' };
      }
    }

    // Create a unique ID
    const newId = 'd' + (data.length);

    // Add a new row
    driversSheet.appendRow([
      newId,
      driverData.name,
      driverData.licenseNumber,
      driverData.licenseExpiry,
      driverData.vehicle || '',
      driverData.phone || ''
    ]);

    return {
      status: 'success',
      message: 'Driver added successfully',
      data: { ...driverData, id: newId }
    };
  } catch (error) {
    Logger.log('Error in addDriver: ' + error.toString());
    return { status: 'error', message: error.toString() };
  }
}

// Update an existing driver
function updateDriver(driverData) {
  try {
    const { driversSheet } = getSheets();
    const data = driversSheet.getDataRange().getValues();
    const headers = data[0];

    // Find column indexes
    const idIndex = headers.indexOf('ID');
    const nameIndex = headers.indexOf('Name');
    const licenseNumberIndex = headers.indexOf('LicenseNumber');
    const licenseExpiryIndex = headers.indexOf('LicenseExpiry');
    const vehicleIndex = headers.indexOf('Vehicle');
    const phoneIndex = headers.indexOf('Phone');

    // Validate column indexes
    if (idIndex === -1 || nameIndex === -1 || licenseNumberIndex === -1 ||
        licenseExpiryIndex === -1 || vehicleIndex === -1 || phoneIndex === -1) {
      return { status: 'error', message: 'Invalid drivers sheet structure' };
    }

    // Find the driver by ID
    for (let i = 1; i < data.length; i++) {
      if (data[i][idIndex] === driverData.id) {
        // Check for duplicate license number
        for (let j = 1; j < data.length; j++) {
          if (j !== i && data[j][licenseNumberIndex] === driverData.licenseNumber) {
            return { status: 'error', message: 'License number already exists' };
          }
        }

        // Update the row
        driversSheet.getRange(i + 1, nameIndex + 1).setValue(driverData.name);
        driversSheet.getRange(i + 1, licenseNumberIndex + 1).setValue(driverData.licenseNumber);
        driversSheet.getRange(i + 1, licenseExpiryIndex + 1).setValue(driverData.licenseExpiry);
        driversSheet.getRange(i + 1, vehicleIndex + 1).setValue(driverData.vehicle || '');
        driversSheet.getRange(i + 1, phoneIndex + 1).setValue(driverData.phone || '');

        return {
          status: 'success',
          message: 'Driver updated successfully',
          data: driverData
        };
      }
    }

    return { status: 'error', message: 'Driver not found' };
  } catch (error) {
    Logger.log('Error in updateDriver: ' + error.toString());
    return { status: 'error', message: error.toString() };
  }
}

// Delete a driver
function deleteDriver(driverId) {
  try {
    const { driversSheet } = getSheets();
    const data = driversSheet.getDataRange().getValues();
    const headers = data[0];

    // Find column index for ID
    const idIndex = headers.indexOf('ID');

    // Validate column index
    if (idIndex === -1) {
      return { status: 'error', message: 'Invalid drivers sheet structure' };
    }

    // Find the driver by ID
    for (let i = 1; i < data.length; i++) {
      if (data[i][idIndex] === driverId) {
        // Delete the row
        driversSheet.deleteRow(i + 1);

        // Update vehicles that had this driver assigned
        updateVehiclesAfterDriverDelete(driverId);

        return {
          status: 'success',
          message: 'Driver deleted successfully'
        };
      }
    }

    return { status: 'error', message: 'Driver not found' };
  } catch (error) {
    Logger.log('Error in deleteDriver: ' + error.toString());
    return { status: 'error', message: error.toString() };
  }
}

// Update vehicles after a driver is deleted
function updateVehiclesAfterDriverDelete(driverId) {
  try {
    const { vehiclesSheet } = getSheets();
    const data = vehiclesSheet.getDataRange().getValues();
    const headers = data[0];

    // Find column indexes
    const driverIndex = headers.indexOf('Driver');

    // Validate column index
    if (driverIndex === -1) {
      Logger.log('Invalid vehicles sheet structure');
      return;
    }

    // Find vehicles with the deleted driver
    for (let i = 1; i < data.length; i++) {
      if (data[i][driverIndex] === driverId) {
        // Remove the driver association
        vehiclesSheet.getRange(i + 1, driverIndex + 1).setValue('');
      }
    }
  } catch (error) {
    Logger.log('Error in updateVehiclesAfterDriverDelete: ' + error.toString());
  }
}

// Add a new user
function addUser(userData) {
  try {
    // Check if current user has permission to add users based on role
    const allowedRolesToViewUsers = [
      ROLES.SUPER_ADMIN,
      ROLES.GENERAL_MANAGER,
      ROLES.OPERATIONS_MANAGER,
      ROLES.FLEET_MANAGER,
      ROLES.FLEET_SUPERVISOR
    ];

    if (userData.currentUserRole && !allowedRolesToViewUsers.includes(userData.currentUserRole)) {
      return {
        status: 'error',
        message: 'You do not have permission to add users.'
      };
    }

    const { usersSheet } = getSheets();
    const data = usersSheet.getDataRange().getValues();
    const headers = data[0];

    // Find column indexes
    const emailIndex = headers.indexOf('Email');
    const roleIndex = headers.indexOf('Role');
    const branchIndex = headers.indexOf('Branch');

    // Validate column index
    if (emailIndex === -1) {
      return { status: 'error', message: 'Invalid users sheet structure' };
    }

    // Validate required fields
    if (!userData.name || !userData.email || !userData.password || !userData.role) {
      return { status: 'error', message: 'Missing required fields' };
    }

    // Check role-based permissions for adding users
    if (userData.currentUserRole) {
      // Only Super Admin can add Super Admin users
      if (userData.role === ROLES.SUPER_ADMIN && userData.currentUserRole !== ROLES.SUPER_ADMIN) {
        return {
          status: 'error',
          message: 'Only Super Admin can add Super Admin users.'
        };
      }

      // General Manager can only add Operations Manager and Fleet Manager
      if (userData.currentUserRole === ROLES.GENERAL_MANAGER &&
          userData.role !== ROLES.OPERATIONS_MANAGER &&
          userData.role !== ROLES.FLEET_MANAGER) {
        return {
          status: 'error',
          message: 'General Manager can only add Operations Manager and Fleet Manager users.'
        };
      }

      // Operations Manager and Fleet Manager can only add Fleet Supervisor, Fleet Staff, and Driver
      if ((userData.currentUserRole === ROLES.OPERATIONS_MANAGER || userData.currentUserRole === ROLES.FLEET_MANAGER) &&
          userData.role !== ROLES.FLEET_SUPERVISOR &&
          userData.role !== ROLES.FLEET_STAFF &&
          userData.role !== ROLES.DRIVER) {
        return {
          status: 'error',
          message: 'Operations Manager and Fleet Manager can only add Fleet Supervisor, Fleet Staff, and Driver users.'
        };
      }

      // Fleet Supervisor can only add Fleet Staff and Driver
      if (userData.currentUserRole === ROLES.FLEET_SUPERVISOR &&
          userData.role !== ROLES.FLEET_STAFF &&
          userData.role !== ROLES.DRIVER) {
        return {
          status: 'error',
          message: 'Fleet Supervisor can only add Fleet Staff and Driver users.'
        };
      }
    }

    // Check for duplicate email
    for (let i = 1; i < data.length; i++) {
      if (data[i][emailIndex] === userData.email) {
        return { status: 'error', message: 'Email already exists' };
      }
    }

    // Create a unique ID
    const newId = 'u' + (data.length);

    // Prepare row data
    const rowData = [];
    for (let i = 0; i < headers.length; i++) {
      if (headers[i] === 'ID') rowData[i] = newId;
      else if (headers[i] === 'Name') rowData[i] = userData.name;
      else if (headers[i] === 'Email') rowData[i] = userData.email;
      else if (headers[i] === 'Password') rowData[i] = userData.password; // In a real application, password should be hashed
      else if (headers[i] === 'Role') rowData[i] = userData.role;
      else if (headers[i] === 'Status') rowData[i] = 'Active';
      else if (headers[i] === 'Manager') rowData[i] = userData.manager || '';
      else if (headers[i] === 'Branch') rowData[i] = userData.branch || '';
      else rowData[i] = ''; // Default value for any other columns
    }

    // Add a new row
    usersSheet.appendRow(rowData);

    return {
      status: 'success',
      message: 'User added successfully',
      data: { ...userData, id: newId }
    };
  } catch (error) {
    Logger.log('Error in addUser: ' + error.toString());
    return { status: 'error', message: error.toString() };
  }
}

// Update an existing user
function updateUser(userData) {
  try {
    // Check if current user has permission to update users based on role
    const allowedRolesToViewUsers = [
      ROLES.SUPER_ADMIN,
      ROLES.GENERAL_MANAGER,
      ROLES.OPERATIONS_MANAGER,
      ROLES.FLEET_MANAGER,
      ROLES.FLEET_SUPERVISOR
    ];

    if (userData.currentUserRole && !allowedRolesToViewUsers.includes(userData.currentUserRole)) {
      return {
        status: 'error',
        message: 'You do not have permission to update users.'
      };
    }

    const { usersSheet } = getSheets();
    const data = usersSheet.getDataRange().getValues();
    const headers = data[0];

    // Find column indexes
    const idIndex = headers.indexOf('ID');
    const nameIndex = headers.indexOf('Name');
    const emailIndex = headers.indexOf('Email');
    const passwordIndex = headers.indexOf('Password');
    const roleIndex = headers.indexOf('Role');
    const managerIndex = headers.indexOf('Manager');
    const branchIndex = headers.indexOf('Branch');
    const statusIndex = headers.indexOf('Status');

    // Validate column indexes
    if (idIndex === -1 || nameIndex === -1 || emailIndex === -1 || roleIndex === -1) {
      return { status: 'error', message: 'Invalid users sheet structure' };
    }

    // Find the user by ID
    let userRowIndex = -1;
    let userCurrentRole = '';

    for (let i = 1; i < data.length; i++) {
      if (data[i][idIndex] === userData.id) {
        userRowIndex = i;
        userCurrentRole = data[i][roleIndex];
        break;
      }
    }

    if (userRowIndex === -1) {
      return { status: 'error', message: 'User not found' };
    }

    // Check role-based permissions for updating users
    if (userData.currentUserRole) {
      // Only Super Admin can update Super Admin users
      if (userCurrentRole === ROLES.SUPER_ADMIN && userData.currentUserRole !== ROLES.SUPER_ADMIN) {
        return {
          status: 'error',
          message: 'Only Super Admin can update Super Admin users.'
        };
      }

      // General Manager can only update Operations Manager and Fleet Manager
      if (userData.currentUserRole === ROLES.GENERAL_MANAGER &&
          userCurrentRole !== ROLES.OPERATIONS_MANAGER &&
          userCurrentRole !== ROLES.FLEET_MANAGER) {
        return {
          status: 'error',
          message: 'General Manager can only update Operations Manager and Fleet Manager users.'
        };
      }

      // Operations Manager and Fleet Manager can only update Fleet Supervisor, Fleet Staff, and Driver
      if ((userData.currentUserRole === ROLES.OPERATIONS_MANAGER || userData.currentUserRole === ROLES.FLEET_MANAGER) &&
          userCurrentRole !== ROLES.FLEET_SUPERVISOR &&
          userCurrentRole !== ROLES.FLEET_STAFF &&
          userCurrentRole !== ROLES.DRIVER) {
        return {
          status: 'error',
          message: 'Operations Manager and Fleet Manager can only update Fleet Supervisor, Fleet Staff, and Driver users.'
        };
      }

      // Fleet Supervisor can only update Fleet Staff and Driver
      if (userData.currentUserRole === ROLES.FLEET_SUPERVISOR &&
          userCurrentRole !== ROLES.FLEET_STAFF &&
          userCurrentRole !== ROLES.DRIVER) {
        return {
          status: 'error',
          message: 'Fleet Supervisor can only update Fleet Staff and Driver users.'
        };
      }

      // Only Super Admin can change roles
      if (userData.role && userData.role !== userCurrentRole && userData.currentUserRole !== ROLES.SUPER_ADMIN) {
        return {
          status: 'error',
          message: 'Only Super Admin can change user roles.'
        };
      }
    }

    // Check for duplicate email
    for (let j = 1; j < data.length; j++) {
      if (j !== userRowIndex && data[j][emailIndex] === userData.email) {
        return { status: 'error', message: 'Email already exists' };
      }
    }

    // Update the row
    usersSheet.getRange(userRowIndex + 1, nameIndex + 1).setValue(userData.name);
    usersSheet.getRange(userRowIndex + 1, emailIndex + 1).setValue(userData.email);

    // Only update password if provided
    if (userData.password && passwordIndex !== -1) {
      usersSheet.getRange(userRowIndex + 1, passwordIndex + 1).setValue(userData.password);
    }

    // Only Super Admin can change roles
    if (userData.role && (userData.currentUserRole === ROLES.SUPER_ADMIN || userData.role === userCurrentRole)) {
      usersSheet.getRange(userRowIndex + 1, roleIndex + 1).setValue(userData.role);
    }

    // Update manager if the field exists
    if (managerIndex !== -1) {
      usersSheet.getRange(userRowIndex + 1, managerIndex + 1).setValue(userData.manager || '');
    }

    // Update branch if the field exists and user has permission
    if (branchIndex !== -1 && userData.branch) {
      let canEditBranch = false;

      // Super Admin can edit all branches
      if (userData.currentUserRole === ROLES.SUPER_ADMIN) {
        canEditBranch = true;
      }
      // General Manager can edit branch for Operations Manager and Fleet Manager
      else if (userData.currentUserRole === ROLES.GENERAL_MANAGER &&
              (userCurrentRole === ROLES.OPERATIONS_MANAGER || userCurrentRole === ROLES.FLEET_MANAGER)) {
        canEditBranch = true;
      }
      // Operations Manager and Fleet Manager can edit branch for Fleet Supervisor, Fleet Staff, and Driver
      else if ((userData.currentUserRole === ROLES.OPERATIONS_MANAGER || userData.currentUserRole === ROLES.FLEET_MANAGER) &&
              (userCurrentRole === ROLES.FLEET_SUPERVISOR || userCurrentRole === ROLES.FLEET_STAFF || userCurrentRole === ROLES.DRIVER)) {
        canEditBranch = true;
      }
      // Fleet Supervisor can edit branch for Fleet Staff and Driver
      else if (userData.currentUserRole === ROLES.FLEET_SUPERVISOR &&
              (userCurrentRole === ROLES.FLEET_STAFF || userCurrentRole === ROLES.DRIVER)) {
        canEditBranch = true;
      }

      if (canEditBranch) {
        usersSheet.getRange(userRowIndex + 1, branchIndex + 1).setValue(userData.branch);
      }
    }

    // Update status if the field exists
    if (statusIndex !== -1 && userData.status) {
      usersSheet.getRange(userRowIndex + 1, statusIndex + 1).setValue(userData.status);
    }

    return {
      status: 'success',
      message: 'User updated successfully',
      data: userData
    };
  } catch (error) {
    Logger.log('Error in updateUser: ' + error.toString());
    return { status: 'error', message: error.toString() };
  }
}

// Delete a user
function deleteUser(userId, currentUserRole) {
  try {
    // Check if current user has permission to delete users based on role
    // Only Super Admin can delete users
    if (currentUserRole && currentUserRole !== ROLES.SUPER_ADMIN) {
      return {
        status: 'error',
        message: 'Only Super Admin can delete users.'
      };
    }

    const { usersSheet, vehiclesSheet } = getSheets();
    const userData = usersSheet.getDataRange().getValues();
    const vehiclesData = vehiclesSheet.getDataRange().getValues();
    const userHeaders = userData[0];
    const vehiclesHeaders = vehiclesData[0];

    // Find column indexes
    const userIdIndex = userHeaders.indexOf('ID');
    const userRoleIndex = userHeaders.indexOf('Role');
    const vehicleManagerIndex = vehiclesHeaders.indexOf('Manager');

    // Validate column indexes
    if (userIdIndex === -1 || userRoleIndex === -1) {
      return { status: 'error', message: 'Invalid sheet structure' };
    }

    // Find the user by ID
    let userIndex = -1;
    let userRole = '';
    for (let i = 1; i < userData.length; i++) {
      if (userData[i][userIdIndex] === userId) {
        userIndex = i;
        userRole = userData[i][userRoleIndex];
        break;
      }
    }

    if (userIndex === -1) {
      return { status: 'error', message: 'User not found' };
    }

    // Check if trying to delete a Super Admin
    if (userRole === ROLES.SUPER_ADMIN) {
      return {
        status: 'error',
        message: 'Cannot delete Super Admin users.'
      };
    }

    // If the user is a manager, check if they have vehicles assigned
    if (userRole === ROLES.FLEET_MANAGER || userRole === 'manager') {
      let hasVehicles = false;

      if (vehicleManagerIndex !== -1) {
        for (let i = 1; i < vehiclesData.length; i++) {
          if (vehiclesData[i][vehicleManagerIndex] === userId) {
            hasVehicles = true;
            break;
          }
        }
      }

      if (hasVehicles) {
        return {
          status: 'error',
          message: 'Cannot delete manager: They have vehicles assigned. Please reassign vehicles first.'
        };
      }
    }

    // Delete the user
    usersSheet.deleteRow(userIndex + 1);

    return {
      status: 'success',
      message: 'User deleted successfully'
    };
  } catch (error) {
    Logger.log('Error in deleteUser: ' + error.toString());
    return { status: 'error', message: error.toString() };
  }
}

// Get dashboard data (using Vehicles sheet)
function getDashboardData() {
  try {
    const { vehiclesSheet } = getSheets();
    const data = vehiclesSheet.getDataRange().getValues();
    const headers = data[0];

    // Log all available headers for debugging
    Logger.log('Available headers in vehicles sheet: ' + headers.join(', '));

    // Get column indices for all required fields with better error handling
    const columnIndexes = {};
    let missingColumns = [];

    Object.entries(DASHBOARD_COLUMNS).forEach(([_, columnName]) => {
      const index = headers.indexOf(columnName);
      if (index === -1) {
        // Add to missing columns list instead of throwing error immediately
        missingColumns.push(columnName);
        Logger.log(`Warning: Column not found: ${columnName}`);
      } else {
        columnIndexes[columnName] = index;
      }
    });

    // Check for possible case mismatches or minor variations in column names
    if (missingColumns.length > 0) {
      // For each missing column, try to find a similar column name
      const fixedIndexes = {};
      missingColumns.forEach(missingCol => {
        // Try case-insensitive match
        for (let i = 0; i < headers.length; i++) {
          if (headers[i].toLowerCase() === missingCol.toLowerCase() ||
              headers[i].toLowerCase().includes(missingCol.toLowerCase())) {
            Logger.log(`Found possible match for "${missingCol}": "${headers[i]}"`);
            fixedIndexes[missingCol] = i;
            break;
          }
        }
      });

      // Update column indexes with the fixed ones
      Object.entries(fixedIndexes).forEach(([missingCol, index]) => {
        columnIndexes[missingCol] = index;
        // Remove from missing columns
        missingColumns = missingColumns.filter(col => col !== missingCol);
      });

      // If there are still missing columns, log a warning but continue
      if (missingColumns.length > 0) {
        Logger.log(`Warning: Some columns could not be found: ${missingColumns.join(', ')}`);
      }
    }

    Logger.log('Found column indexes:', columnIndexes);

    // Map data to vehicles array
    const vehicles = data.slice(1) // Skip header row
      .filter(row => {
        // Only include rows with vehicle ID
        const hasId = row[columnIndexes[DASHBOARD_COLUMNS.VEHICLE_ID]];
        if (!hasId) {
          Logger.log('Skipping row without vehicle ID');
          return false;
        }
        return true;
      })
      .map(row => {
        const vehicle = {};

        // First add all available standard columns
        Object.entries(DASHBOARD_COLUMNS).forEach(([key, columnName]) => {
          if (columnIndexes[columnName] !== undefined) {
            let value = row[columnIndexes[columnName]];

            // Clean up and convert values based on column name
            if (typeof value === 'string') {
              value = value.trim();
            }

            // Special logging for Vehicle Status column
            if (columnName === DASHBOARD_COLUMNS.VEHICLE_STATUS) {
              Logger.log(`Vehicle Status for ${row[columnIndexes[DASHBOARD_COLUMNS.VEHICLE_ID]]}: ${value}`);
            }

            if (columnName === DASHBOARD_COLUMNS.CURRENT_KM || columnName === DASHBOARD_COLUMNS.KM_TO_MAINTENANCE) {
              const numValue = parseInt(String(value).replace(/,/g, ''), 10);
              vehicle[columnName] = isNaN(numValue) ? 0 : numValue; // Default to 0 if NaN
            } else {
              vehicle[columnName] = value || '';
            }
          } else {
            // If column is missing, add a default empty value
            vehicle[columnName] = '';

            // Log missing Vehicle Status column specifically
            if (columnName === DASHBOARD_COLUMNS.VEHICLE_STATUS) {
              Logger.log(`WARNING: Vehicle Status column not found in sheet!`);
            }
          }
        });

        return vehicle;
      });

    Logger.log(`Processed ${vehicles.length} vehicles`);
    if (vehicles.length > 0) {
      Logger.log('Sample vehicle data:', vehicles[0]);
    }

    return {
      status: 'success',
      data: vehicles
    };
  } catch (error) {
    Logger.log('Error in getDashboardData: ' + error.toString());
    return { status: 'error', message: error.toString() };
  }
}

// Update dashboard data (using Vehicles sheet)
function updateDashboardData(updateData) {
  try {
    const { vehiclesSheet } = getSheets();
    const data = vehiclesSheet.getDataRange().getValues();
    const headers = data[0];

    // Create a map of column names to indexes
    const columnIndexes = {};
    Object.values(DASHBOARD_COLUMNS).forEach(columnName => {
      columnIndexes[columnName] = headers.indexOf(columnName);
      if (columnIndexes[columnName] === -1) {
        throw new Error(`Column not found: ${columnName}`);
      }
    });

    // Find the row to update based on Vehicle ID
    const vehicleIdIndex = columnIndexes[DASHBOARD_COLUMNS.VEHICLE_ID];
    let rowIndex = -1;

    for (let i = 1; i < data.length; i++) {
      if (data[i][vehicleIdIndex] === updateData[DASHBOARD_COLUMNS.VEHICLE_ID]) {
        rowIndex = i + 1; // Add 1 because sheet rows are 1-based
        break;
      }
    }

    if (rowIndex === -1) {
      // If vehicle not found, add new row
      const newRow = [];
      headers.forEach((header, index) => {
        const value = updateData[header] || '';
        newRow[index] = value;
      });
      vehiclesSheet.appendRow(newRow);
      return {
        status: 'success',
        message: 'Vehicle added to Vehicles sheet successfully',
        data: updateData
      };
    }

    // Update each field that is provided
    Object.entries(updateData).forEach(([columnName, value]) => {
      const columnIndex = columnIndexes[columnName];
      if (columnIndex !== undefined) {
        vehiclesSheet.getRange(rowIndex, columnIndex + 1).setValue(value);
      }
    });

    return {
      status: 'success',
      message: 'Vehicles data updated successfully',
      data: updateData
    };
  } catch (error) {
    Logger.log('Error in updateDashboardData: ' + error.toString());
    return { status: 'error', message: error.toString() };
  }
}
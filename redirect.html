<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>توجيه - نظام إدارة الأسطول</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            direction: rtl;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
            max-width: 600px;
        }
        .logo {
            width: 80px;
            height: 80px;
            background: #667eea;
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
        }
        .alert {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            color: #856404;
        }
        .btn {
            background: #667eea;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-size: 1.1rem;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            margin: 10px;
        }
        .btn:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }
        .btn-success {
            background: #28a745;
        }
        .btn-success:hover {
            background: #218838;
        }
        .code {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            margin: 10px 0;
            direction: ltr;
            text-align: left;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">
            <i class="fas fa-truck"></i>
        </div>
        <h1>🚨 خطأ في المسار</h1>
        
        <div class="alert">
            <strong><i class="fas fa-exclamation-triangle"></i> تنبيه:</strong>
            يبدو أنك تحاول الوصول للنسخة القديمة من النظام. النظام الجديد موجود في مجلد <code>frontend</code>.
        </div>
        
        <h3>📍 المسار الصحيح:</h3>
        <div class="code">
            http://localhost:3000/frontend/
        </div>
        
        <div style="margin: 30px 0;">
            <a href="frontend/" class="btn">
                <i class="fas fa-arrow-left" style="margin-left: 10px;"></i>
                انتقل للنظام الجديد
            </a>
            
            <a href="frontend/test.html" class="btn btn-success">
                <i class="fas fa-vial" style="margin-left: 10px;"></i>
                اختبار النظام
            </a>
        </div>
        
        <h3>💡 نصائح:</h3>
        <ul style="text-align: right; max-width: 400px; margin: 0 auto;">
            <li>استخدم المسار: <code>/frontend/</code></li>
            <li>بيانات الدخول: <code>admin</code> / <code>admin123</code></li>
            <li>للاختبار: <code>/frontend/test.html</code></li>
            <li>لحل المشاكل: <code>/frontend/TROUBLESHOOTING.md</code></li>
        </ul>
    </div>

    <script>
        // توجيه تلقائي بعد 3 ثوان
        setTimeout(() => {
            window.location.href = 'frontend/';
        }, 3000);
    </script>
</body>
</html>

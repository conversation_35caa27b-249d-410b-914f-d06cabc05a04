// Import required variables and functions
import { vehicles, drivers, users, currentUser, API_URL, openModal, closeModal, DASHBOARD_COLUMNS } from './script.js';
import { updateDashboard } from './dashboard.js';

// Open add/edit vehicle modal
export function openVehicleModal(vehicleId = null) {
    const modal = document.getElementById('vehicle-modal');
    if (!modal) {
        createVehicleModal();
        return;
    }

    // Update modal title
    modal.querySelector('.modal-title').innerHTML =
        vehicleId ? '<i class="fas fa-car"></i> Edit Vehicle' : '<i class="fas fa-car"></i> Add New Vehicle';

    // Reset form
    const form = modal.querySelector('form');
    form.reset();
    const vehicleIdInput = form.querySelector('#vehicle-id');
    if (vehicleId) {
        vehicleIdInput.value = vehicleId;
        vehicleIdInput.readOnly = true;
        vehicleIdInput.nextElementSibling.className = 'fas fa-lock';
    } else {
        vehicleIdInput.value = '';
        vehicleIdInput.readOnly = false;
        vehicleIdInput.nextElementSibling.className = 'fas fa-pencil-alt';
    }

    // Fill vehicle data if editing
    if (vehicleId) {
        const vehicle = vehicles.find(v => v.id === vehicleId);
        if (vehicle) {
            fillVehicleForm(vehicle);
        }
    }

    // Add event listeners for close buttons
    const closeBtn = modal.querySelector('#close-vehicle-modal');
    const cancelBtn = modal.querySelector('#cancel-vehicle-form');

    closeBtn.addEventListener('click', () => closeModal('vehicle-modal'));
    cancelBtn.addEventListener('click', () => closeModal('vehicle-modal'));

    // Open modal
    openModal('vehicle-modal');
}

// Create vehicle modal
function createVehicleModal() {
    const modalHTML = `
        <div id="vehicle-modal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title"><i class="fas fa-car"></i> Add New Vehicle</h3>
                    <button id="close-vehicle-modal" class="close-btn">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <form id="vehicle-form">
                    <div class="form-section">
                        <h4><i class="fas fa-info-circle"></i> Basic Information</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="vehicle-id"><i class="fas fa-fingerprint"></i> Vehicle ID</label>
                                <div class="input-with-icon">
                                    <input type="text" id="vehicle-id" placeholder="Enter Vehicle ID">
                                    <i class="fas fa-pencil-alt"></i>
                                </div>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="license-plate"><i class="fas fa-id-card"></i> License Plate</label>
                                <div class="input-with-icon">
                                    <input type="text" id="license-plate" required placeholder="Enter license plate number">
                                    <i class="fas fa-pencil-alt"></i>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="vehicle-type"><i class="fas fa-truck"></i> Vehicle Type</label>
                                <div class="input-with-icon">
                                    <input type="text" id="vehicle-type" placeholder="Enter vehicle type">
                                    <i class="fas fa-pencil-alt"></i>
                                </div>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="service-type"><i class="fas fa-cogs"></i> Service Type</label>
                                <div class="input-with-icon">
                                    <input type="text" id="service-type" placeholder="Enter service type">
                                    <i class="fas fa-pencil-alt"></i>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="model"><i class="fas fa-calendar"></i> Model</label>
                                <div class="input-with-icon">
                                    <input type="text" id="model" placeholder="Enter model year">
                                    <i class="fas fa-pencil-alt"></i>
                                </div>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="color"><i class="fas fa-palette"></i> Color</label>
                                <div class="input-with-icon">
                                    <input type="text" id="color" placeholder="Enter vehicle color">
                                    <i class="fas fa-pencil-alt"></i>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="vin-number"><i class="fas fa-barcode"></i> VIN Number</label>
                                <div class="input-with-icon">
                                    <input type="text" id="vin-number" placeholder="Enter VIN number">
                                    <i class="fas fa-pencil-alt"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-section">
                        <h4><i class="fas fa-tachometer-alt"></i> Status & Metrics</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="current-km"><i class="fas fa-road"></i> Current Km</label>
                                <div class="input-with-icon">
                                    <input type="text" id="current-km" placeholder="Enter current kilometers">
                                    <i class="fas fa-pencil-alt"></i>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="vehicle-status"><i class="fas fa-info-circle"></i> Vehicle Status</label>
                                <div class="input-with-icon">
                                    <input type="text" id="vehicle-status" placeholder="Enter vehicle status">
                                    <i class="fas fa-pencil-alt"></i>
                                </div>
                            </div>
                        </div>
                        <div class="form-group checkbox-group">
                            <label for="inactive">
                                <input type="checkbox" id="inactive">
                                <span>Inactive</span>
                            </label>
                        </div>
                    </div>

                    <div class="form-section">
                        <h4><i class="fas fa-wrench"></i> Maintenance Information</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="last-maintenance-km"><i class="fas fa-tools"></i> Last Maintenance Km</label>
                                <div class="input-with-icon">
                                    <input type="text" id="last-maintenance-km" placeholder="Enter last maintenance km">
                                    <i class="fas fa-pencil-alt"></i>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="last-maintenance-date"><i class="fas fa-calendar-alt"></i> Last Maintenance Date</label>
                                <div class="input-with-icon">
                                    <input type="date" id="last-maintenance-date">
                                    <i class="fas fa-calendar-alt"></i>
                                </div>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="next-maintenance-km"><i class="fas fa-tools"></i> Next Maintenance Km</label>
                                <div class="input-with-icon">
                                    <input type="text" id="next-maintenance-km" placeholder="Enter next maintenance km">
                                    <i class="fas fa-pencil-alt"></i>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="km-to-maintenance"><i class="fas fa-road"></i> Km to Next Maintenance</label>
                                <div class="input-with-icon">
                                    <input type="text" id="km-to-maintenance" placeholder="Enter km to next maintenance">
                                    <i class="fas fa-pencil-alt"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-section">
                        <h4><i class="fas fa-circle"></i> Tire Information</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="last-tire-change-km"><i class="fas fa-circle"></i> Last Tire Change Km</label>
                                <div class="input-with-icon">
                                    <input type="text" id="last-tire-change-km" placeholder="Enter last tire change km">
                                    <i class="fas fa-pencil-alt"></i>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="last-tire-change-date"><i class="fas fa-calendar-alt"></i> Last Tire Change Date</label>
                                <div class="input-with-icon">
                                    <input type="date" id="last-tire-change-date">
                                    <i class="fas fa-calendar-alt"></i>
                                </div>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="next-tire-change-km"><i class="fas fa-circle"></i> Next Tire Change Km</label>
                                <div class="input-with-icon">
                                    <input type="text" id="next-tire-change-km" placeholder="Enter next tire change km">
                                    <i class="fas fa-pencil-alt"></i>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="km-to-tire-change"><i class="fas fa-road"></i> Km to Tire Change</label>
                                <div class="input-with-icon">
                                    <input type="text" id="km-to-tire-change" placeholder="Enter km to tire change">
                                    <i class="fas fa-pencil-alt"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-section">
                        <h4><i class="fas fa-calendar-check"></i> Important Dates</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="license-renewal-date"><i class="fas fa-calendar-alt"></i> License Renewal Date</label>
                                <div class="input-with-icon">
                                    <input type="date" id="license-renewal-date">
                                    <i class="fas fa-calendar-alt"></i>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="insurance-expiry-date"><i class="fas fa-shield-alt"></i> Insurance Expiry Date</label>
                                <div class="input-with-icon">
                                    <input type="date" id="insurance-expiry-date">
                                    <i class="fas fa-calendar-alt"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-section">
                        <h4><i class="fas fa-building"></i> Location & Branch</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="branch"><i class="fas fa-code-branch"></i> Branch</label>
                                <div class="input-with-icon">
                                    <input type="text" id="branch" placeholder="Enter branch name">
                                    <i class="fas fa-pencil-alt"></i>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="current-location"><i class="fas fa-map-marker-alt"></i> Current Location</label>
                                <div class="input-with-icon">
                                    <input type="text" id="current-location" placeholder="Enter current location">
                                    <i class="fas fa-pencil-alt"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-section">
                        <h4><i class="fas fa-user"></i> Driver Information</h4>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="assigned-driver"><i class="fas fa-user"></i> Assigned Driver</label>
                                <div class="input-with-icon">
                                    <input type="text" id="assigned-driver" placeholder="Enter driver name">
                                    <i class="fas fa-pencil-alt"></i>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="driver-contact"><i class="fas fa-phone"></i> Driver Contact</label>
                                <div class="input-with-icon">
                                    <input type="text" id="driver-contact" placeholder="Enter driver contact">
                                    <i class="fas fa-pencil-alt"></i>
                                </div>
                            </div>
                        </div>
                    </div>



                    <div class="form-section">
                        <h4><i class="fas fa-sticky-note"></i> Additional Information</h4>
                        <div class="form-group">
                            <label for="notes"><i class="fas fa-comment"></i> Notes</label>
                            <div class="input-with-icon">
                                <textarea id="notes" placeholder="Enter any additional notes"></textarea>
                                <i class="fas fa-pencil-alt"></i>
                            </div>
                        </div>
                    </div>

                    <div class="form-section services-section">
                        <h4><i class="fas fa-clock"></i> Upcoming Services</h4>
                        <ul id="upcoming-services-list"></ul>
                    </div>
                    <div class="modal-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Save Vehicle
                        </button>
                        <button type="button" id="cancel-vehicle-form" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Cancel
                        </button>
                    </div>
                </form>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHTML);
    document.getElementById('vehicle-form').addEventListener('submit', handleVehicleSubmit);

    // After modal is created, call openVehicleModal again to initialize it
    setTimeout(() => openVehicleModal(), 0);
}

// Fill vehicle form with vehicle data
function fillVehicleForm(vehicle) {
    if (!vehicle) return;

    const form = document.getElementById('vehicle-form');
    if (!form) return;

    // Helper function to safely set form values
    const setFormValue = (selector, value, defaultValue = '') => {
        const element = form.querySelector(selector);
        if (element) {
            element.value = value || defaultValue;
        }
    };

    // Fill in the form fields with vehicle data
    setFormValue('#vehicle-id', vehicle.id, 'Not assigned');
    setFormValue('#license-plate', vehicle['License Plate']);
    setFormValue('#vehicle-type', vehicle['Vehicle Type']);
    setFormValue('#service-type', vehicle['Service Type']);
    setFormValue('#model', vehicle['Model']);
    setFormValue('#color', vehicle['Color']);
    setFormValue('#vin-number', vehicle['VIN Number']);
    setFormValue('#current-km', vehicle['Current Km']);
    setFormValue('#vehicle-status', vehicle['Vehicle Status']);

    // Handle checkbox separately
    const inactiveCheckbox = form.querySelector('#inactive');
    if (inactiveCheckbox) {
        inactiveCheckbox.checked = vehicle['Vehicle Status']?.toLowerCase() === 'inactive';
    }

    setFormValue('#last-maintenance-km', vehicle['Last Maintenance Km']);
    setFormValue('#last-maintenance-date', vehicle['Last Maintenance Date']);
    setFormValue('#next-maintenance-km', vehicle['Next Maintenance Km']);
    setFormValue('#km-to-maintenance', vehicle['Km to Maintenance']);
    setFormValue('#last-tire-change-km', vehicle['Last Tire Change Km']);
    setFormValue('#last-tire-change-date', vehicle['Last Tire Change Date']);
    setFormValue('#next-tire-change-km', vehicle['Next Tire Change Km']);
    setFormValue('#km-to-tire-change', vehicle['Km to Tire Change']);
    setFormValue('#license-renewal-date', vehicle['License Renewal Date']);
    setFormValue('#assigned-driver', vehicle['Driver Name']);
    setFormValue('#driver-contact', vehicle['Driver Contact']);
    setFormValue('#insurance-expiry-date', vehicle['Insurance Expiry Date']);
    setFormValue('#branch', vehicle['Branch']);
    setFormValue('#current-location', vehicle['Current Location']);
    setFormValue('#notes', vehicle['Notes']);

    // Update current location
    setFormValue('#current-location', vehicle['Current Location'] || vehicle.location || '');

    // Update upcoming services list if the function exists
    if (typeof updateUpcomingServicesList === 'function') {
        updateUpcomingServicesList(vehicle);
    }
}

// No longer needed since we're using text inputs instead of select dropdowns
function updateVehicleFormLists() {
    // Function is now empty as we no longer use select dropdowns
    return;
}

// Update upcoming services list
function updateUpcomingServicesList(vehicle) {
    const servicesList = document.getElementById('upcoming-services-list');
    const currentKm = parseInt(vehicle['Current Km']) || 0;
    const services = [];

    // Check maintenance service
    const nextMaintenanceKm = parseInt(vehicle['Next Maintenance Km']) || 0;
    if (nextMaintenanceKm > 0) {
        const kmToMaintenance = nextMaintenanceKm - currentKm;
        if (kmToMaintenance <= 1000) {
            services.push({
                type: 'Maintenance',
                kmLeft: kmToMaintenance
            });
        }
    }

    // Check tire change service
    const nextTireChangeKm = parseInt(vehicle['Next Tire Change Km']) || 0;
    if (nextTireChangeKm > 0) {
        const kmToTireChange = nextTireChangeKm - currentKm;
        if (kmToTireChange <= 1000) {
            services.push({
                type: 'Tire Change',
                kmLeft: kmToTireChange
            });
        }
    }

    // Check license renewal
    const licenseRenewalDate = new Date(vehicle['License Renewal Date']);
    const today = new Date();
    const daysToRenewal = Math.floor((licenseRenewalDate - today) / (1000 * 60 * 60 * 24));
    if (daysToRenewal <= 30) {
        services.push({
            type: 'License Renewal',
            daysLeft: daysToRenewal
        });
    }

    // Check insurance expiry
    const insuranceExpiryDate = new Date(vehicle['Insurance Expiry Date']);
    const daysToInsuranceExpiry = Math.floor((insuranceExpiryDate - today) / (1000 * 60 * 60 * 24));
    if (daysToInsuranceExpiry <= 30) {
        services.push({
            type: 'Insurance Renewal',
            daysLeft: daysToInsuranceExpiry
        });
    }

    // Update the services list in the modal
    servicesList.innerHTML = services.length ?
        services.map(service => `
            <li class="upcoming-service">
                <span class="service-type">${service.type}</span>
                <span class="service-due">${
                    service.kmLeft !== undefined ?
                    `${service.kmLeft} كم متبقية` :
                    `${service.daysLeft} يوم متبقي`
                }</span>
            </li>
        `).join('') :
        '<li>لا توجد خدمات قادمة</li>';
}

// Handle vehicle form submission
export async function handleVehicleSubmit(event) {
    event.preventDefault();

    const form = event.target;
    const vehicleData = {
        "Vehicle ID": form.querySelector('#vehicle-id').value,
        "License Plate": form.querySelector('#license-plate').value,
        "Vehicle Type": form.querySelector('#vehicle-type').value,
        "Service Type": form.querySelector('#service-type').value,
        "Model": form.querySelector('#model').value,
        "Color": form.querySelector('#color').value,
        "VIN Number": form.querySelector('#vin-number').value,
        "Current Km": form.querySelector('#current-km').value,
        "Vehicle Status": form.querySelector('#vehicle-status').value,
        "Inactive": form.querySelector('#inactive').checked,
        "Last Maintenance Km": form.querySelector('#last-maintenance-km').value,
        "Last Maintenance Date": form.querySelector('#last-maintenance-date').value,
        "Last tire change Km": form.querySelector('#last-tire-change-km').value,
        "Last tire change Data": form.querySelector('#last-tire-change-date').value,
        "License Renewal Date": form.querySelector('#license-renewal-date').value,
        "Assigned Driver": form.querySelector('#assigned-driver').value,
        "Driver Contact": form.querySelector('#driver-contact').value,
        "Insurance Expiry Date": form.querySelector('#insurance-expiry-date').value,
        "Branch": form.querySelector('#branch').value,
        "Current Location": form.querySelector('#current-location').value,
        "Notes": form.querySelector('#notes').value
    };

    try {
        // Check if this is a new vehicle and validate Vehicle ID
        if (!vehicleData['Vehicle ID']) {
            throw new Error('Vehicle ID is required for new vehicles');
        }

        // Determine if this is an add or update operation
        const existingVehicle = vehicles.find(v => v['Vehicle ID'] === vehicleData['Vehicle ID']);
        const action = existingVehicle ? 'updateVehicle' : 'addVehicle';

        // Add user role to check permissions on the server side
        if (currentUser && currentUser.role) {
            vehicleData.userRole = currentUser.role;
        }

        const response = await fetch(API_URL + '?action=' + action, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(vehicleData)
        });

        const result = await response.json();

        if (result.status === 'success') {
            if (existingVehicle) {
                const index = vehicles.findIndex(v => v['Vehicle ID'] === vehicleData['Vehicle ID']);
                if (index !== -1) vehicles[index] = result.data;
            } else {
                vehicles.push(result.data);
            }

            closeModal('vehicle-modal');
            updateDashboard();
        } else {
            throw new Error(result.message);
        }
    } catch (error) {
        console.error('Error submitting vehicle form:', error);
        alert(error.message || 'Error saving data');
    }
}
/**
 * Debug Script - سكريبت التشخيص
 * يساعد في تشخيص مشاكل تحميل الملفات والمراجع
 */

console.log('🔧 بدء تشخيص النظام...');

// فحص الملفات المطلوبة
const requiredFiles = [
    './main.js',
    './router.js',
    './css/index.css',
    './components/index.js',
    './services/index.js',
    './store/index.js',
    './utils/index.js',
    './pages/index.js',
    './data/mockData.js'
];

// فحص وجود الملفات
async function checkFiles() {
    console.log('📁 فحص الملفات المطلوبة...');
    
    for (const file of requiredFiles) {
        try {
            const response = await fetch(file);
            if (response.ok) {
                console.log(`✅ ${file} - موجود`);
            } else {
                console.error(`❌ ${file} - غير موجود (${response.status})`);
            }
        } catch (error) {
            console.error(`❌ ${file} - خطأ في التحميل:`, error.message);
        }
    }
}

// فحص المراجع في HTML
function checkHTMLReferences() {
    console.log('🔍 فحص المراجع في HTML...');
    
    // فحص scripts
    const scripts = document.querySelectorAll('script[src]');
    scripts.forEach(script => {
        const src = script.getAttribute('src');
        console.log(`📜 Script: ${src}`);
        
        // التحقق من وجود مراجع لملفات محذوفة
        if (src.includes('script.js') || src.includes('auth.js') || src.includes('dashboard.js')) {
            console.error(`⚠️ مرجع لملف محذوف: ${src}`);
        }
    });
    
    // فحص CSS
    const links = document.querySelectorAll('link[rel="stylesheet"]');
    links.forEach(link => {
        const href = link.getAttribute('href');
        console.log(`🎨 CSS: ${href}`);
    });
}

// فحص الأخطاء في Console
function setupErrorHandling() {
    console.log('🚨 إعداد معالجة الأخطاء...');
    
    // التقاط أخطاء JavaScript
    window.addEventListener('error', (event) => {
        console.error('❌ خطأ JavaScript:', {
            message: event.message,
            filename: event.filename,
            lineno: event.lineno,
            colno: event.colno,
            error: event.error
        });
    });
    
    // التقاط أخطاء Promise
    window.addEventListener('unhandledrejection', (event) => {
        console.error('❌ خطأ Promise:', event.reason);
    });
    
    // التقاط أخطاء تحميل الموارد
    window.addEventListener('error', (event) => {
        if (event.target !== window) {
            console.error('❌ خطأ تحميل مورد:', {
                type: event.target.tagName,
                source: event.target.src || event.target.href,
                message: 'فشل في التحميل'
            });
        }
    }, true);
}

// فحص Service Workers
function checkServiceWorkers() {
    console.log('🔧 فحص Service Workers...');
    
    if ('serviceWorker' in navigator) {
        navigator.serviceWorker.getRegistrations().then(registrations => {
            if (registrations.length > 0) {
                console.log('📋 Service Workers مسجلة:', registrations.length);
                registrations.forEach((registration, index) => {
                    console.log(`SW ${index + 1}:`, registration.scope);
                });
            } else {
                console.log('✅ لا توجد Service Workers مسجلة');
            }
        });
    } else {
        console.log('ℹ️ Service Workers غير مدعومة');
    }
}

// فحص Cache
function checkCache() {
    console.log('💾 فحص Cache...');
    
    if ('caches' in window) {
        caches.keys().then(cacheNames => {
            if (cacheNames.length > 0) {
                console.log('📦 Caches موجودة:', cacheNames);
                cacheNames.forEach(cacheName => {
                    caches.open(cacheName).then(cache => {
                        cache.keys().then(requests => {
                            console.log(`Cache "${cacheName}":`, requests.length, 'عنصر');
                        });
                    });
                });
            } else {
                console.log('✅ لا توجد Caches');
            }
        });
    } else {
        console.log('ℹ️ Cache API غير مدعومة');
    }
}

// فحص localStorage
function checkLocalStorage() {
    console.log('💽 فحص LocalStorage...');
    
    try {
        const keys = Object.keys(localStorage);
        if (keys.length > 0) {
            console.log('🗂️ LocalStorage يحتوي على:', keys.length, 'عنصر');
            keys.forEach(key => {
                console.log(`- ${key}:`, localStorage.getItem(key).substring(0, 50) + '...');
            });
        } else {
            console.log('✅ LocalStorage فارغ');
        }
    } catch (error) {
        console.error('❌ خطأ في الوصول لـ LocalStorage:', error);
    }
}

// تشغيل جميع الفحوصات
async function runDiagnostics() {
    console.log('🚀 بدء التشخيص الشامل...');
    
    setupErrorHandling();
    checkHTMLReferences();
    await checkFiles();
    checkServiceWorkers();
    checkCache();
    checkLocalStorage();
    
    console.log('✅ انتهى التشخيص');
}

// تشغيل التشخيص عند تحميل الصفحة
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', runDiagnostics);
} else {
    runDiagnostics();
}

// تصدير للاستخدام اليدوي
window.runDiagnostics = runDiagnostics;
window.checkFiles = checkFiles;
window.checkCache = checkCache;

console.log('💡 يمكنك تشغيل التشخيص يدوياً باستخدام: runDiagnostics()');

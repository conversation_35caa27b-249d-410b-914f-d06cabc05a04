/**
 * Authentication Module - وحدة المصادقة
 * يوفر واجهة للتفاعل مع خدمة المصادقة والتحقق من الجلسات
 */

import authService from './services/authService.js';
import { showNotification } from './utils/utility.js';

// متغيرات عامة للتوافق مع الكود القديم
export let currentUser = null;
export let isAuthenticated = false;

// دالة callback لتهيئة واجهة المستخدم
let initializeUICallback = null;

/**
 * تعيين دالة callback لتهيئة واجهة المستخدم
 */
export function setInitializeUICallback(callback) {
    initializeUICallback = callback;
}

/**
 * التحقق من حالة تسجيل الدخول
 */
export function checkLoginStatus() {
    if (authService.isUserAuthenticated()) {
        const user = authService.getCurrentUser();
        handleSuccessfulLogin(user);
    } else {
        handleLogout();
    }
}

/**
 * تسجيل الدخول
 */
export async function login(credentials) {
    try {
        const result = await authService.login(credentials);
        
        if (result.success) {
            handleSuccessfulLogin(result.user);
            return result;
        } else {
            throw new Error(result.message || 'فشل في تسجيل الدخول');
        }
    } catch (error) {
        console.error('Login error:', error);
        showNotification(error.message || 'فشل في تسجيل الدخول', 'error');
        throw error;
    }
}

/**
 * تسجيل الخروج
 */
export async function logout() {
    try {
        await authService.logout();
        handleLogout();
    } catch (error) {
        console.error('Logout error:', error);
        // حتى لو فشل تسجيل الخروج من الخادم، نقوم بتسجيل الخروج محلياً
        handleLogout();
    }
}

/**
 * معالجة تسجيل الدخول الناجح
 */
function handleSuccessfulLogin(user) {
    currentUser = user;
    isAuthenticated = true;
    
    // تحديث واجهة المستخدم
    if (initializeUICallback) {
        initializeUICallback(user);
    }
    
    // إطلاق حدث تسجيل الدخول
    document.dispatchEvent(new CustomEvent('auth:login', {
        detail: user
    }));
}

/**
 * معالجة تسجيل الخروج
 */
function handleLogout() {
    currentUser = null;
    isAuthenticated = false;
    
    // إخفاء الواجهة الرئيسية وإظهار صفحة تسجيل الدخول
    const loginContainer = document.getElementById('login-container');
    const mainContainer = document.getElementById('main-container');
    
    if (loginContainer) loginContainer.style.display = 'flex';
    if (mainContainer) mainContainer.style.display = 'none';
    
    document.body.className = 'login-page';
    
    // إطلاق حدث تسجيل الخروج
    document.dispatchEvent(new CustomEvent('auth:logout'));
}

/**
 * الحصول على المستخدم الحالي
 */
export function getCurrentUser() {
    return currentUser || authService.getCurrentUser();
}

/**
 * التحقق من حالة المصادقة
 */
export function getAuthenticationStatus() {
    return isAuthenticated || authService.isUserAuthenticated();
}

/**
 * الحصول على دور المستخدم
 */
export function getUserRole() {
    const user = getCurrentUser();
    return user?.role || null;
}

/**
 * التحقق من صلاحية محددة
 */
export function hasPermission(permission) {
    return authService.hasPermission(permission);
}

/**
 * تحديث بيانات المستخدم
 */
export function updateUserData(userData) {
    if (currentUser) {
        currentUser = { ...currentUser, ...userData };
        authService.updateUser(userData);
    }
}

/**
 * تغيير كلمة المرور
 */
export async function changePassword(oldPassword, newPassword) {
    try {
        const result = await authService.changePassword(oldPassword, newPassword);
        
        if (result.success) {
            showNotification('تم تغيير كلمة المرور بنجاح', 'success');
            return result;
        } else {
            throw new Error(result.message || 'فشل في تغيير كلمة المرور');
        }
    } catch (error) {
        console.error('Change password error:', error);
        showNotification(error.message || 'فشل في تغيير كلمة المرور', 'error');
        throw error;
    }
}

/**
 * التحقق من صحة الجلسة
 */
export async function validateSession() {
    try {
        const isValid = await authService.validateSession();
        
        if (!isValid) {
            handleLogout();
        }
        
        return isValid;
    } catch (error) {
        console.error('Session validation error:', error);
        handleLogout();
        return false;
    }
}

/**
 * الاستماع لتغييرات المصادقة
 */
export function onAuthChange(callback) {
    return authService.onAuthChange(callback);
}

// تهيئة الاستماع لأحداث المصادقة من الخدمة
authService.onAuthChange((type, userData) => {
    if (type === 'login') {
        handleSuccessfulLogin(userData);
    } else if (type === 'logout') {
        handleLogout();
    } else if (type === 'userUpdate') {
        currentUser = userData;
    }
});

// تصدير الخدمة للاستخدام المباشر
export { authService };

export default {
    login,
    logout,
    checkLoginStatus,
    getCurrentUser,
    getAuthenticationStatus,
    getUserRole,
    hasPermission,
    updateUserData,
    changePassword,
    validateSession,
    onAuthChange,
    setInitializeUICallback
};

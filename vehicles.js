// Import required variables and functions
import { vehicles, drivers, users, currentUser, API_URL, showNotification } from "./script.js"
import { openVehicleDetailsModal } from "./script.js"
import { updateDashboard } from "./dashboard.js"
import { hasPermission, PERMISSIONS } from './permissions.js'

// Helper functions
function formatNumber(num) {
  if (!num) return "0";
  return String(num).replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

function getStatusBadge(status) {
  // Log the status value to help diagnose issues
  console.log('Vehicle status value:', status, 'Type:', typeof status);

  if (!status) return '<span class="status-badge unknown">Unknown</span>';

  // Ensure status is a string and trim whitespace
  const statusStr = String(status).trim();
  const statusLower = statusStr.toLowerCase();

  // Log the processed status for debugging
  console.log('Processed status:', statusStr, 'Lowercase:', statusLower);

  // Check for exact match first (case insensitive)
  if (statusLower === "inactive") {
    console.log('Status matched: Inactive');
    return '<span class="status-badge inactive">Inactive</span>';
  } else if (statusLower === "active") {
    console.log('Status matched: Active');
    return '<span class="status-badge active">Active</span>';
  } else if (statusLower === "maintenance" || statusLower === "under maintenance") {
    console.log('Status matched: Maintenance');
    return '<span class="status-badge maintenance">Maintenance</span>';
  }

  // If no exact match, try includes
  if (statusLower.includes("inactive")) {
    console.log('Status includes: Inactive');
    return '<span class="status-badge inactive">Inactive</span>';
  } else if (statusLower.includes("active")) {
    console.log('Status includes: Active');
    return '<span class="status-badge active">Active</span>';
  } else if (statusLower.includes("maintenance")) {
    console.log('Status includes: Maintenance');
    return '<span class="status-badge maintenance">Maintenance</span>';
  } else {
    // Return the original status with a default badge if it doesn't match any known status
    console.log('Status unknown, using default');
    return `<span class="status-badge unknown">${statusStr}</span>`;
  }
}

// Cache for vehicles data
let vehiclesCache = new Map();

// Function to update vehicles cache
function updateVehiclesCache(vehicle) {
  vehiclesCache.set(vehicle["Vehicle ID"], vehicle);
  // Update the vehicles array to maintain compatibility
  const index = vehicles.findIndex(v => v["Vehicle ID"] === vehicle["Vehicle ID"]);
  if (index !== -1) {
    vehicles[index] = vehicle;
  } else {
    vehicles.push(vehicle);
  }
}

// Function to get vehicle from cache
function getVehicleFromCache(vehicleId) {
  return vehiclesCache.get(vehicleId);
}

// Function to initialize cache
function initializeCache() {
  console.log('Initializing vehicles cache with', vehicles.length, 'vehicles');
  vehiclesCache.clear();
  vehicles.forEach(vehicle => {
    // تأكد من وجود معرف للمركبة
    const vehicleId = vehicle["Vehicle ID"] || vehicle.vehicleId || vehicle.id || vehicle["vehicle_id"] || vehicle.vehicle_id;
    if (vehicleId) {
      vehiclesCache.set(vehicleId, vehicle);
      console.log(`Added vehicle to cache: ${vehicleId}`);
    } else {
      console.warn('Vehicle without ID found:', vehicle);
    }
  });
  console.log('Cache initialized with', vehiclesCache.size, 'vehicles');
}

// Store dropdown options
let dropdownOptions = {
  serviceType: [],
  vehicleType: [],
  model: [],
  color: [],
  branch: [],
  currentLocation: [],
  vehicleStatus: [] // Se llenará con datos del servidor
}

// Valores para vehicleStatus que solo se usarون إذا لم يوفر الخادم القيم
const defaultVehicleStatusOptions = [
  "Active",
  "Inactive",
  "Under Maintenance",
  "Out of Service",
  "Reserved"
];

// Function to fetch dropdown options from Google Sheets
async function fetchDropdownOptions() {
  try {
    console.log('Fetching dropdown options from server...');

    // Asegurarnos de que todas las opciones tengan al menos valores predeterminados
    // Esto evitará la advertencia "No vehicle status options received from server"
    // Configuración inicial de estados de vehículos predeterminados
    dropdownOptions.vehicleStatus = [...defaultVehicleStatusOptions];

    // También inicializamos otros valores con opciones predeterminadas
    if (!dropdownOptions.fuelType || dropdownOptions.fuelType.length === 0) {
      dropdownOptions.fuelType = ["92", "95", "Diesel", "Electric"];
    }

    if (!dropdownOptions.serviceType || dropdownOptions.serviceType.length === 0) {
      dropdownOptions.serviceType = ["Regular", "VIP", "Economy", "Luxury"];
    }

    if (!dropdownOptions.vehicleType || dropdownOptions.vehicleType.length === 0) {
      dropdownOptions.vehicleType = ["Sedan", "SUV", "Truck", "Van"];
    }

    // Ahora intentamos obtener datos del servidor
    const response = await fetch(`${API_URL}?action=getDropdownOptions&timestamp=${new Date().getTime()}`);

    // Registrar información de la respuesta para depuración
    console.log('Response status:', response.status);
    console.log('Response ok:', response.ok);

    if (response.ok) {
      const result = await response.json();

      // Mostrar la respuesta completa para depuración
      console.log('Server response:', JSON.stringify(result));

      if (result.status === 'success') {
        // Guardar los datos originales en una variable temporal
        const serverData = result.data || {};

        // Mostrar las opciones que vienen del servidor para cada campo
        console.log('Server data received:');
        Object.keys(serverData).forEach(key => {
          console.log(`${key}:`, Array.isArray(serverData[key]) ? serverData[key].length + ' items' : 'not an array');
          if (key === 'vehicleStatus' && Array.isArray(serverData[key])) {
            console.log('Vehicle Status values from server:', serverData[key]);
          }
        });

        // IMPORTANTE: Si el servidor devuelve datos válidos, reemplazar los valores predeterminados
        Object.keys(dropdownOptions).forEach(key => {
          // Si hay datos del servidor para esta clave y son válidos (array no vacío)
          if (serverData[key] && Array.isArray(serverData[key]) && serverData[key].length > 0) {
            dropdownOptions[key] = [...serverData[key]]; // Crear una copia del array
            console.log(`Replaced default values with ${dropdownOptions[key].length} server values for ${key}`);
          } else {
            console.log(`Keeping default values for ${key} as server data was empty or invalid`);
          }
        });
      } else {
        console.error('Server returned error status:', result.status, result.message || 'No error message');
      }
    } else {
      console.error('Error in server response:', response.status);
    }
  } catch (error) {
    console.error('Error fetching dropdown options:', error);
    // Los valores predeterminados ya están configurados al inicio de la función
    console.log('Using default values due to connection error');
  }

  // Registro final para verificar el estado de las opciones
  console.log('Final dropdown options:');
  Object.keys(dropdownOptions).forEach(key => {
    console.log(`${key}:`, dropdownOptions[key].length, 'items');
    if (key === 'vehicleStatus') {
      console.log('Final Vehicle Status options:', dropdownOptions.vehicleStatus);
    }
  });
}

// Función para crear un dropdown con búsqueda para Driver Name
function createSearchableDriverDropdown(selectElement, options, placeholder = "Select Driver") {
  if (!selectElement || !Array.isArray(options)) {
    console.warn('Invalid parameters for createSearchableDriverDropdown:', { selectElement, options });
    return;
  }

  // Obtener el elemento padre
  const parentElement = selectElement.parentElement;

  // Guardar el nombre y otros atributos importantes del select original
  const selectName = selectElement.getAttribute('name');
  const selectId = selectElement.getAttribute('id');
  const isRequired = selectElement.hasAttribute('required');

  // Crear el contenedor del dropdown personalizado
  const dropdownContainer = document.createElement('div');
  dropdownContainer.className = 'searchable-dropdown';
  dropdownContainer.style.position = 'relative';

  // Crear campo de entrada
  const inputField = document.createElement('input');
  inputField.type = 'text';
  inputField.className = 'form-control searchable-dropdown-input';
  inputField.placeholder = placeholder;
  inputField.setAttribute('autocomplete', 'off');
  if (isRequired) {
    inputField.setAttribute('required', '');
  }

  // Crear campo oculto para almacenar el valor real seleccionado
  const hiddenInput = document.createElement('input');
  hiddenInput.type = 'hidden';
  hiddenInput.name = selectName;
  hiddenInput.id = selectId;

  // Crear la lista desplegable
  const dropdownList = document.createElement('ul');
  dropdownList.className = 'searchable-dropdown-list';
  dropdownList.style.display = 'none';
  dropdownList.style.position = 'absolute';
  dropdownList.style.width = '100%';
  dropdownList.style.maxHeight = '200px';
  dropdownList.style.overflowY = 'auto';
  dropdownList.style.zIndex = '1000';
  dropdownList.style.backgroundColor = '#fff';
  dropdownList.style.border = '1px solid #ddd';
  dropdownList.style.borderTop = 'none';
  dropdownList.style.borderRadius = '0 0 4px 4px';
  dropdownList.style.boxShadow = '0 6px 12px rgba(0,0,0,.175)';
  dropdownList.style.padding = '0';
  dropdownList.style.margin = '0';
  dropdownList.style.listStyle = 'none';

  // Agregar una opción vacía al inicio
  const emptyOption = document.createElement('li');
  emptyOption.className = 'searchable-dropdown-item';
  emptyOption.textContent = placeholder;
  emptyOption.dataset.value = '';
  emptyOption.style.padding = '10px 15px';
  emptyOption.style.cursor = 'pointer';
  dropdownList.appendChild(emptyOption);

  // Agregar todas las opciones a la lista
  options.forEach(option => {
    if (!option) return; // Ignorar opciones vacías

    const listItem = document.createElement('li');
    listItem.className = 'searchable-dropdown-item';
    listItem.textContent = option;
    listItem.dataset.value = option;
    listItem.style.padding = '10px 15px';
    listItem.style.cursor = 'pointer';

    // Hover effect
    listItem.addEventListener('mouseenter', () => {
      listItem.style.backgroundColor = '#f5f5f5';
    });

    listItem.addEventListener('mouseleave', () => {
      listItem.style.backgroundColor = '';
    });

    dropdownList.appendChild(listItem);
  });

  // Funcionalidad de búsqueda
  inputField.addEventListener('input', () => {
    const searchText = inputField.value.toLowerCase();
    const items = dropdownList.querySelectorAll('li');

    let visibleItems = 0;

    items.forEach(item => {
      const text = item.textContent.toLowerCase();
      if (text.includes(searchText) || item === emptyOption) {
        item.style.display = '';
        visibleItems++;
      } else {
        item.style.display = 'none';
      }
    });

    // Mostrar la lista si hay elementos visibles
    if (visibleItems > 0 && searchText.length > 0) {
      dropdownList.style.display = 'block';
    } else if (searchText.length === 0) {
      dropdownList.style.display = 'block';
    } else {
      dropdownList.style.display = 'none';
    }
  });

  // Mostrar/ocultar lista al hacer clic en el input
  inputField.addEventListener('click', () => {
    dropdownList.style.display = 'block';
  });

  // Seleccionar opción al hacer clic
  dropdownList.addEventListener('click', (e) => {
    const item = e.target.closest('.searchable-dropdown-item');
    if (item) {
      inputField.value = item.textContent;
      hiddenInput.value = item.dataset.value;
      dropdownList.style.display = 'none';

      // Disparar evento change para que otros listeners se enteren
      const event = new Event('change', { bubbles: true });
      hiddenInput.dispatchEvent(event);
    }
  });

  // Cerrar la lista al hacer clic fuera
  document.addEventListener('click', (e) => {
    if (!dropdownContainer.contains(e.target)) {
      dropdownList.style.display = 'none';
    }
  });

  // Permitir navegación con teclado
  inputField.addEventListener('keydown', (e) => {
    const visibleItems = Array.from(dropdownList.querySelectorAll('li')).filter(
      item => item.style.display !== 'none'
    );

    if (visibleItems.length === 0) return;

    const currentFocus = visibleItems.findIndex(item => item.classList.contains('active'));

    // Si presiona flecha abajo
    if (e.key === 'ArrowDown') {
      e.preventDefault();

      // Si no hay elemento activo o es el último, activar el primero
      if (currentFocus === -1 || currentFocus === visibleItems.length - 1) {
        visibleItems.forEach(item => item.classList.remove('active'));
        visibleItems[0].classList.add('active');
        visibleItems[0].scrollIntoView({ block: 'nearest' });
      } else {
        // Activar el siguiente elemento
        visibleItems.forEach(item => item.classList.remove('active'));
        visibleItems[currentFocus + 1].classList.add('active');
        visibleItems[currentFocus + 1].scrollIntoView({ block: 'nearest' });
      }
    }

    // Si presiona flecha arriba
    else if (e.key === 'ArrowUp') {
      e.preventDefault();

      // Si no hay elemento activo o es el primero, activar el último
      if (currentFocus === -1 || currentFocus === 0) {
        visibleItems.forEach(item => item.classList.remove('active'));
        visibleItems[visibleItems.length - 1].classList.add('active');
        visibleItems[visibleItems.length - 1].scrollIntoView({ block: 'nearest' });
      } else {
        // Activar el elemento anterior
        visibleItems.forEach(item => item.classList.remove('active'));
        visibleItems[currentFocus - 1].classList.add('active');
        visibleItems[currentFocus - 1].scrollIntoView({ block: 'nearest' });
      }
    }

    // Si presiona Enter
    else if (e.key === 'Enter') {
      e.preventDefault();

      // Si hay un elemento activo, seleccionarlo
      if (currentFocus !== -1) {
        inputField.value = visibleItems[currentFocus].textContent;
        hiddenInput.value = visibleItems[currentFocus].dataset.value;
        dropdownList.style.display = 'none';

        // Disparar evento change
        const event = new Event('change', { bubbles: true });
        hiddenInput.dispatchEvent(event);
      }
    }

    // Si presiona Escape
    else if (e.key === 'Escape') {
      dropdownList.style.display = 'none';
    }
  });

  // Agregar los elementos al contenedor
  dropdownContainer.appendChild(inputField);
  dropdownContainer.appendChild(hiddenInput);
  dropdownContainer.appendChild(dropdownList);

  // Reemplazar el select original con nuestro dropdown personalizado
  parentElement.replaceChild(dropdownContainer, selectElement);

  return { container: dropdownContainer, input: inputField, hiddenInput, dropdownList };
}

// Function to populate dropdown
function populateDropdown(selectElement, options, placeholder = "Select an option") {
  if (!selectElement || !Array.isArray(options)) {
    console.warn('Invalid parameters for populateDropdown:', { selectElement, options })
    return
  }

  // Tratamiento especial para el dropdown de Driver Name
  if (selectElement.name === 'driverName' || selectElement.getAttribute('name') === 'driverName') {
    return createSearchableDriverDropdown(selectElement, options, placeholder);
  }

  // Comportamiento normal para otros dropdowns
  selectElement.innerHTML = `<option value="">${placeholder}</option>`
  options.forEach(option => {
    const optionElement = document.createElement('option')
    optionElement.value = option
    optionElement.textContent = option
    selectElement.appendChild(optionElement)
  })
}

// State to track if add vehicle form is visible
let isAddFormVisible = false
let editVehicleForm = null
let vehiclesTable = null
let addVehicleForm = null
let addVehicleBtn = null

// Default visible columns
const defaultVisibleColumns = {
  "License Plate": true,
  "Vehicle Type": true,
  "Current Km": true,
  "Vehicle Status": true,
  "Next Maintenance Km": true,
  "Branch": true,
  "Driver Name": true,
  "Actions": true
}

// Get visible columns from localStorage or use defaults
function getVisibleColumns() {
  try {
    const savedColumns = localStorage.getItem('vehicleTableColumns')
    return savedColumns ? JSON.parse(savedColumns) : defaultVisibleColumns
  } catch (e) {
    console.warn('Error getting visible columns:', e)
    return defaultVisibleColumns
  }
}

// Save visible columns to localStorage
function saveVisibleColumns(columns) {
  localStorage.setItem('vehicleTableColumns', JSON.stringify(columns))
}

// Toggle column visibility
function toggleColumnVisibility(columnName, isVisible) {
  const columns = getVisibleColumns();
  columns[columnName] = isVisible;
  saveVisibleColumns(columns);

  // Update table header visibility
  const headers = document.querySelectorAll('#vehicles-page table thead th');
  headers.forEach(header => {
    const headerText = header.textContent.trim();
    if (headerText === columnName) {
      header.style.display = isVisible ? '' : 'none';
    }
  });

  // Update table cells visibility
  const rows = document.querySelectorAll('#vehicles-page table tbody tr');
  rows.forEach(row => {
    const cells = row.querySelectorAll('td');
    headers.forEach((header, index) => {
      if (header.textContent.trim() === columnName && cells[index]) {
        cells[index].style.display = isVisible ? '' : 'none';
      }
    });
  });
}

// Create manage columns modal
function createManageColumnsModal() {
  const modal = document.createElement('div')
  modal.id = 'manage-columns-modal'
  modal.className = 'modal'
  modal.innerHTML = `
    <div class="modal-content">
      <div class="modal-header">
        <h3>Manage Columns</h3>
        <button class="close-btn">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="modal-body">
        <div class="columns-list"></div>
      </div>
    </div>
  `

  document.body.appendChild(modal)

  // Add event listeners
  const closeBtn = modal.querySelector('.close-btn')
  closeBtn.onclick = () => modal.style.display = 'none'

  window.onclick = (event) => {
    if (event.target === modal) {
      modal.style.display = 'none'
    }
  }

  return modal
}

// Open manage columns modal
function openManageColumnsModal() {
  let modal = document.getElementById('manage-columns-modal')
  if (!modal) {
    modal = createManageColumnsModal()
  }

  const columnsList = modal.querySelector('.columns-list')
  const visibleColumns = getVisibleColumns()
  const allColumns = Array.from(document.querySelectorAll('#vehicles-page table thead th'))
    .map(th => th.textContent.trim())

  columnsList.innerHTML = allColumns
    .map(column => `
      <div class="column-item">
        <label>
          <input type="checkbox"
            ${visibleColumns[column] ? 'checked' : ''}
            onclick="window.toggleColumnVisibility('${column}', this.checked)">
          ${column}
        </label>
      </div>
    `)
    .join('')

  modal.style.display = 'block'

  // Make toggleColumnVisibility available globally
  window.toggleColumnVisibility = toggleColumnVisibility;
}

// Function to render vehicles table - now using data directly from global vehicles array
export function renderVehiclesTable() {
  const tableBody = document.querySelector("#vehicles-page table tbody")
  if (!tableBody) {
    initializeVehiclesPage()
    return
  }

  // Get search value
  const searchValue = document.getElementById("license-plate-search")?.value.toLowerCase() || ""

  // Use vehicles array directly instead of cache
  let filteredVehicles = [...vehicles]

  // Filter vehicles based on user role and permissions
  if (currentUser) {
    // If user only has branch-specific permissions, filter vehicles by branch
    if (!hasPermission(currentUser.role, PERMISSIONS.MANAGE_VEHICLES_ALL_BRANCHES) &&
        hasPermission(currentUser.role, PERMISSIONS.MANAGE_VEHICLES_BRANCH)) {
      // Filter vehicles by user's branch
      const userBranch = currentUser.branch;
      if (userBranch) {
        filteredVehicles = filteredVehicles.filter(v => v['Branch'] === userBranch);
      }
    }
    // For backward compatibility with old role system
    else if (currentUser.role === "manager") {
      filteredVehicles = filteredVehicles.filter((v) => v.manager === currentUser.id)
    }
  }

  // Filter by license plate
  if (searchValue) {
    filteredVehicles = filteredVehicles.filter((v) => String(v["License Plate"] || '').toLowerCase().includes(searchValue))
  }

  // Get visible columns
  const visibleColumns = getVisibleColumns()

  // Update table header visibility
  const headers = document.querySelectorAll('#vehicles-page table thead th')
  headers.forEach(header => {
    const columnName = header.textContent.trim()
    header.style.display = visibleColumns[columnName] ? '' : 'none'
  })

  // Create table rows with visible columns only
  const createVisibleRow = (vehicle) => {
    // Obtener el HTML de la fila del vehículo
    const rowHTML = createVehicleRow(vehicle);

    if (!rowHTML) {
      console.error('Failed to create row HTML for vehicle:', vehicle);
      return '';
    }

    // Crear un elemento TABLE temporal para asegurar que se parseé correctamente el HTML de TR
    const tempTable = document.createElement('table');
    tempTable.innerHTML = rowHTML;

    // Obtener la fila de la tabla temporal
    const row = tempTable.querySelector('tr');

    if (!row) {
      console.error('Failed to create valid TR element for vehicle:', vehicle);
      return rowHTML; // Devolver el HTML original como fallback
    }

    // Obtener todos los elementos th del encabezado de la tabla
    const headerTexts = Array.from(headers || []).map(header => header.textContent.trim());

    try {
      // Ocultar celdas según la configuración de columnas visibles
      Array.from(row.cells || []).forEach((cell, index) => {
        // Verificar que el índice existe en headerTexts
        if (index < headerTexts.length) {
          const columnName = headerTexts[index];
          cell.style.display = visibleColumns[columnName] ? '' : 'none';
        }
      });

      return row.outerHTML;
    } catch (error) {
      console.warn('Error processing row cells for vehicle:', vehicle, error);
      return rowHTML; // Devolver el HTML original como fallback si hay algún error
    }
  }

  // Create table rows
  tableBody.innerHTML = filteredVehicles.length
    ? filteredVehicles.map(createVisibleRow).join("")
    : '<tr><td colspan="8" class="text-center">No vehicles found</td></tr>'

  // Add event listeners to buttons
  addVehicleActionListeners()

  // Add manage columns button listener
  const manageColumnsBtn = document.getElementById('manage-columns-btn')
  if (manageColumnsBtn) {
    manageColumnsBtn.onclick = openManageColumnsModal
  }
}

// Function to initialize vehicles page
async function initializeVehiclesPage() {
  // Initialize cache
  initializeCache()

  // Fetch dropdown options first
  await fetchDropdownOptions()
  console.log('Dropdown options before populating forms:', dropdownOptions)

  // No default values - only use values from Google Sheets
  const page = document.getElementById("vehicles-page")
  if (!page) return

  // استخراج القيم الفعلية من البيانات للقوائم المنسدلة
  const actualVehicleStatuses = new Set();
  const actualFuelTypes = new Set();
  const actualInactiveValues = new Set();

  // استخراج القيم من بيانات المركبات
  vehicles.forEach(vehicle => {
    // استخراج حالة المركبة
    const status = vehicle["Vehicle Status"] || vehicle.vehicleStatus || vehicle.status || vehicle["vehicle_status"] || vehicle.vehicle_status;
    if (status && typeof status === 'string' && status.trim() !== '') {
      actualVehicleStatuses.add(status);
    }

    // استخراج نوع الوقود
    const fuelType = vehicle["Fuel Type"] || vehicle.fuelType || vehicle["fuel_type"] || vehicle.fuel_type;
    if (fuelType && typeof fuelType === 'string' && fuelType.trim() !== '') {
      actualFuelTypes.add(fuelType);
    } else if (fuelType && typeof fuelType !== 'string') {
      // Si fuelType existe pero no es una cadena, convertirlo a cadena
      const fuelTypeStr = String(fuelType);
      if (fuelTypeStr.trim() !== '') {
        actualFuelTypes.add(fuelTypeStr);
      }
    }

    // استخراج قيم Inactive
    const inactive = vehicle["Inactive"] || vehicle.inactive;
    if (inactive && typeof inactive === 'string' && inactive.trim() !== '') {
      actualInactiveValues.add(inactive);
    } else if (inactive && typeof inactive !== 'string') {
      // Si inactive existe pero no es una cadena, convertirlo a cadena
      const inactiveStr = String(inactive);
      if (inactiveStr.trim() !== '') {
        actualInactiveValues.add(inactiveStr);
      }
    }
  });

  // إضافة قيم افتراضية للوقود إذا لم يكن هناك قيم
  if (actualFuelTypes.size === 0) {
    const defaultFuelTypes = ["92", "95", "Diesel", "Electric"];
    defaultFuelTypes.forEach(type => actualFuelTypes.add(type));
  }

  // إضافة قيم افتراضية لـ Inactive إذا لم يكن هناك قيم
  if (actualInactiveValues.size === 0) {
    const defaultInactiveValues = ["Yes", "No"];
    defaultInactiveValues.forEach(value => actualInactiveValues.add(value));
  }

  // تحديث القيم في dropdownOptions بالقيم الفعلية
  if (actualVehicleStatuses.size > 0) {
    console.log('Using actual vehicle statuses from data:', Array.from(actualVehicleStatuses));
    dropdownOptions.vehicleStatus = Array.from(actualVehicleStatuses);
  }

  if (actualFuelTypes.size > 0) {
    console.log('Using actual fuel types from data:', Array.from(actualFuelTypes));
    dropdownOptions.fuelType = Array.from(actualFuelTypes);
  }

  if (actualInactiveValues.size > 0) {
    console.log('Using actual inactive values from data:', Array.from(actualInactiveValues));
    dropdownOptions.inactive = Array.from(actualInactiveValues);
  }

  page.innerHTML = `
        <div class="page-header">
            <div class="search-filter">
                <div class="search-box">
                    <input type="text" id="license-plate-search" class="form-control" placeholder="Search by car number...">
                    <i class="fas fa-search"></i>
                </div>
            </div>
            <div class="button-group">
                <button id="add-vehicle-btn" class="btn btn-primary btn-icon">
                    <i class="fas fa-plus-circle"></i>
                    <span>Add New Vehicle</span>
                </button>
                <button id="edit-vehicle-btn" class="btn btn-secondary btn-icon">
                    <i class="fas fa-edit"></i>
                    <span>Edit Vehicle</span>
                </button>
                <button id="manage-columns-btn" class="btn btn-secondary btn-icon">
                    <i class="fas fa-columns"></i>
                    <span>Manage Columns</span>
                </button>
            </div>
        </div>
        <div id="vehicles-table" class="table-responsive">
            <table class="table">
                <thead>
                    <tr>
                        <th>License Plate</th>
                        <th>Service Type</th>
                        <th>Vehicle Type</th>
                        <th>Model</th>
                        <th>Color</th>
                        <th>VIN Number</th>
                        <th>Fuel Type</th>
                        <th>Current Km</th>
                        <th>Vehicle Status</th>
                        <th>Inactive</th>
                        <th>Last Maintenance Km</th>
                        <th>Last Maintenance Date</th>
                        <th>Next Maintenance Km</th>
                        <th>Km to next maintenance</th>
                        <th>Last tire change Km</th>
                        <th>Last tire change Data</th>
                        <th>Next Tire Change Km</th>
                        <th>Km left for tire change</th>
                        <th>License Renewal Date</th>
                        <th>Days to renew license</th>
                        <th>Insurance Expiry Date</th>
                        <th>Branch</th>
                        <th>Current Location</th>
                        <th>Driver Name</th>
                        <th>Driver Contact</th>
                        <th>Notes</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody></tbody>
            </table>
        </div>
        <div id="add-vehicle-form" class="form-section" style="display: none;">
            <div class="header">
                <div class="header-top">
                    <div>
                        <h1>Add New Vehicle</h1>
                        <p>Give this vehicle a name for future reference and add basic information.</p>
                    </div>
                </div>
            </div>
            <form id="vehicleForm">
                <div class="form-grid">
                    <div class="form-group">
                        <label>Vehicle ID</label>
                        <input type="text" name="vehicleId" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label>License Plate</label>
                        <input type="text" name="licensePlate" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label>Service Type</label>
                        <select name="serviceType" class="form-control">
                            <option value="">Select Service Type</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Vehicle Type</label>
                        <select name="vehicleType" class="form-control">
                            <option value="">Select Vehicle Type</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Model</label>
                        <select name="model" class="form-control">
                            <option value="">Select Model</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Color</label>
                        <select name="color" class="form-control">
                            <option value="">Select Color</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>VIN Number</label>
                        <input type="text" name="vinNumber" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label>Fuel Type</label>
                        <select name="fuelType" class="form-control">
                            <option value="">Select Fuel Type</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Current Km</label>
                        <input type="text" name="currentKm" class="form-control">
                    </div>
                    <div class="form-group">
                    <label>Vehicle Status</label>
                        <select name="vehicleStatus" class="form-control">
                            <option value="">Select Vehicle Status</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Inactive</label>
                        <select name="inactive" class="form-control">
                            <option value="">Select Status</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Last Maintenance Km</label>
                        <input type="text" name="lastMaintenanceKm" class="form-control">
                    </div>
                    <div class="form-group">
                        <label>Last Maintenance Date</label>
                        <input type="date" name="lastMaintenanceDate" class="form-control">
                    </div>
                    <div class="form-group">
                        <label>Next Maintenance Km</label>
                        <input type="text" name="nextMaintenanceKm" class="form-control">
                    </div>
                    <div class="form-group">
                        <label>Km to next maintenance</label>
                        <input type="text" name="kmToNextMaintenance" class="form-control" readonly>
                    </div>
                    <div class="form-group">
                        <label>Last Tire Change Km</label>
                        <input type="text" name="lastTireChangeKm" class="form-control">
                    </div>
                    <div class="form-group">
                        <label>Last Tire Change Date</label>
                        <input type="date" name="lastTireChangeDate" class="form-control">
                    </div>
                    <div class="form-group">
                        <label>Next Tire Change Km</label>
                        <input type="text" name="nextTireChangeKm" class="form-control">
                    </div>
                    <div class="form-group">
                        <label>Km left for tire change</label>
                        <input type="text" name="kmLeftForTireChange" class="form-control" readonly>
                    </div>
                    <div class="form-group">
                        <label>License Renewal Date</label>
                        <input type="date" name="licenseRenewalDate" class="form-control">
                    </div>
                    <div class="form-group">
                        <label>Days to renew license</label>
                        <input type="number" name="daysToRenewLicense" class="form-control">
                    </div>
                    <div class="form-group">
                        <label>Insurance Expiry Date</label>
                        <input type="date" name="insuranceExpiryDate" class="form-control">
                    </div>
                    <div class="form-group">
                        <label>Branch</label>
                        <select name="branch" class="form-control">
                            <option value="">Select Branch</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Current Location</label>
                        <select name="currentLocation" class="form-control">
                            <option value="">Select Location</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Driver Name</label>
                        <select name="driverName" class="form-control">
                            <option value="">Select Driver</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Driver Contact</label>
                        <input type="number" name="driverContact" class="form-control">
                    </div>
                    <div class="form-group">
                        <label>Notes</label>
                        <textarea name="notes" class="form-control" rows="3"></textarea>
                    </div>
                </div>
                <button type="submit" class="submit-btn" id="submitBtn">Add Vehicle</button>
            </form>
        </div>
        <div id="edit-vehicle-form" class="form-section" style="display: none;">
            <div class="header">
                <div class="header-top">
                    <div>
                        <h1>Edit Vehicle</h1>
                        <p>Update vehicle information and maintenance details.</p>
                    </div>
                </div>
            </div>
            <form id="editVehicleForm">
                <input type="hidden" name="vehicleId" id="edit-vehicle-id">
                <div class="form-grid">
                    <div class="form-group">
                        <label>License Plate</label>
                        <input type="text" name="licensePlate" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label>Service Type</label>
                        <select name="serviceType" class="form-control">
                            <option value="">Select Service Type</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Vehicle Type</label>
                        <select name="vehicleType" class="form-control">
                            <option value="">Select Vehicle Type</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Model</label>
                        <select name="model" class="form-control">
                            <option value="">Select Model</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Color</label>
                        <select name="color" class="form-control">
                            <option value="">Select Color</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>VIN Number</label>
                        <input type="text" name="vinNumber" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label>Fuel Type</label>
                        <select name="fuelType" class="form-control">
                            <option value="">Select Fuel Type</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Current Km</label>
                        <input type="text" name="currentKm" class="form-control">
                    </div>
                    <div class="form-group">
                    <label>Vehicle Status</label>
                        <select name="vehicleStatus" class="form-control">
                            <option value="">Select Vehicle Status</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Inactive</label>
                        <select name="inactive" class="form-control">
                            <option value="">Select Status</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Last Maintenance Km</label>
                        <input type="text" name="lastMaintenanceKm" class="form-control">
                    </div>
                    <div class="form-group">
                        <label>Last Maintenance Date</label>
                        <input type="date" name="lastMaintenanceDate" class="form-control">
                    </div>
                    <div class="form-group">
                        <label>Next Maintenance Km</label>
                        <input type="text" name="nextMaintenanceKm" class="form-control">
                    </div>
                    <div class="form-group">
                        <label>Km to next maintenance</label>
                        <input type="text" name="kmToNextMaintenance" class="form-control" readonly>
                    </div>
                    <div class="form-group">
                        <label>Last Tire Change Km</label>
                        <input type="text" name="lastTireChangeKm" class="form-control">
                    </div>
                    <div class="form-group">
                        <label>Last Tire Change Date</label>
                        <input type="date" name="lastTireChangeDate" class="form-control">
                    </div>
                    <div class="form-group">
                        <label>Next Tire Change Km</label>
                        <input type="text" name="nextTireChangeKm" class="form-control">
                    </div>
                    <div class="form-group">
                        <label>Km left for tire change</label>
                        <input type="text" name="kmLeftForTireChange" class="form-control" readonly>
                    </div>
                    <div class="form-group">
                        <label>License Renewal Date</label>
                        <input type="date" name="licenseRenewalDate" class="form-control">
                    </div>
                    <div class="form-group">
                        <label>Days to renew license</label>
                        <input type="number" name="daysToRenewLicense" class="form-control">
                    </div>
                    <div class="form-group">
                        <label>Insurance Expiry Date</label>
                        <input type="date" name="insuranceExpiryDate" class="form-control">
                    </div>
                    <div class="form-group">
                        <label>Branch</label>
                        <select name="branch" class="form-control">
                            <option value="">Select Branch</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Current Location</label>
                        <select name="currentLocation" class="form-control">
                            <option value="">Select Location</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Driver Name</label>
                        <select name="driverName" class="form-control">
                            <option value="">Select Driver</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Driver Contact</label>
                        <input type="number" name="driverContact" class="form-control">
                    </div>
                    <div class="form-group">
                        <label>Notes</label>
                        <textarea name="notes" class="form-control" rows="3"></textarea>
                    </div>
                </div>
                <button type="submit" class="submit-btn" id="editSubmitBtn">Update Vehicle</button>
            </form>
        </div>
    `

  // Store references to DOM elements
  vehiclesTable = document.getElementById("vehicles-table")
  addVehicleForm = document.getElementById("add-vehicle-form")
  editVehicleForm = document.getElementById("edit-vehicle-form")
  addVehicleBtn = document.getElementById("add-vehicle-btn")
  const editVehicleBtn = document.getElementById("edit-vehicle-btn")
  const vehicleForm = document.getElementById("vehicleForm")
  const editVehicleFormElement = document.getElementById("editVehicleForm")
  const searchInput = document.getElementById("license-plate-search")

  // Add event listener for search input
  searchInput.addEventListener("input", () => {
    renderVehiclesTable()
  })

  // Add event listener for edit vehicle button
  editVehicleBtn.addEventListener("click", () => {
    vehiclesTable.style.display = "none"
    addVehicleForm.style.display = "none"
    editVehicleForm.style.display = "block"
    addVehicleBtn.innerHTML = '<i class="fas fa-arrow-left"></i> Back to Vehicles'

    // Add event listener for back button when in edit mode
    addVehicleBtn.onclick = () => {
      vehiclesTable.style.display = "block"
      editVehicleForm.style.display = "none"
      addVehicleBtn.innerHTML = '<i class="fas fa-plus"></i> Add New Vehicle'
      // Reset the onclick event to original add vehicle behavior
      addVehicleBtn.onclick = null
      addVehicleBtn.addEventListener("click", toggleAddVehicleForm)
    }
  })

  // Add event listener for edit form submission
  editVehicleFormElement.addEventListener("submit", handleEditVehicleSubmit)

  // Add event listener for add vehicle button
  addVehicleBtn.addEventListener("click", toggleAddVehicleForm)

  // Add event listener for form submission
  vehicleForm.addEventListener("submit", handleAddVehicleSubmit)

  // Get all dropdowns in both forms
  const addForm = document.getElementById('vehicleForm')
  const editForm = document.getElementById('editVehicleForm')

  // Get drivers list for dropdown - use Driver Name (EN) instead of name
  const driversList = drivers.map(driver => driver['Driver Name (EN)'] || driver.name || '')

  console.log('About to populate dropdowns with options:')
  console.log('- Vehicle Status:', dropdownOptions.vehicleStatus)
  console.log('- Fuel Type:', dropdownOptions.fuelType)
  console.log('- Inactive:', dropdownOptions.inactive)

  // Populate dropdowns in add form
  populateDropdown(addForm.querySelector('[name="serviceType"]'), dropdownOptions.serviceType, "Select Service Type")
  populateDropdown(addForm.querySelector('[name="vehicleType"]'), dropdownOptions.vehicleType, "Select Vehicle Type")
  populateDropdown(addForm.querySelector('[name="model"]'), dropdownOptions.model, "Select Model")
  populateDropdown(addForm.querySelector('[name="color"]'), dropdownOptions.color, "Select Color")
  populateDropdown(addForm.querySelector('[name="fuelType"]'), dropdownOptions.fuelType || [], "Select Fuel Type")
  populateDropdown(addForm.querySelector('[name="inactive"]'), dropdownOptions.inactive || [], "Select Status")
  populateDropdown(addForm.querySelector('[name="branch"]'), dropdownOptions.branch, "Select Branch")
  populateDropdown(addForm.querySelector('[name="currentLocation"]'), dropdownOptions.currentLocation, "Select Location")
  populateDropdown(addForm.querySelector('[name="vehicleStatus"]'), dropdownOptions.vehicleStatus || [], "Select Vehicle Status")
  populateDropdown(addForm.querySelector('[name="driverName"]'), driversList, "Select Driver")

  // Populate dropdowns in edit form
  populateDropdown(editForm.querySelector('[name="serviceType"]'), dropdownOptions.serviceType, "Select Service Type")
  populateDropdown(editForm.querySelector('[name="vehicleType"]'), dropdownOptions.vehicleType, "Select Vehicle Type")
  populateDropdown(editForm.querySelector('[name="model"]'), dropdownOptions.model, "Select Model")
  populateDropdown(editForm.querySelector('[name="color"]'), dropdownOptions.color, "Select Color")
  populateDropdown(editForm.querySelector('[name="fuelType"]'), dropdownOptions.fuelType || [], "Select Fuel Type")
  populateDropdown(editForm.querySelector('[name="inactive"]'), dropdownOptions.inactive || [], "Select Status")
  populateDropdown(editForm.querySelector('[name="branch"]'), dropdownOptions.branch, "Select Branch")
  populateDropdown(editForm.querySelector('[name="currentLocation"]'), dropdownOptions.currentLocation, "Select Location")
  populateDropdown(editForm.querySelector('[name="vehicleStatus"]'), dropdownOptions.vehicleStatus || [], "Select Vehicle Status")
  populateDropdown(editForm.querySelector('[name="driverName"]'), driversList, "Select Driver")

  // Initialize the table
  renderVehiclesTable()
}

// Toggle add vehicle form visibility
function toggleAddVehicleForm() {
  isAddFormVisible = !isAddFormVisible
  vehiclesTable.style.display = isAddFormVisible ? "none" : "block"
  addVehicleForm.style.display = isAddFormVisible ? "block" : "none"
  editVehicleForm.style.display = "none"
  addVehicleBtn.innerHTML = isAddFormVisible
    ? '<i class="fas fa-arrow-left"></i> Back to Vehicles'
    : '<i class="fas fa-plus"></i> Add New Vehicle'
}

// Handle edit vehicle form submission
async function handleEditVehicleSubmit(e) {
  e.preventDefault()
  const formData = new FormData(e.target)
  const vehicleId = document.getElementById("edit-vehicle-id").value

  if (!vehicleId) {
    showNotification("Error: Vehicle ID is missing", "error")
    return
  }

  // Create an object to hold all the vehicle data
  const vehicleData = {
    action: "updateVehicle",
    id: vehicleId,
    licensePlate: formData.get("licensePlate"),
    serviceType: formData.get("serviceType"),
    vehicleType: formData.get("vehicleType"),
    model: formData.get("model"),
    color: formData.get("color"),
    vinNumber: formData.get("vinNumber"),
    fuelType: formData.get("fuelType"),
    currentKm: formData.get("currentKm"),
    status: formData.get("vehicleStatus"),
    inactive: formData.get("inactive"),
    lastMaintenanceKm: formData.get("lastMaintenanceKm"),
    lastMaintenanceDate: formData.get("lastMaintenanceDate"),
    nextMaintenanceKm: formData.get("nextMaintenanceKm"),
    kmToNextMaintenance: formData.get("kmToNextMaintenance"),
    lastTireChangeKm: formData.get("lastTireChangeKm"),
    lastTireChangeDate: formData.get("lastTireChangeDate"),
    nextTireChangeKm: formData.get("nextTireChangeKm"),
    kmLeftForTireChange: formData.get("kmLeftForTireChange"),
    licenseRenewalDate: formData.get("licenseRenewalDate"),
    daysToRenewLicense: formData.get("daysToRenewLicense"),
    insuranceExpiryDate: formData.get("insuranceExpiryDate"),
    branch: formData.get("branch"),
    currentLocation: formData.get("currentLocation"),
    driverName: formData.get("driverName"),
    driverContact: formData.get("driverContact"),
    notes: formData.get("notes"),
  }

  try {
    // Convert vehicle data to URL parameters
    const params = new URLSearchParams()
    for (const [key, value] of Object.entries(vehicleData)) {
      params.append(key, value || '')
    }

    // Send request with URL parameters using POST method
    const response = await fetch(`${API_URL}?${params.toString()}`, {
      method: 'POST',
      mode: 'no-cors',
      headers: {
        'Content-Type': 'application/json',
      }
    })

    // Since no-cors mode returns opaque response, we'll consider it successful if no error was thrown
    const result = { status: 'success' }

    if (result.status === "success") {
      showNotification("Vehicle updated successfully", "success")
      e.target.reset()

      // Switch back to table view
      vehiclesTable.style.display = "block"
      addVehicleForm.style.display = "none"
      editVehicleForm.style.display = "none"
      addVehicleBtn.innerHTML = '<i class="fas fa-plus"></i> Add New Vehicle'

      // IMPORTANT FIX: Create a consistent structure that maps both API and form fields to ensure future edits work correctly
      const updatedVehicle = {
        // Standard vehicle ID
        "Vehicle ID": vehicleId,

        // Map form fields to display fields with consistent naming
        "License Plate": vehicleData.licensePlate,
        "Service Type": vehicleData.serviceType,
        "Vehicle Type": vehicleData.vehicleType,
        "Model": vehicleData.model,
        "Color": vehicleData.color,
        "VIN Number": vehicleData.vinNumber,
        "Fuel Type": vehicleData.fuelType,
        "Current Km": vehicleData.currentKm,
        "Vehicle Status": vehicleData.status, // Map status to Vehicle Status
        "Inactive": vehicleData.inactive,
        "Last Maintenance Km": vehicleData.lastMaintenanceKm,
        "Last Maintenance Date": vehicleData.lastMaintenanceDate,
        "Next Maintenance Km": vehicleData.nextMaintenanceKm,
        "Km to next maintenance": vehicleData.kmToNextMaintenance,
        "Last tire change Km": vehicleData.lastTireChangeKm,
        "Last tire change Data": vehicleData.lastTireChangeDate,
        "Next Tire Change Km": vehicleData.nextTireChangeKm,
        "Km left for tire change": vehicleData.kmLeftForTireChange,
        "License Renewal Date": vehicleData.licenseRenewalDate,
        "Days to renew license": vehicleData.daysToRenewLicense,
        "Insurance Expiry Date": vehicleData.insuranceExpiryDate,
        "Branch": vehicleData.branch,
        "Current Location": vehicleData.currentLocation,
        "Driver Name": vehicleData.driverName,
        "Driver Contact": vehicleData.driverContact,
        "Notes": vehicleData.notes,

        // Also include snake case and camelCase versions of key fields for compatibility
        vehicleId: vehicleId,
        id: vehicleId,
        vehicle_id: vehicleId,
        licensePlate: vehicleData.licensePlate,
        license_plate: vehicleData.licensePlate,
        serviceType: vehicleData.serviceType,
        service_type: vehicleData.serviceType,
        vehicleType: vehicleData.vehicleType,
        vehicle_type: vehicleData.vehicleType,
        model: vehicleData.model,
        color: vehicleData.color,
        vinNumber: vehicleData.vinNumber,
        vin_number: vehicleData.vinNumber,
        fuelType: vehicleData.fuelType,
        fuel_type: vehicleData.fuelType,
        currentKm: vehicleData.currentKm,
        current_km: vehicleData.currentKm,
        vehicleStatus: vehicleData.status,
        vehicle_status: vehicleData.status,
        status: vehicleData.status,
        inactive: vehicleData.inactive,
        notes: vehicleData.notes,

        // Add any other field mappings needed for full compatibility
      };

      // Update the cache with the comprehensive updated vehicle data
      updateVehiclesCache(updatedVehicle);

      // Refresh the UI
      renderVehiclesTable()
      updateDashboard()
    } else {
      showNotification(`Error: ${result.message}`, "error")
    }
  } catch (error) {
    console.error("Error updating vehicle:", error)
    showNotification("Error updating vehicle: " + error.message, "error")
  }
}

// Handle add vehicle form submission
async function handleAddVehicleSubmit(e) {
  e.preventDefault()
  const formData = new FormData(e.target)
  const params = new URLSearchParams()

  for (const [key, value] of formData.entries()) {
    params.append(key, value)
  }

  params.append("action", "addVehicle")

  // Add user role to check permissions on the server side
  if (currentUser && currentUser.role) {
    params.append("userRole", currentUser.role)
  }

  try {
    const response = await fetch(`${API_URL}?${params.toString()}`, {
      method: 'POST',
      mode: 'no-cors',
      headers: {
        'Content-Type': 'application/json',
      }
    })
    // Since no-cors mode returns opaque response, we'll consider it successful if no error was thrown
    const result = { status: 'success' }

    if (response.status === 0 || response.type === 'opaque') {
      showNotification("Vehicle added successfully", "success")
      e.target.reset()

      // Create vehicle object for cache
      const newVehicle = {
        "Vehicle ID": formData.get("vehicleId"),
        "License Plate": formData.get("licensePlate"),
        "Vehicle Type": formData.get("vehicleType"),
        "Vehicle Status": formData.get("vehicleStatus"),
        "Current Km": formData.get("currentKm"),
        "Last Maintenance Date": formData.get("lastMaintenanceDate"),
        ...Object.fromEntries(formData.entries())
      };

      // Update cache with new vehicle
      updateVehiclesCache(newVehicle);

      // Switch back to table view
      isAddFormVisible = false
      vehiclesTable.style.display = "block"
      addVehicleForm.style.display = "none"
      addVehicleBtn.innerHTML = '<i class="fas fa-plus"></i> Add New Vehicle'

      // Refresh the UI
      renderVehiclesTable()
      updateDashboard()
    } else {
      showNotification(`Error: ${result.message}`, "error")
    }
  } catch (error) {
    console.error("Error adding vehicle:", error)
    showNotification("Error adding vehicle", "error")
  }
}

// Create a row in the vehicles table
function createVehicleRow(vehicle) {
  // تأكد من وجود جميع البيانات المطلوبة
  if (!vehicle) {
    console.error('Vehicle object is undefined or null');
    return '';
  }

  // طباعة بيانات المركبة للتصحيح
  console.log('Vehicle data:', JSON.stringify(vehicle));

  // استخدام جميع الأشكال المحتملة لأسماء الحقول
  const vehicleId = vehicle["Vehicle ID"] || vehicle.vehicleId || vehicle.id || vehicle["vehicle_id"] || vehicle.vehicle_id || "";
  const licensePlate = vehicle["License Plate"] || vehicle.licensePlate || vehicle["license_plate"] || vehicle.license_plate || "N/A";
  const serviceType = vehicle["Service Type"] || vehicle.serviceType || vehicle["service_type"] || vehicle.service_type || "N/A";
  const vehicleType = vehicle["Vehicle Type"] || vehicle.vehicleType || vehicle["vehicle_type"] || vehicle.vehicle_type || "N/A";
  const model = vehicle["Model"] || vehicle.model || "N/A";
  const color = vehicle["Color"] || vehicle.color || "N/A";
  const vinNumber = vehicle["VIN Number"] || vehicle.vinNumber || vehicle["vin_number"] || vehicle.vin_number || "N/A";
  const fuelType = vehicle["Fuel Type"] || vehicle.fuelType || vehicle["fuel_type"] || vehicle.fuel_type || "N/A";
  const currentKm = vehicle["Current Km"] || vehicle.currentKm || vehicle["current_km"] || vehicle.current_km || 0;

  // Enhanced handling for Vehicle Status field
  let vehicleStatus = vehicle["Vehicle Status"];

  // Log the raw vehicle status data
  console.log(`Raw Vehicle Status for ${vehicleId}:`, {
    "Vehicle Status": vehicle["Vehicle Status"],
    vehicleStatus: vehicle.vehicleStatus,
    status: vehicle.status,
    vehicle_status: vehicle["vehicle_status"]
  });

  if (vehicleStatus === undefined || vehicleStatus === null || vehicleStatus === "") {
    vehicleStatus = vehicle.vehicleStatus || vehicle.status || vehicle["vehicle_status"] || vehicle.vehicle_status || "Unknown";
  }

  // Ensure the status is a string and trim any whitespace
  vehicleStatus = String(vehicleStatus).trim();

  console.log(`Vehicle ${vehicleId} final status:`, vehicleStatus);

  const inactive = vehicle["Inactive"] || vehicle.inactive || "";
  const lastMaintenanceKm = vehicle["Last Maintenance Km"] || vehicle.lastMaintenanceKm || vehicle["last_maintenance_km"] || vehicle.last_maintenance_km || "N/A";
  const lastMaintenanceDate = vehicle["Last Maintenance Date"] || vehicle.lastMaintenanceDate || vehicle["last_maintenance_date"] || vehicle.last_maintenance_date || "N/A";
  const nextMaintenanceKm = vehicle["Next Maintenance Km"] || vehicle.nextMaintenanceKm || vehicle["next_maintenance_km"] || vehicle.next_maintenance_km || "N/A";
  const kmToNextMaintenance = vehicle["Km to next maintenance"] || vehicle.kmToNextMaintenance || vehicle["km_to_next_maintenance"] || vehicle.km_to_next_maintenance || "N/A";
  const lastTireChangeKm = vehicle["Last tire change Km"] || vehicle.lastTireChangeKm || vehicle["last_tire_change_km"] || vehicle.last_tire_change_km || "N/A";
  const lastTireChangeDate = vehicle["Last tire change Data"] || vehicle.lastTireChangeDate || vehicle["last_tire_change_date"] || vehicle.last_tire_change_date || "N/A";
  const nextTireChangeKm = vehicle["Next Tire Change Km"] || vehicle.nextTireChangeKm || vehicle["next_tire_change_km"] || vehicle.next_tire_change_km || "N/A";
  const kmLeftForTireChange = vehicle["Km left for tire change"] || vehicle.kmLeftForTireChange || vehicle["km_left_for_tire_change"] || vehicle.km_left_for_tire_change || "N/A";
  const licenseRenewalDate = vehicle["License Renewal Date"] || vehicle.licenseRenewalDate || vehicle["license_renewal_date"] || vehicle.license_renewal_date || "N/A";
  const daysToRenewLicense = vehicle["Days to renew license"] || vehicle.daysToRenewLicense || vehicle["days_to_renew_license"] || vehicle.days_to_renew_license || "N/A";
  const insuranceExpiryDate = vehicle["Insurance Expiry Date"] || vehicle.insuranceExpiryDate || vehicle["insurance_expiry_date"] || vehicle.insurance_expiry_date || "N/A";
  const branch = vehicle["Branch"] || vehicle.branch || "N/A";
  const currentLocation = vehicle["Current Location"] || vehicle.currentLocation || vehicle["current_location"] || vehicle.current_location || "N/A";
  const driverName = vehicle["Driver Name"] || vehicle.driverName || vehicle["driver_name"] || vehicle.driver_name || "N/A";
  const driverContact = vehicle["Driver Contact"] || vehicle.driverContact || vehicle["driver_contact"] || vehicle.driver_contact || "N/A";
  const notes = vehicle["Notes"] || vehicle.notes || "";

  return `
        <tr>
            <td>${licensePlate}</td>
            <td>${serviceType}</td>
            <td>${vehicleType}</td>
            <td>${model}</td>
            <td>${color}</td>
            <td>${vinNumber}</td>
            <td>${fuelType}</td>
            <td>${formatNumber(currentKm)} KM</td>
            <td>${getStatusBadge(vehicleStatus)}</td> <!-- Vehicle Status: ${vehicleStatus} -->
            <td>${inactive}</td>
            <td>${lastMaintenanceKm}</td>
            <td>${lastMaintenanceDate}</td>
            <td>${formatNumber(nextMaintenanceKm)} KM</td>
            <td>${kmToNextMaintenance}</td>
            <td>${lastTireChangeKm}</td>
            <td>${lastTireChangeDate}</td>
            <td>${nextTireChangeKm}</td>
            <td>${kmLeftForTireChange}</td>
            <td>${licenseRenewalDate}</td>
            <td>${daysToRenewLicense}</td>
            <td>${insuranceExpiryDate}</td>
            <td>${branch}</td>
            <td>${currentLocation}</td>
            <td>${driverName}</td>
            <td>${driverContact}</td>
            <td>${notes}</td>
            <td>
                <div class="action-buttons">
                    <button class="action-btn profile-btn" data-vehicle-id="${vehicleId}" title="View">
                        <i class="fas fa-eye"></i>
                    </button>
                    ${(hasPermission(currentUser?.role, PERMISSIONS.MANAGE_VEHICLES_ALL_BRANCHES) ||
                      (hasPermission(currentUser?.role, PERMISSIONS.MANAGE_VEHICLES_BRANCH) &&
                       (!currentUser.branch || vehicle['Branch'] === currentUser.branch))) ?
                    `<button class="action-btn edit-btn" data-id="${vehicleId}" title="Edit">
                        <i class="fas fa-edit"></i>
                    </button>` : ""}
                    ${hasPermission(currentUser?.role, PERMISSIONS.FULL_SYSTEM_CONTROL) ?
                    `<button class="action-btn delete-btn" data-id="${vehicleId}" title="Delete">
                        <i class="fas fa-trash"></i>
                    </button>` : ""}
                </div>
            </td>
        </tr>
    `
}

// Fetch vehicle details for editing
async function fetchVehicleDetails(vehicleId) {
  try {
    // First try to get the vehicle from cache
    let vehicle = getVehicleFromCache(vehicleId)

    if (!vehicle) {
      // If not found locally, fetch from API
      const params = new URLSearchParams()
      params.append("action", "getVehicleDetails")
      params.append("vehicleId", vehicleId)

      const response = await fetch(`${API_URL}?${params.toString()}`)
      const result = await response.json()

      if (result.status === "success") {
        vehicle = result.data
        // Update cache with fetched data
        updateVehiclesCache(vehicle)
      } else {
        throw new Error(result.message || "Failed to fetch vehicle details")
      }
    }

    if (vehicle) {
      fillVehicleForm(vehicle)
      vehiclesTable.style.display = "none"
      addVehicleForm.style.display = "none"
      editVehicleForm.style.display = "block"
      addVehicleBtn.innerHTML = '<i class="fas fa-arrow-left"></i> Back to Vehicles'

      // Update back button behavior
      addVehicleBtn.onclick = () => {
        vehiclesTable.style.display = "block"
        editVehicleForm.style.display = "none"
        addVehicleBtn.innerHTML = '<i class="fas fa-plus"></i> Add New Vehicle'
        addVehicleBtn.onclick = null
        addVehicleBtn.addEventListener("click", toggleAddVehicleForm)
      }
    } else {
      showNotification("Vehicle not found", "error")
    }
  } catch (error) {
    console.error("Error fetching vehicle details:", error)
    showNotification("Error fetching vehicle details", "error")
  }
}

// Confirm delete vehicle
function confirmDeleteVehicle(vehicleId) {
  const vehicle = getVehicleFromCache(vehicleId)
  if (!vehicle) {
    showNotification("Vehicle not found", "error")
    return
  }

  const message = `Are you sure you want to delete vehicle ${vehicle["License Plate"]}?`
  if (confirm(message)) {
    deleteVehicle(vehicleId)
  }
}

// Delete vehicle
async function deleteVehicle(vehicleId) {
  try {
    const params = new URLSearchParams()
    params.append("action", "deleteVehicle")
    params.append("id", vehicleId)

    const response = await fetch(`${API_URL}?${params.toString()}`)
    const result = await response.json()

    if (result.status === "success") {
      // Remove from cache
      vehiclesCache.delete(vehicleId)

      // Remove from vehicles array to maintain compatibility
      const index = vehicles.findIndex(v => v["Vehicle ID"] === vehicleId)
      if (index !== -1) {
        vehicles.splice(index, 1)
      }

      showNotification("Vehicle deleted successfully", "success")
      renderVehiclesTable()
      updateDashboard()
    } else {
      throw new Error(result.message || "Failed to delete vehicle")
    }
  } catch (error) {
    console.error("Error deleting vehicle:", error)
    showNotification("Error deleting vehicle: " + error.message, "error")
  }
}

// Fill vehicle form with vehicle data
function fillVehicleForm(vehicle) {
  const form = document.getElementById("editVehicleForm")
  if (!form) return

  // Set hidden vehicle ID field
  const vehicleIdField = document.getElementById("edit-vehicle-id")
  if (vehicleIdField) {
    vehicleIdField.value = vehicle["Vehicle ID"] || ""
  }

  // Map vehicle data to form fields
  const fieldMappings = {
    licensePlate: "License Plate",
    serviceType: "Service Type",
    vehicleType: "Vehicle Type",
    model: "Model",
    color: "Color",
    vinNumber: "VIN Number",
    fuelType: "Fuel Type",
    currentKm: "Current Km",
    vehicleStatus: "Vehicle Status",
    inactive: "Inactive",
    lastMaintenanceKm: "Last Maintenance Km",
    lastMaintenanceDate: "Last Maintenance Date",
    nextMaintenanceKm: "Next Maintenance Km",
    kmToNextMaintenance: "Km to next maintenance",
    lastTireChangeKm: "Last tire change Km",
    lastTireChangeDate: "Last tire change Data",
    nextTireChangeKm: "Next Tire Change Km",
    kmLeftForTireChange: "Km left for tire change",
    licenseRenewalDate: "License Renewal Date",
    daysToRenewLicense: "Days to renew license",
    insuranceExpiryDate: "Insurance Expiry Date",
    branch: "Branch",
    currentLocation: "Current Location",
    driverName: "Driver Name",
    driverContact: "Driver Contact",
    notes: "Notes"
  }

  // Set form field values
  Object.entries(fieldMappings).forEach(([formField, vehicleField]) => {
    const field = form.querySelector(`[name="${formField}"]`)
    if (!field) return;

    // حصول على قيمة الحقل من بيانات المركبة
    const fieldValue = vehicle[vehicleField] || "";

    // معالجة خاصة لحقل اسم السائق مع مربع البحث المخصص
    if (formField === 'driverName') {
      // ابحث عن عنصر الإدخال المرئي لمربع البحث
      const searchableInput = form.querySelector('.searchable-dropdown-input');
      const hiddenInput = field; // الحقل المخفي الذي يحمل القيمة الفعلية

      // إذا وجدنا عناصر مربع البحث المخصص
      if (searchableInput && hiddenInput) {
        searchableInput.value = fieldValue;
        hiddenInput.value = fieldValue;
        console.log(`Setting driver name to: ${fieldValue}`);
      } else {
        // إذا كان لا يزال مجرد عنصر select عادي
        field.value = fieldValue;
      }
    }
    // معالجة حقول التاريخ بشكل خاص
    else if (field.type === "date" && fieldValue) {
      // تحويل تنسيق التاريخ إذا لزم الأمر (YYYY-MM-DD)
      let dateValue = fieldValue;
      if (dateValue && typeof dateValue === "string") {
        // محاولة تحليل وتنسيق التاريخ
        try {
          const date = new Date(dateValue)
          if (!isNaN(date.getTime())) {
            dateValue = date.toISOString().split("T")[0]
          }
        } catch (e) {
          console.warn(`Failed to parse date: ${dateValue}`, e)
        }
      }
      field.value = dateValue || ""
    }
    // معالجة باقي الحقول بشكل عادي
    else {
      field.value = fieldValue;
    }
  });

  // إعادة تطبيق معالج حدث تغيير القيمة على مربعات البحث
  const driverNameField = form.querySelector('[name="driverName"]');
  if (driverNameField) {
    // إطلاق حدث تغيير لتحديث أي معالجات أحداث مرتبطة
    const event = new Event('change', { bubbles: true });
    driverNameField.dispatchEvent(event);
  }

  // عرض إشعار بأن النموذج تم تحميله
  showNotification("Vehicle data loaded for editing", "success");
}

// Add event listeners for vehicle action buttons
function addVehicleActionListeners() {

  // Edit buttons
  document.querySelectorAll("#vehicles-page .edit-btn").forEach((btn) => {
    btn.addEventListener("click", () => {
      const vehicleId = btn.getAttribute("data-id")
      fetchVehicleDetails(vehicleId)
    })
  })

  // Delete buttons
  document.querySelectorAll("#vehicles-page .delete-btn").forEach((btn) => {
    btn.addEventListener("click", () => {
      const vehicleId = btn.getAttribute("data-id")
      confirmDeleteVehicle(vehicleId)
    })
  })
}



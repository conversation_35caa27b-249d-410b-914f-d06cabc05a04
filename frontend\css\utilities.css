/**
 * Utilities CSS - أدوات CSS المساعدة
 * يحتوي على فئات مساعدة للتنسيق السريع والتأثيرات
 */

/* ===== أدوات العرض ===== */
.d-none { display: none !important; }
.d-inline { display: inline !important; }
.d-inline-block { display: inline-block !important; }
.d-block { display: block !important; }
.d-table { display: table !important; }
.d-table-row { display: table-row !important; }
.d-table-cell { display: table-cell !important; }
.d-flex { display: flex !important; }
.d-inline-flex { display: inline-flex !important; }
.d-grid { display: grid !important; }

/* ===== أدوات Flexbox ===== */
.flex-row { flex-direction: row !important; }
.flex-column { flex-direction: column !important; }
.flex-row-reverse { flex-direction: row-reverse !important; }
.flex-column-reverse { flex-direction: column-reverse !important; }

.flex-wrap { flex-wrap: wrap !important; }
.flex-nowrap { flex-wrap: nowrap !important; }
.flex-wrap-reverse { flex-wrap: wrap-reverse !important; }

.flex-fill { flex: 1 1 auto !important; }
.flex-grow-0 { flex-grow: 0 !important; }
.flex-grow-1 { flex-grow: 1 !important; }
.flex-shrink-0 { flex-shrink: 0 !important; }
.flex-shrink-1 { flex-shrink: 1 !important; }

.justify-content-start { justify-content: flex-start !important; }
.justify-content-end { justify-content: flex-end !important; }
.justify-content-center { justify-content: center !important; }
.justify-content-between { justify-content: space-between !important; }
.justify-content-around { justify-content: space-around !important; }
.justify-content-evenly { justify-content: space-evenly !important; }

.align-items-start { align-items: flex-start !important; }
.align-items-end { align-items: flex-end !important; }
.align-items-center { align-items: center !important; }
.align-items-baseline { align-items: baseline !important; }
.align-items-stretch { align-items: stretch !important; }

.align-content-start { align-content: flex-start !important; }
.align-content-end { align-content: flex-end !important; }
.align-content-center { align-content: center !important; }
.align-content-between { align-content: space-between !important; }
.align-content-around { align-content: space-around !important; }
.align-content-stretch { align-content: stretch !important; }

.align-self-auto { align-self: auto !important; }
.align-self-start { align-self: flex-start !important; }
.align-self-end { align-self: flex-end !important; }
.align-self-center { align-self: center !important; }
.align-self-baseline { align-self: baseline !important; }
.align-self-stretch { align-self: stretch !important; }

/* ===== أدوات Grid ===== */
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)) !important; }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)) !important; }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)) !important; }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)) !important; }
.grid-cols-5 { grid-template-columns: repeat(5, minmax(0, 1fr)) !important; }
.grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)) !important; }
.grid-cols-12 { grid-template-columns: repeat(12, minmax(0, 1fr)) !important; }

.col-span-1 { grid-column: span 1 / span 1 !important; }
.col-span-2 { grid-column: span 2 / span 2 !important; }
.col-span-3 { grid-column: span 3 / span 3 !important; }
.col-span-4 { grid-column: span 4 / span 4 !important; }
.col-span-5 { grid-column: span 5 / span 5 !important; }
.col-span-6 { grid-column: span 6 / span 6 !important; }
.col-span-full { grid-column: 1 / -1 !important; }

.gap-0 { gap: 0 !important; }
.gap-1 { gap: var(--spacing-xs) !important; }
.gap-2 { gap: var(--spacing-sm) !important; }
.gap-3 { gap: var(--spacing-md) !important; }
.gap-4 { gap: var(--spacing-lg) !important; }
.gap-5 { gap: var(--spacing-xl) !important; }

/* ===== أدوات المسافات ===== */
.m-0 { margin: 0 !important; }
.m-1 { margin: var(--spacing-xs) !important; }
.m-2 { margin: var(--spacing-sm) !important; }
.m-3 { margin: var(--spacing-md) !important; }
.m-4 { margin: var(--spacing-lg) !important; }
.m-5 { margin: var(--spacing-xl) !important; }
.m-auto { margin: auto !important; }

.mx-0 { margin-left: 0 !important; margin-right: 0 !important; }
.mx-1 { margin-left: var(--spacing-xs) !important; margin-right: var(--spacing-xs) !important; }
.mx-2 { margin-left: var(--spacing-sm) !important; margin-right: var(--spacing-sm) !important; }
.mx-3 { margin-left: var(--spacing-md) !important; margin-right: var(--spacing-md) !important; }
.mx-4 { margin-left: var(--spacing-lg) !important; margin-right: var(--spacing-lg) !important; }
.mx-5 { margin-left: var(--spacing-xl) !important; margin-right: var(--spacing-xl) !important; }
.mx-auto { margin-left: auto !important; margin-right: auto !important; }

.my-0 { margin-top: 0 !important; margin-bottom: 0 !important; }
.my-1 { margin-top: var(--spacing-xs) !important; margin-bottom: var(--spacing-xs) !important; }
.my-2 { margin-top: var(--spacing-sm) !important; margin-bottom: var(--spacing-sm) !important; }
.my-3 { margin-top: var(--spacing-md) !important; margin-bottom: var(--spacing-md) !important; }
.my-4 { margin-top: var(--spacing-lg) !important; margin-bottom: var(--spacing-lg) !important; }
.my-5 { margin-top: var(--spacing-xl) !important; margin-bottom: var(--spacing-xl) !important; }

.mt-0 { margin-top: 0 !important; }
.mt-1 { margin-top: var(--spacing-xs) !important; }
.mt-2 { margin-top: var(--spacing-sm) !important; }
.mt-3 { margin-top: var(--spacing-md) !important; }
.mt-4 { margin-top: var(--spacing-lg) !important; }
.mt-5 { margin-top: var(--spacing-xl) !important; }
.mt-auto { margin-top: auto !important; }

.mr-0 { margin-right: 0 !important; }
.mr-1 { margin-right: var(--spacing-xs) !important; }
.mr-2 { margin-right: var(--spacing-sm) !important; }
.mr-3 { margin-right: var(--spacing-md) !important; }
.mr-4 { margin-right: var(--spacing-lg) !important; }
.mr-5 { margin-right: var(--spacing-xl) !important; }
.mr-auto { margin-right: auto !important; }

.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: var(--spacing-xs) !important; }
.mb-2 { margin-bottom: var(--spacing-sm) !important; }
.mb-3 { margin-bottom: var(--spacing-md) !important; }
.mb-4 { margin-bottom: var(--spacing-lg) !important; }
.mb-5 { margin-bottom: var(--spacing-xl) !important; }
.mb-auto { margin-bottom: auto !important; }

.ml-0 { margin-left: 0 !important; }
.ml-1 { margin-left: var(--spacing-xs) !important; }
.ml-2 { margin-left: var(--spacing-sm) !important; }
.ml-3 { margin-left: var(--spacing-md) !important; }
.ml-4 { margin-left: var(--spacing-lg) !important; }
.ml-5 { margin-left: var(--spacing-xl) !important; }
.ml-auto { margin-left: auto !important; }

.p-0 { padding: 0 !important; }
.p-1 { padding: var(--spacing-xs) !important; }
.p-2 { padding: var(--spacing-sm) !important; }
.p-3 { padding: var(--spacing-md) !important; }
.p-4 { padding: var(--spacing-lg) !important; }
.p-5 { padding: var(--spacing-xl) !important; }

.px-0 { padding-left: 0 !important; padding-right: 0 !important; }
.px-1 { padding-left: var(--spacing-xs) !important; padding-right: var(--spacing-xs) !important; }
.px-2 { padding-left: var(--spacing-sm) !important; padding-right: var(--spacing-sm) !important; }
.px-3 { padding-left: var(--spacing-md) !important; padding-right: var(--spacing-md) !important; }
.px-4 { padding-left: var(--spacing-lg) !important; padding-right: var(--spacing-lg) !important; }
.px-5 { padding-left: var(--spacing-xl) !important; padding-right: var(--spacing-xl) !important; }

.py-0 { padding-top: 0 !important; padding-bottom: 0 !important; }
.py-1 { padding-top: var(--spacing-xs) !important; padding-bottom: var(--spacing-xs) !important; }
.py-2 { padding-top: var(--spacing-sm) !important; padding-bottom: var(--spacing-sm) !important; }
.py-3 { padding-top: var(--spacing-md) !important; padding-bottom: var(--spacing-md) !important; }
.py-4 { padding-top: var(--spacing-lg) !important; padding-bottom: var(--spacing-lg) !important; }
.py-5 { padding-top: var(--spacing-xl) !important; padding-bottom: var(--spacing-xl) !important; }

/* ===== أدوات الأحجام ===== */
.w-25 { width: 25% !important; }
.w-50 { width: 50% !important; }
.w-75 { width: 75% !important; }
.w-100 { width: 100% !important; }
.w-auto { width: auto !important; }

.h-25 { height: 25% !important; }
.h-50 { height: 50% !important; }
.h-75 { height: 75% !important; }
.h-100 { height: 100% !important; }
.h-auto { height: auto !important; }

.mw-100 { max-width: 100% !important; }
.mh-100 { max-height: 100% !important; }

.min-vw-100 { min-width: 100vw !important; }
.min-vh-100 { min-height: 100vh !important; }

.vw-100 { width: 100vw !important; }
.vh-100 { height: 100vh !important; }

/* ===== أدوات النصوص ===== */
.text-left { text-align: left !important; }
.text-right { text-align: right !important; }
.text-center { text-align: center !important; }
.text-justify { text-align: justify !important; }

.text-wrap { white-space: normal !important; }
.text-nowrap { white-space: nowrap !important; }

.text-break { word-wrap: break-word !important; word-break: break-word !important; }

.text-lowercase { text-transform: lowercase !important; }
.text-uppercase { text-transform: uppercase !important; }
.text-capitalize { text-transform: capitalize !important; }

.fw-light { font-weight: var(--font-light) !important; }
.fw-normal { font-weight: var(--font-normal) !important; }
.fw-medium { font-weight: var(--font-medium) !important; }
.fw-semibold { font-weight: var(--font-semibold) !important; }
.fw-bold { font-weight: var(--font-bold) !important; }

.fs-1 { font-size: var(--font-3xl) !important; }
.fs-2 { font-size: var(--font-2xl) !important; }
.fs-3 { font-size: var(--font-xl) !important; }
.fs-4 { font-size: var(--font-lg) !important; }
.fs-5 { font-size: var(--font-base) !important; }
.fs-6 { font-size: var(--font-sm) !important; }

.text-primary { color: var(--primary-color) !important; }
.text-secondary { color: var(--text-secondary) !important; }
.text-success { color: var(--success-color) !important; }
.text-info { color: var(--info-color) !important; }
.text-warning { color: var(--warning-color) !important; }
.text-danger { color: var(--error-color) !important; }
.text-light { color: var(--text-light) !important; }
.text-dark { color: var(--text-primary) !important; }
.text-muted { color: var(--text-muted) !important; }

/* ===== أدوات الخلفيات ===== */
.bg-primary { background-color: var(--primary-color) !important; }
.bg-secondary { background-color: var(--bg-secondary) !important; }
.bg-success { background-color: var(--success-color) !important; }
.bg-info { background-color: var(--info-color) !important; }
.bg-warning { background-color: var(--warning-color) !important; }
.bg-danger { background-color: var(--error-color) !important; }
.bg-light { background-color: var(--bg-primary) !important; }
.bg-dark { background-color: var(--bg-dark) !important; }
.bg-transparent { background-color: transparent !important; }

/* ===== أدوات الحدود ===== */
.border { border: 1px solid var(--border-color) !important; }
.border-0 { border: 0 !important; }
.border-top { border-top: 1px solid var(--border-color) !important; }
.border-right { border-right: 1px solid var(--border-color) !important; }
.border-bottom { border-bottom: 1px solid var(--border-color) !important; }
.border-left { border-left: 1px solid var(--border-color) !important; }

.border-primary { border-color: var(--primary-color) !important; }
.border-secondary { border-color: var(--border-color) !important; }
.border-success { border-color: var(--success-color) !important; }
.border-info { border-color: var(--info-color) !important; }
.border-warning { border-color: var(--warning-color) !important; }
.border-danger { border-color: var(--error-color) !important; }

.rounded { border-radius: var(--radius-md) !important; }
.rounded-0 { border-radius: 0 !important; }
.rounded-1 { border-radius: var(--radius-sm) !important; }
.rounded-2 { border-radius: var(--radius-md) !important; }
.rounded-3 { border-radius: var(--radius-lg) !important; }
.rounded-circle { border-radius: 50% !important; }
.rounded-pill { border-radius: 50rem !important; }

/* ===== أدوات الموضع ===== */
.position-static { position: static !important; }
.position-relative { position: relative !important; }
.position-absolute { position: absolute !important; }
.position-fixed { position: fixed !important; }
.position-sticky { position: sticky !important; }

.top-0 { top: 0 !important; }
.top-50 { top: 50% !important; }
.top-100 { top: 100% !important; }

.bottom-0 { bottom: 0 !important; }
.bottom-50 { bottom: 50% !important; }
.bottom-100 { bottom: 100% !important; }

.start-0 { right: 0 !important; }
.start-50 { right: 50% !important; }
.start-100 { right: 100% !important; }

.end-0 { left: 0 !important; }
.end-50 { left: 50% !important; }
.end-100 { left: 100% !important; }

.translate-middle { transform: translate(-50%, -50%) !important; }
.translate-middle-x { transform: translateX(-50%) !important; }
.translate-middle-y { transform: translateY(-50%) !important; }

/* ===== أدوات الظلال ===== */
.shadow-none { box-shadow: none !important; }
.shadow-sm { box-shadow: var(--shadow-sm) !important; }
.shadow { box-shadow: var(--shadow-md) !important; }
.shadow-lg { box-shadow: var(--shadow-lg) !important; }

/* ===== أدوات الشفافية ===== */
.opacity-0 { opacity: 0 !important; }
.opacity-25 { opacity: 0.25 !important; }
.opacity-50 { opacity: 0.5 !important; }
.opacity-75 { opacity: 0.75 !important; }
.opacity-100 { opacity: 1 !important; }

/* ===== أدوات التفاعل ===== */
.user-select-all { user-select: all !important; }
.user-select-auto { user-select: auto !important; }
.user-select-none { user-select: none !important; }

.pe-none { pointer-events: none !important; }
.pe-auto { pointer-events: auto !important; }

.cursor-pointer { cursor: pointer !important; }
.cursor-default { cursor: default !important; }
.cursor-not-allowed { cursor: not-allowed !important; }

/* ===== أدوات الفيض ===== */
.overflow-auto { overflow: auto !important; }
.overflow-hidden { overflow: hidden !important; }
.overflow-visible { overflow: visible !important; }
.overflow-scroll { overflow: scroll !important; }

.overflow-x-auto { overflow-x: auto !important; }
.overflow-x-hidden { overflow-x: hidden !important; }
.overflow-x-visible { overflow-x: visible !important; }
.overflow-x-scroll { overflow-x: scroll !important; }

.overflow-y-auto { overflow-y: auto !important; }
.overflow-y-hidden { overflow-y: hidden !important; }
.overflow-y-visible { overflow-y: visible !important; }
.overflow-y-scroll { overflow-y: scroll !important; }

/* ===== أدوات z-index ===== */
.z-n1 { z-index: -1 !important; }
.z-0 { z-index: 0 !important; }
.z-1 { z-index: 1 !important; }
.z-2 { z-index: 2 !important; }
.z-3 { z-index: 3 !important; }

/* ===== التأثيرات والحركات ===== */
.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

.fade-out {
    animation: fadeOut 0.3s ease-in-out;
}

.slide-in-up {
    animation: slideInUp 0.3s ease-out;
}

.slide-in-down {
    animation: slideInDown 0.3s ease-out;
}

.slide-in-left {
    animation: slideInLeft 0.3s ease-out;
}

.slide-in-right {
    animation: slideInRight 0.3s ease-out;
}

.zoom-in {
    animation: zoomIn 0.3s ease-out;
}

.bounce {
    animation: bounce 1s infinite;
}

.pulse {
    animation: pulse 2s infinite;
}

.shake {
    animation: shake 0.5s ease-in-out;
}

/* ===== تعريف الحركات ===== */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}

@keyframes slideInUp {
    from { transform: translateY(100%); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes slideInDown {
    from { transform: translateY(-100%); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes slideInLeft {
    from { transform: translateX(-100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes slideInRight {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes zoomIn {
    from { transform: scale(0); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% { transform: translate3d(0, 0, 0); }
    40%, 43% { transform: translate3d(0, -30px, 0); }
    70% { transform: translate3d(0, -15px, 0); }
    90% { transform: translate3d(0, -4px, 0); }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-10px); }
    20%, 40%, 60%, 80% { transform: translateX(10px); }
}

/* ===== أدوات الانتقالات ===== */
.transition-none { transition: none !important; }
.transition-all { transition: all var(--transition-normal) !important; }
.transition-colors { transition: color var(--transition-fast), background-color var(--transition-fast), border-color var(--transition-fast) !important; }
.transition-opacity { transition: opacity var(--transition-fast) !important; }
.transition-shadow { transition: box-shadow var(--transition-fast) !important; }
.transition-transform { transition: transform var(--transition-fast) !important; }

/* ===== أدوات التحويل ===== */
.rotate-0 { transform: rotate(0deg) !important; }
.rotate-90 { transform: rotate(90deg) !important; }
.rotate-180 { transform: rotate(180deg) !important; }
.rotate-270 { transform: rotate(270deg) !important; }

.scale-0 { transform: scale(0) !important; }
.scale-50 { transform: scale(0.5) !important; }
.scale-75 { transform: scale(0.75) !important; }
.scale-90 { transform: scale(0.9) !important; }
.scale-95 { transform: scale(0.95) !important; }
.scale-100 { transform: scale(1) !important; }
.scale-105 { transform: scale(1.05) !important; }
.scale-110 { transform: scale(1.1) !important; }
.scale-125 { transform: scale(1.25) !important; }
.scale-150 { transform: scale(1.5) !important; }

/* ===== أدوات الاستجابة ===== */
@media (max-width: 576px) {
    .d-sm-none { display: none !important; }
    .d-sm-inline { display: inline !important; }
    .d-sm-inline-block { display: inline-block !important; }
    .d-sm-block { display: block !important; }
    .d-sm-flex { display: flex !important; }
    .d-sm-inline-flex { display: inline-flex !important; }
    
    .flex-sm-row { flex-direction: row !important; }
    .flex-sm-column { flex-direction: column !important; }
    
    .justify-content-sm-start { justify-content: flex-start !important; }
    .justify-content-sm-end { justify-content: flex-end !important; }
    .justify-content-sm-center { justify-content: center !important; }
    .justify-content-sm-between { justify-content: space-between !important; }
    
    .text-sm-left { text-align: left !important; }
    .text-sm-right { text-align: right !important; }
    .text-sm-center { text-align: center !important; }
}

@media (max-width: 768px) {
    .d-md-none { display: none !important; }
    .d-md-inline { display: inline !important; }
    .d-md-inline-block { display: inline-block !important; }
    .d-md-block { display: block !important; }
    .d-md-flex { display: flex !important; }
    .d-md-inline-flex { display: inline-flex !important; }
    
    .flex-md-row { flex-direction: row !important; }
    .flex-md-column { flex-direction: column !important; }
    
    .justify-content-md-start { justify-content: flex-start !important; }
    .justify-content-md-end { justify-content: flex-end !important; }
    .justify-content-md-center { justify-content: center !important; }
    .justify-content-md-between { justify-content: space-between !important; }
    
    .text-md-left { text-align: left !important; }
    .text-md-right { text-align: right !important; }
    .text-md-center { text-align: center !important; }
}

@media (max-width: 1024px) {
    .d-lg-none { display: none !important; }
    .d-lg-inline { display: inline !important; }
    .d-lg-inline-block { display: inline-block !important; }
    .d-lg-block { display: block !important; }
    .d-lg-flex { display: flex !important; }
    .d-lg-inline-flex { display: inline-flex !important; }
    
    .flex-lg-row { flex-direction: row !important; }
    .flex-lg-column { flex-direction: column !important; }
    
    .justify-content-lg-start { justify-content: flex-start !important; }
    .justify-content-lg-end { justify-content: flex-end !important; }
    .justify-content-lg-center { justify-content: center !important; }
    .justify-content-lg-between { justify-content: space-between !important; }
    
    .text-lg-left { text-align: left !important; }
    .text-lg-right { text-align: right !important; }
    .text-lg-center { text-align: center !important; }
}

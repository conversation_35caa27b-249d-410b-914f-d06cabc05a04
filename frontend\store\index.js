/**
 * Store Index - فهرس المخزن
 * يجمع جميع مكونات إدارة الحالة ويصدرها من مكان واحد
 */

// استيراد المكونات الأساسية
export { Store, appStore, getState, setState, subscribe } from './store.js';
export { actions } from './actions.js';
export { selectors } from './selectors.js';
export { hooks } from './hooks.js';

// استيراد افتراضي
import appStore from './store.js';
import { actions } from './actions.js';
import { selectors } from './selectors.js';
import { hooks } from './hooks.js';

/**
 * تهيئة نظام إدارة الحالة
 */
export function initializeStore() {
    try {
        console.log('Initializing store...');
        
        // استعادة الحالة من التخزين المحلي
        restoreStateFromStorage();
        
        // إعداد مراقبة الاتصال
        setupConnectionMonitoring();
        
        // إعداد حفظ الحالة التلقائي
        setupAutoSave();
        
        console.log('Store initialized successfully');
        return true;
        
    } catch (error) {
        console.error('Failed to initialize store:', error);
        return false;
    }
}

/**
 * استعادة الحالة من التخزين المحلي
 */
function restoreStateFromStorage() {
    try {
        // استعادة تفضيلات المستخدم
        const userPreferences = localStorage.getItem('user_preferences');
        if (userPreferences) {
            const preferences = JSON.parse(userPreferences);
            actions.user.updatePreferences(preferences);
        }
        
        // استعادة إعدادات واجهة المستخدم
        const uiSettings = localStorage.getItem('ui_settings');
        if (uiSettings) {
            const settings = JSON.parse(uiSettings);
            appStore.setState({
                app: {
                    ...appStore.getState().app,
                    ...settings
                }
            }, 'RESTORE_UI_SETTINGS');
        }
        
        // استعادة الفلاتر المحفوظة
        const savedFilters = localStorage.getItem('saved_filters');
        if (savedFilters) {
            const filters = JSON.parse(savedFilters);
            appStore.setState({
                filters: {
                    ...appStore.getState().filters,
                    ...filters
                }
            }, 'RESTORE_FILTERS');
        }
        
    } catch (error) {
        console.warn('Failed to restore state from storage:', error);
    }
}

/**
 * إعداد مراقبة الاتصال
 */
function setupConnectionMonitoring() {
    // مراقبة حالة الاتصال
    window.addEventListener('online', () => {
        actions.app.setOnlineStatus(true);
    });

    window.addEventListener('offline', () => {
        actions.app.setOnlineStatus(false);
    });

    // تعيين الحالة الأولية
    actions.app.setOnlineStatus(navigator.onLine);
}

/**
 * إعداد حفظ الحالة التلقائي
 */
function setupAutoSave() {
    // حفظ تفضيلات المستخدم عند تغييرها
    appStore.subscribe(selectors.user.getUserPreferences, (preferences) => {
        try {
            localStorage.setItem('user_preferences', JSON.stringify(preferences));
        } catch (error) {
            console.warn('Failed to save user preferences:', error);
        }
    });

    // حفظ إعدادات واجهة المستخدم
    appStore.subscribe((state) => state.app, (appState) => {
        try {
            const settingsToSave = {
                sidebarOpen: appState.sidebarOpen,
                theme: appState.theme
            };
            localStorage.setItem('ui_settings', JSON.stringify(settingsToSave));
        } catch (error) {
            console.warn('Failed to save UI settings:', error);
        }
    });

    // حفظ الفلاتر النشطة
    appStore.subscribe((state) => state.filters, (filters) => {
        try {
            // حفظ الفلاتر التي لها قيم فقط
            const activeFilters = {};
            Object.keys(filters).forEach(section => {
                if (Object.keys(filters[section]).length > 0) {
                    activeFilters[section] = filters[section];
                }
            });
            
            if (Object.keys(activeFilters).length > 0) {
                localStorage.setItem('saved_filters', JSON.stringify(activeFilters));
            } else {
                localStorage.removeItem('saved_filters');
            }
        } catch (error) {
            console.warn('Failed to save filters:', error);
        }
    });
}

/**
 * تنظيف المخزن
 */
export function cleanupStore() {
    try {
        console.log('Cleaning up store...');
        
        // مسح الإشعارات
        actions.app.clearNotifications();
        
        // مسح الأخطاء
        actions.app.clearErrors();
        
        // مسح التاريخ
        appStore.clearHistory();
        
        console.log('Store cleaned up successfully');
        return true;
        
    } catch (error) {
        console.error('Failed to cleanup store:', error);
        return false;
    }
}

/**
 * إعادة تعيين المخزن
 */
export function resetStore() {
    try {
        console.log('Resetting store...');
        
        // إعادة تعيين إلى الحالة الأولية
        appStore.reset({
            user: {
                isAuthenticated: false,
                currentUser: null,
                preferences: {
                    theme: 'light',
                    language: 'ar',
                    notifications: true
                }
            },
            app: {
                isLoading: false,
                currentPage: 'dashboard',
                sidebarOpen: true,
                notifications: [],
                errors: []
            },
            data: {
                vehicles: [],
                drivers: [],
                maintenance: [],
                fuel: [],
                users: [],
                lastSync: null,
                isOnline: navigator.onLine
            },
            ui: {
                modals: {},
                forms: {},
                tables: {},
                charts: {}
            },
            filters: {
                vehicles: {},
                drivers: {},
                maintenance: {},
                fuel: {}
            }
        });
        
        console.log('Store reset successfully');
        return true;
        
    } catch (error) {
        console.error('Failed to reset store:', error);
        return false;
    }
}

/**
 * الحصول على معلومات المخزن
 */
export function getStoreInfo() {
    return {
        ...appStore.getInfo(),
        actions: Object.keys(actions).length,
        selectors: Object.keys(selectors).length,
        hooks: Object.keys(hooks).length
    };
}

/**
 * تصدير البيانات من المخزن
 */
export function exportStoreData() {
    const state = appStore.getState();
    return {
        data: state.data,
        filters: state.filters,
        userPreferences: state.user.preferences,
        timestamp: new Date().toISOString()
    };
}

/**
 * استيراد البيانات إلى المخزن
 */
export function importStoreData(data) {
    try {
        if (data.data) {
            appStore.setState({
                data: {
                    ...appStore.getState().data,
                    ...data.data
                }
            }, 'IMPORT_DATA');
        }
        
        if (data.filters) {
            appStore.setState({
                filters: {
                    ...appStore.getState().filters,
                    ...data.filters
                }
            }, 'IMPORT_FILTERS');
        }
        
        if (data.userPreferences) {
            actions.user.updatePreferences(data.userPreferences);
        }
        
        return true;
    } catch (error) {
        console.error('Failed to import store data:', error);
        return false;
    }
}

/**
 * دوال مساعدة للوصول السريع
 */

// دالة موحدة للحصول على البيانات
export function getData(type) {
    const state = appStore.getState();
    switch (type) {
        case 'vehicles':
            return state.data.vehicles;
        case 'drivers':
            return state.data.drivers;
        case 'maintenance':
            return state.data.maintenance;
        case 'fuel':
            return state.data.fuel;
        case 'users':
            return state.data.users;
        default:
            return null;
    }
}

// دالة موحدة لتحديث البيانات
export function setData(type, data) {
    switch (type) {
        case 'vehicles':
            actions.data.setVehicles(data);
            break;
        case 'drivers':
            actions.data.setDrivers(data);
            break;
        case 'maintenance':
            actions.data.setMaintenanceRecords(data);
            break;
        case 'fuel':
            actions.data.setFuelRecords(data);
            break;
        case 'users':
            actions.data.setUsers(data);
            break;
    }
}

// دالة للحصول على الإحصائيات
export function getStats() {
    const state = appStore.getState();
    return {
        vehicles: selectors.stats.getVehicleStats(state),
        drivers: selectors.stats.getDriverStats(state),
        maintenance: selectors.stats.getMaintenanceStats(state),
        fuel: selectors.stats.getFuelStats(state)
    };
}

/**
 * مجموعة أدوات المخزن للاستخدام السهل
 */
export const store = {
    // المخزن الأساسي
    appStore,
    
    // المكونات
    actions,
    selectors,
    hooks,
    
    // دوال الإدارة
    initialize: initializeStore,
    cleanup: cleanupStore,
    reset: resetStore,
    getInfo: getStoreInfo,
    
    // دوال البيانات
    getData,
    setData,
    getStats,
    exportData: exportStoreData,
    importData: importStoreData,
    
    // دوال مساعدة
    getState: () => appStore.getState(),
    setState: (updates, actionType) => appStore.setState(updates, actionType),
    subscribe: (selector, callback) => appStore.subscribe(selector, callback)
};

// تصدير افتراضي
export default {
    // المكونات الأساسية
    appStore,
    actions,
    selectors,
    hooks,
    
    // مجموعة الأدوات
    store,
    
    // دوال الإدارة
    initializeStore,
    cleanupStore,
    resetStore,
    getStoreInfo,
    
    // دوال مساعدة
    getData,
    setData,
    getStats,
    exportStoreData,
    importStoreData
};

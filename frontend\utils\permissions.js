/**
 * Permissions System - نظام الصلاحيات
 * يوفر إدارة الأدوار والصلاحيات للمستخدمين
 */

// تعريف الأدوار
export const ROLES = {
    SUPER_ADMIN: 'Super Admin',
    GENERAL_MANAGER: 'General Manager',
    OPERATIONS_MANAGER: 'Operations Manager',
    FLEET_MANAGER: 'Fleet Manager',
    FLEET_SUPERVISOR: 'Fleet Supervisor',
    FLEET_STAFF: 'Fleet Staff',
    DRIVER: 'Driver'
};

// ترجمة الأدوار
export const ROLE_LABELS = {
    [ROLES.SUPER_ADMIN]: 'مدير النظام',
    [ROLES.GENERAL_MANAGER]: 'المدير العام',
    [ROLES.OPERATIONS_MANAGER]: 'مدير العمليات',
    [ROLES.FLEET_MANAGER]: 'مدير الأسطول',
    [ROLES.FLEET_SUPERVISOR]: 'مشرف الأسطول',
    [ROLES.FLEET_STAFF]: 'موظف الأسطول',
    [ROLES.DRIVER]: 'سائق'
};

// تعريف الصلاحيات
export const PERMISSIONS = {
    // صلاحيات النظام
    FULL_SYSTEM_CONTROL: 'Full system control',
    MANAGE_SYSTEM_CODE: 'Manage system code',
    MANAGE_USERS: 'Manage users',
    ASSIGN_SUPERVISORS_MANAGERS: 'Assign supervisors and managers',
    
    // صلاحيات المركبات
    MANAGE_VEHICLES_ALL_BRANCHES: 'Manage vehicles (all branches)',
    MANAGE_VEHICLES_BRANCH: 'Manage vehicles (branch only)',
    VIEW_VEHICLES_ALL_BRANCHES: 'View vehicles (all branches)',
    VIEW_VEHICLES_BRANCH: 'View vehicles (branch only)',
    VIEW_VEHICLES_ASSIGNED: 'View vehicles (assigned only)',
    
    // صلاحيات الصيانة
    REGISTER_MAINTENANCE: 'Register maintenance',
    RECEIVE_MAINTENANCE_ALERTS: 'Receive maintenance alerts',
    VIEW_MAINTENANCE_ALL_BRANCHES: 'View maintenance (all branches)',
    VIEW_MAINTENANCE_BRANCH: 'View maintenance (branch only)',
    
    // صلاحيات الكيلومترات والوقود
    UPDATE_MILEAGE_ALL_BRANCHES: 'Update mileage (all branches)',
    UPDATE_MILEAGE_BRANCH: 'Update mileage (branch only)',
    UPDATE_MILEAGE_ASSIGNED: 'Update mileage (assigned vehicle only)',
    
    // صلاحيات التقارير
    VIEW_REPORTS: 'View reports',
    EXPORT_REPORTS: 'Export reports',
    
    // صلاحيات أخرى
    GRANT_TEMPORARY_PERMISSIONS: 'Grant temporary permissions',
    VIEW_DASHBOARD: 'View dashboard',
    MANAGE_DRIVERS: 'Manage drivers'
};

// ترجمة الصلاحيات
export const PERMISSION_LABELS = {
    [PERMISSIONS.FULL_SYSTEM_CONTROL]: 'التحكم الكامل في النظام',
    [PERMISSIONS.MANAGE_SYSTEM_CODE]: 'إدارة كود النظام',
    [PERMISSIONS.MANAGE_USERS]: 'إدارة المستخدمين',
    [PERMISSIONS.ASSIGN_SUPERVISORS_MANAGERS]: 'تعيين المشرفين والمديرين',
    [PERMISSIONS.MANAGE_VEHICLES_ALL_BRANCHES]: 'إدارة المركبات (جميع الفروع)',
    [PERMISSIONS.MANAGE_VEHICLES_BRANCH]: 'إدارة المركبات (الفرع فقط)',
    [PERMISSIONS.VIEW_VEHICLES_ALL_BRANCHES]: 'عرض المركبات (جميع الفروع)',
    [PERMISSIONS.VIEW_VEHICLES_BRANCH]: 'عرض المركبات (الفرع فقط)',
    [PERMISSIONS.VIEW_VEHICLES_ASSIGNED]: 'عرض المركبات (المخصصة فقط)',
    [PERMISSIONS.REGISTER_MAINTENANCE]: 'تسجيل الصيانة',
    [PERMISSIONS.RECEIVE_MAINTENANCE_ALERTS]: 'استقبال تنبيهات الصيانة',
    [PERMISSIONS.VIEW_MAINTENANCE_ALL_BRANCHES]: 'عرض الصيانة (جميع الفروع)',
    [PERMISSIONS.VIEW_MAINTENANCE_BRANCH]: 'عرض الصيانة (الفرع فقط)',
    [PERMISSIONS.UPDATE_MILEAGE_ALL_BRANCHES]: 'تحديث الكيلومترات (جميع الفروع)',
    [PERMISSIONS.UPDATE_MILEAGE_BRANCH]: 'تحديث الكيلومترات (الفرع فقط)',
    [PERMISSIONS.UPDATE_MILEAGE_ASSIGNED]: 'تحديث الكيلومترات (المركبة المخصصة فقط)',
    [PERMISSIONS.VIEW_REPORTS]: 'عرض التقارير',
    [PERMISSIONS.EXPORT_REPORTS]: 'تصدير التقارير',
    [PERMISSIONS.GRANT_TEMPORARY_PERMISSIONS]: 'منح صلاحيات مؤقتة',
    [PERMISSIONS.VIEW_DASHBOARD]: 'عرض لوحة التحكم',
    [PERMISSIONS.MANAGE_DRIVERS]: 'إدارة السائقين'
};

// صلاحيات المستخدمين
export const USER_PERMISSIONS = {
    VIEW_ALL_USERS: 'View all users',
    EDIT_ALL_USERS: 'Edit all users',
    EDIT_OPERATIONS_MANAGER_BRANCH: 'Edit Operations Manager branch',
    EDIT_FLEET_MANAGER_BRANCH: 'Edit Fleet Manager branch',
    EDIT_FLEET_SUPERVISOR_BRANCH: 'Edit Fleet Supervisor branch',
    EDIT_FLEET_STAFF_BRANCH: 'Edit Fleet Staff branch',
    EDIT_DRIVER_BRANCH: 'Edit Driver branch'
};

// مصفوفة الصلاحيات حسب الدور
export const ROLE_PERMISSIONS = {
    [ROLES.SUPER_ADMIN]: [
        PERMISSIONS.FULL_SYSTEM_CONTROL,
        PERMISSIONS.MANAGE_SYSTEM_CODE,
        PERMISSIONS.MANAGE_USERS,
        PERMISSIONS.ASSIGN_SUPERVISORS_MANAGERS,
        PERMISSIONS.MANAGE_VEHICLES_ALL_BRANCHES,
        PERMISSIONS.MANAGE_VEHICLES_BRANCH,
        PERMISSIONS.VIEW_VEHICLES_ALL_BRANCHES,
        PERMISSIONS.REGISTER_MAINTENANCE,
        PERMISSIONS.RECEIVE_MAINTENANCE_ALERTS,
        PERMISSIONS.VIEW_MAINTENANCE_ALL_BRANCHES,
        PERMISSIONS.UPDATE_MILEAGE_ALL_BRANCHES,
        PERMISSIONS.UPDATE_MILEAGE_BRANCH,
        PERMISSIONS.VIEW_REPORTS,
        PERMISSIONS.EXPORT_REPORTS,
        PERMISSIONS.GRANT_TEMPORARY_PERMISSIONS,
        PERMISSIONS.VIEW_DASHBOARD,
        PERMISSIONS.MANAGE_DRIVERS,
        USER_PERMISSIONS.VIEW_ALL_USERS,
        USER_PERMISSIONS.EDIT_ALL_USERS
    ],
    
    [ROLES.GENERAL_MANAGER]: [
        PERMISSIONS.MANAGE_USERS,
        PERMISSIONS.ASSIGN_SUPERVISORS_MANAGERS,
        PERMISSIONS.MANAGE_VEHICLES_ALL_BRANCHES,
        PERMISSIONS.MANAGE_VEHICLES_BRANCH,
        PERMISSIONS.VIEW_VEHICLES_ALL_BRANCHES,
        PERMISSIONS.REGISTER_MAINTENANCE,
        PERMISSIONS.RECEIVE_MAINTENANCE_ALERTS,
        PERMISSIONS.VIEW_MAINTENANCE_ALL_BRANCHES,
        PERMISSIONS.UPDATE_MILEAGE_ALL_BRANCHES,
        PERMISSIONS.UPDATE_MILEAGE_BRANCH,
        PERMISSIONS.VIEW_REPORTS,
        PERMISSIONS.EXPORT_REPORTS,
        PERMISSIONS.GRANT_TEMPORARY_PERMISSIONS,
        PERMISSIONS.VIEW_DASHBOARD,
        PERMISSIONS.MANAGE_DRIVERS,
        USER_PERMISSIONS.VIEW_ALL_USERS,
        USER_PERMISSIONS.EDIT_OPERATIONS_MANAGER_BRANCH,
        USER_PERMISSIONS.EDIT_FLEET_MANAGER_BRANCH
    ],
    
    [ROLES.OPERATIONS_MANAGER]: [
        PERMISSIONS.ASSIGN_SUPERVISORS_MANAGERS,
        PERMISSIONS.MANAGE_VEHICLES_ALL_BRANCHES,
        PERMISSIONS.MANAGE_VEHICLES_BRANCH,
        PERMISSIONS.VIEW_VEHICLES_ALL_BRANCHES,
        PERMISSIONS.REGISTER_MAINTENANCE,
        PERMISSIONS.RECEIVE_MAINTENANCE_ALERTS,
        PERMISSIONS.VIEW_MAINTENANCE_ALL_BRANCHES,
        PERMISSIONS.UPDATE_MILEAGE_ALL_BRANCHES,
        PERMISSIONS.UPDATE_MILEAGE_BRANCH,
        PERMISSIONS.VIEW_REPORTS,
        PERMISSIONS.EXPORT_REPORTS,
        PERMISSIONS.VIEW_DASHBOARD,
        PERMISSIONS.MANAGE_DRIVERS,
        USER_PERMISSIONS.VIEW_ALL_USERS,
        USER_PERMISSIONS.EDIT_FLEET_SUPERVISOR_BRANCH,
        USER_PERMISSIONS.EDIT_FLEET_STAFF_BRANCH,
        USER_PERMISSIONS.EDIT_DRIVER_BRANCH
    ],
    
    [ROLES.FLEET_MANAGER]: [
        PERMISSIONS.ASSIGN_SUPERVISORS_MANAGERS,
        PERMISSIONS.MANAGE_VEHICLES_ALL_BRANCHES,
        PERMISSIONS.MANAGE_VEHICLES_BRANCH,
        PERMISSIONS.VIEW_VEHICLES_ALL_BRANCHES,
        PERMISSIONS.REGISTER_MAINTENANCE,
        PERMISSIONS.RECEIVE_MAINTENANCE_ALERTS,
        PERMISSIONS.VIEW_MAINTENANCE_ALL_BRANCHES,
        PERMISSIONS.UPDATE_MILEAGE_ALL_BRANCHES,
        PERMISSIONS.UPDATE_MILEAGE_BRANCH,
        PERMISSIONS.VIEW_REPORTS,
        PERMISSIONS.EXPORT_REPORTS,
        PERMISSIONS.VIEW_DASHBOARD,
        PERMISSIONS.MANAGE_DRIVERS,
        USER_PERMISSIONS.VIEW_ALL_USERS,
        USER_PERMISSIONS.EDIT_FLEET_SUPERVISOR_BRANCH,
        USER_PERMISSIONS.EDIT_FLEET_STAFF_BRANCH,
        USER_PERMISSIONS.EDIT_DRIVER_BRANCH
    ],
    
    [ROLES.FLEET_SUPERVISOR]: [
        PERMISSIONS.MANAGE_VEHICLES_BRANCH,
        PERMISSIONS.VIEW_VEHICLES_BRANCH,
        PERMISSIONS.REGISTER_MAINTENANCE,
        PERMISSIONS.RECEIVE_MAINTENANCE_ALERTS,
        PERMISSIONS.VIEW_MAINTENANCE_BRANCH,
        PERMISSIONS.UPDATE_MILEAGE_ALL_BRANCHES,
        PERMISSIONS.UPDATE_MILEAGE_BRANCH,
        PERMISSIONS.VIEW_DASHBOARD,
        PERMISSIONS.MANAGE_DRIVERS,
        USER_PERMISSIONS.VIEW_ALL_USERS,
        USER_PERMISSIONS.EDIT_FLEET_STAFF_BRANCH,
        USER_PERMISSIONS.EDIT_DRIVER_BRANCH
    ],
    
    [ROLES.FLEET_STAFF]: [
        PERMISSIONS.VIEW_VEHICLES_BRANCH,
        PERMISSIONS.VIEW_MAINTENANCE_BRANCH,
        PERMISSIONS.UPDATE_MILEAGE_ALL_BRANCHES,
        PERMISSIONS.UPDATE_MILEAGE_BRANCH,
        PERMISSIONS.VIEW_DASHBOARD
    ],
    
    [ROLES.DRIVER]: [
        PERMISSIONS.VIEW_VEHICLES_ASSIGNED,
        PERMISSIONS.UPDATE_MILEAGE_ASSIGNED,
        PERMISSIONS.VIEW_DASHBOARD
    ]
};

/**
 * التحقق من وجود صلاحية محددة للمستخدم
 */
export function hasPermission(userRole, permission) {
    if (!userRole || !permission) return false;
    
    const permissions = ROLE_PERMISSIONS[userRole] || [];
    return permissions.includes(permission);
}

/**
 * التحقق من وجود أي من الصلاحيات المحددة للمستخدم
 */
export function hasAnyPermission(userRole, permissions) {
    if (!userRole || !permissions || !Array.isArray(permissions)) return false;
    
    const userPermissions = ROLE_PERMISSIONS[userRole] || [];
    return permissions.some(permission => userPermissions.includes(permission));
}

/**
 * التحقق من وجود جميع الصلاحيات المحددة للمستخدم
 */
export function hasAllPermissions(userRole, permissions) {
    if (!userRole || !permissions || !Array.isArray(permissions)) return false;
    
    const userPermissions = ROLE_PERMISSIONS[userRole] || [];
    return permissions.every(permission => userPermissions.includes(permission));
}

/**
 * الحصول على جميع صلاحيات دور محدد
 */
export function getRolePermissions(userRole) {
    return ROLE_PERMISSIONS[userRole] || [];
}

/**
 * التحقق من إمكانية الوصول لصفحة محددة
 */
export function canAccessPage(userRole, page) {
    const pagePermissions = {
        dashboard: [PERMISSIONS.VIEW_DASHBOARD],
        vehicles: [
            PERMISSIONS.VIEW_VEHICLES_ALL_BRANCHES,
            PERMISSIONS.VIEW_VEHICLES_BRANCH,
            PERMISSIONS.VIEW_VEHICLES_ASSIGNED
        ],
        maintenance: [
            PERMISSIONS.VIEW_MAINTENANCE_ALL_BRANCHES,
            PERMISSIONS.VIEW_MAINTENANCE_BRANCH,
            PERMISSIONS.REGISTER_MAINTENANCE
        ],
        fuel: [
            PERMISSIONS.UPDATE_MILEAGE_ALL_BRANCHES,
            PERMISSIONS.UPDATE_MILEAGE_BRANCH,
            PERMISSIONS.UPDATE_MILEAGE_ASSIGNED
        ],
        drivers: [PERMISSIONS.MANAGE_DRIVERS],
        users: [USER_PERMISSIONS.VIEW_ALL_USERS],
        reports: [PERMISSIONS.VIEW_REPORTS]
    };
    
    const requiredPermissions = pagePermissions[page];
    if (!requiredPermissions) return true; // صفحة غير محددة، السماح بالوصول
    
    return hasAnyPermission(userRole, requiredPermissions);
}

/**
 * التحقق من إمكانية تنفيذ إجراء محدد
 */
export function canPerformAction(userRole, action, context = {}) {
    const actionPermissions = {
        addVehicle: [
            PERMISSIONS.MANAGE_VEHICLES_ALL_BRANCHES,
            PERMISSIONS.MANAGE_VEHICLES_BRANCH
        ],
        editVehicle: [
            PERMISSIONS.MANAGE_VEHICLES_ALL_BRANCHES,
            PERMISSIONS.MANAGE_VEHICLES_BRANCH
        ],
        deleteVehicle: [
            PERMISSIONS.MANAGE_VEHICLES_ALL_BRANCHES,
            PERMISSIONS.MANAGE_VEHICLES_BRANCH
        ],
        addMaintenance: [PERMISSIONS.REGISTER_MAINTENANCE],
        updateMileage: [
            PERMISSIONS.UPDATE_MILEAGE_ALL_BRANCHES,
            PERMISSIONS.UPDATE_MILEAGE_BRANCH,
            PERMISSIONS.UPDATE_MILEAGE_ASSIGNED
        ],
        viewReports: [PERMISSIONS.VIEW_REPORTS],
        exportReports: [PERMISSIONS.EXPORT_REPORTS],
        manageUsers: [PERMISSIONS.MANAGE_USERS]
    };
    
    const requiredPermissions = actionPermissions[action];
    if (!requiredPermissions) return false;
    
    return hasAnyPermission(userRole, requiredPermissions);
}

export default {
    ROLES,
    ROLE_LABELS,
    PERMISSIONS,
    PERMISSION_LABELS,
    USER_PERMISSIONS,
    ROLE_PERMISSIONS,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    getRolePermissions,
    canAccessPage,
    canPerformAction
};

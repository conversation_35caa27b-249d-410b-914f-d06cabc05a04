/**
 * ChartWrapper Component - مغلف الرسوم البيانية
 * يوفر واجهة موحدة لإنشاء وإدارة الرسوم البيانية باستخدام Chart.js
 */

export class ChartWrapper {
    constructor(canvasId, options = {}) {
        this.canvasId = canvasId;
        this.canvas = document.getElementById(canvasId);
        this.chart = null;
        this.defaultOptions = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                    labels: {
                        usePointStyle: true,
                        padding: 20
                    }
                }
            }
        };
        this.options = { ...this.defaultOptions, ...options };
    }

    async init() {
        if (!this.canvas) {
            console.error(`Canvas with ID ${this.canvasId} not found`);
            return false;
        }

        // التأكد من تحميل Chart.js
        if (typeof Chart === 'undefined') {
            console.error('Chart.js is not loaded');
            return false;
        }

        return true;
    }

    async createChart(type, data, customOptions = {}) {
        if (!await this.init()) return null;

        // دمج الخيارات
        const chartOptions = this.mergeOptions(customOptions);

        // إنشاء الرسم البياني
        this.chart = new Chart(this.canvas, {
            type: type,
            data: data,
            options: chartOptions
        });

        return this.chart;
    }

    mergeOptions(customOptions) {
        return {
            ...this.options,
            ...customOptions,
            plugins: {
                ...this.options.plugins,
                ...customOptions.plugins
            }
        };
    }

    updateData(newData) {
        if (!this.chart) return;

        this.chart.data = newData;
        this.chart.update();
    }

    updateOptions(newOptions) {
        if (!this.chart) return;

        this.chart.options = this.mergeOptions(newOptions);
        this.chart.update();
    }

    destroy() {
        if (this.chart) {
            this.chart.destroy();
            this.chart = null;
        }
    }

    // رسوم بيانية محددة مسبقاً للمشروع
    async createPieChart(data, title = '') {
        const chartData = {
            labels: data.labels,
            datasets: [{
                data: data.values,
                backgroundColor: [
                    '#0057ff',
                    '#28a745',
                    '#fd7e14',
                    '#dc3545',
                    '#6f42c1',
                    '#17a2b8',
                    '#ffc107'
                ],
                borderWidth: 2,
                borderColor: '#fff'
            }]
        };

        const options = {
            plugins: {
                title: {
                    display: !!title,
                    text: title,
                    font: { size: 16, weight: 'bold' }
                },
                legend: {
                    position: 'bottom'
                }
            }
        };

        return await this.createChart('pie', chartData, options);
    }

    async createBarChart(data, title = '') {
        const chartData = {
            labels: data.labels,
            datasets: [{
                label: data.label || 'البيانات',
                data: data.values,
                backgroundColor: '#0057ff',
                borderColor: '#0057ff',
                borderWidth: 1
            }]
        };

        const options = {
            plugins: {
                title: {
                    display: !!title,
                    text: title,
                    font: { size: 16, weight: 'bold' }
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        };

        return await this.createChart('bar', chartData, options);
    }

    async createLineChart(data, title = '') {
        const chartData = {
            labels: data.labels,
            datasets: data.datasets.map((dataset, index) => ({
                label: dataset.label,
                data: dataset.data,
                borderColor: dataset.color || this.getDefaultColor(index),
                backgroundColor: dataset.color || this.getDefaultColor(index),
                fill: false,
                tension: 0.1
            }))
        };

        const options = {
            plugins: {
                title: {
                    display: !!title,
                    text: title,
                    font: { size: 16, weight: 'bold' }
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        };

        return await this.createChart('line', chartData, options);
    }

    async createDoughnutChart(data, title = '') {
        const chartData = {
            labels: data.labels,
            datasets: [{
                data: data.values,
                backgroundColor: [
                    '#0057ff',
                    '#28a745',
                    '#fd7e14',
                    '#dc3545',
                    '#6f42c1'
                ],
                borderWidth: 2,
                borderColor: '#fff'
            }]
        };

        const options = {
            plugins: {
                title: {
                    display: !!title,
                    text: title,
                    font: { size: 16, weight: 'bold' }
                },
                legend: {
                    position: 'bottom'
                }
            },
            cutout: '60%'
        };

        return await this.createChart('doughnut', chartData, options);
    }

    getDefaultColor(index) {
        const colors = [
            '#0057ff', '#28a745', '#fd7e14', '#dc3545', 
            '#6f42c1', '#17a2b8', '#ffc107', '#6c757d'
        ];
        return colors[index % colors.length];
    }
}

// دوال مساعدة للاستخدام السريع
export async function createQuickChart(canvasId, type, data, options = {}) {
    const wrapper = new ChartWrapper(canvasId);
    return await wrapper.createChart(type, data, options);
}

export async function createFleetPieChart(canvasId, vehicleData, title = 'توزيع المركبات') {
    const wrapper = new ChartWrapper(canvasId);
    return await wrapper.createPieChart(vehicleData, title);
}

export async function createMaintenanceBarChart(canvasId, maintenanceData, title = 'إحصائيات الصيانة') {
    const wrapper = new ChartWrapper(canvasId);
    return await wrapper.createBarChart(maintenanceData, title);
}

// إدارة الرسوم البيانية المتعددة
export class ChartManager {
    constructor() {
        this.charts = new Map();
    }

    async addChart(id, canvasId, type, data, options = {}) {
        const wrapper = new ChartWrapper(canvasId);
        const chart = await wrapper.createChart(type, data, options);
        
        if (chart) {
            this.charts.set(id, { wrapper, chart });
        }
        
        return chart;
    }

    getChart(id) {
        const chartData = this.charts.get(id);
        return chartData ? chartData.chart : null;
    }

    updateChart(id, newData) {
        const chartData = this.charts.get(id);
        if (chartData) {
            chartData.wrapper.updateData(newData);
        }
    }

    destroyChart(id) {
        const chartData = this.charts.get(id);
        if (chartData) {
            chartData.wrapper.destroy();
            this.charts.delete(id);
        }
    }

    destroyAll() {
        this.charts.forEach((chartData) => {
            chartData.wrapper.destroy();
        });
        this.charts.clear();
    }
}

export default ChartWrapper;

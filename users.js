// Import required variables and functions
import { users, currentUser, API_URL, openModal, closeModal, showNotification, showPage } from './script.js';
import { ROLES, hasPermission, PERMISSIONS } from './permissions.js';

// Render users table
export function renderUsersTable() {
    console.log('renderUsersTable called, current user role:', currentUser?.role);

    // Check if user has permission to view users page
    const allowedRolesToViewUsers = [
        ROLES.SUPER_ADMIN,
        ROLES.GENERAL_MANAGER,
        ROLES.OPERATIONS_MANAGER,
        ROLES.FLEET_MANAGER,
        ROLES.FLEET_SUPERVISOR
    ];

    console.log('Allowed roles to view users:', allowedRolesToViewUsers);
    console.log('Is current user role in allowed roles?', allowedRolesToViewUsers.includes(currentUser?.role));

    if (!allowedRolesToViewUsers.includes(currentUser?.role)) {
        console.log('User does not have permission to view users page');
        showNotification('You do not have permission to view users', 'error');
        return;
    }

    // Initialize the users page without calling showPage to avoid recursion
    initializeUsersPage();

    // Update the table with user data
    updateUsersTable();

    // Show success notification
    showNotification('Users data loaded successfully', 'success');

    // Update the users table
    updateUsersTable();
}

// Initialize users page
function initializeUsersPage() {
    console.log('Initializing users page');
    const page = document.getElementById('users-page');
    if (!page) {
        console.error('Users page element not found');
        return;
    }

    // Get the users content container
    const usersContent = document.getElementById('users-content');
    if (!usersContent) {
        console.error('Users content container not found');
        return;
    }

    // Check if the add user button already exists
    let addUserBtn = document.getElementById('add-user-btn');
    if (!addUserBtn) {
        // Add the add user button to the page header
        const pageHeader = page.querySelector('.page-header');
        if (pageHeader) {
            addUserBtn = document.createElement('button');
            addUserBtn.id = 'add-user-btn';
            addUserBtn.className = 'btn btn-primary';
            addUserBtn.innerHTML = '<i class="fas fa-plus"></i> Add New User';
            pageHeader.appendChild(addUserBtn);
        }
    }

    // Add the table to the users content container if it's empty
    if (!usersContent.querySelector('.table-responsive')) {
        usersContent.innerHTML = `
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Role</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        `;
    }

    // Add event listener to the load users button if it exists
    const loadUsersBtn = document.getElementById('load-users-btn');
    if (loadUsersBtn) {
        // Remove existing event listeners
        const newLoadUsersBtn = loadUsersBtn.cloneNode(true);
        loadUsersBtn.parentNode.replaceChild(newLoadUsersBtn, loadUsersBtn);

        // Add new event listener
        newLoadUsersBtn.addEventListener('click', async () => {
            console.log('Load users button clicked');
            try {
                await import('./script.js').then(module => {
                    if (module && typeof module.loadUsersData === 'function') {
                        module.loadUsersData();
                    } else {
                        console.error('loadUsersData function not found');
                    }
                });
            } catch (error) {
                console.error('Error loading users data:', error);
            }
        });
    }

    // Add event listeners to buttons
    addUserActionListeners();

    // We don't need to add event listener to the add user button here
    // because we're using event delegation in script.js
}

// Create a row in the users table
function createUserRow(user) {
    // Determine if current user can edit this user
    let canEdit = false;

    // Super Admin can edit all users
    if (currentUser.role === ROLES.SUPER_ADMIN) {
        canEdit = true;
    }
    // General Manager can edit Operations Manager and Fleet Manager
    else if (currentUser.role === ROLES.GENERAL_MANAGER &&
             (user.role === ROLES.OPERATIONS_MANAGER || user.role === ROLES.FLEET_MANAGER)) {
        canEdit = true;
    }
    // Operations Manager and Fleet Manager can edit Fleet Supervisor, Fleet Staff, and Driver
    else if ((currentUser.role === ROLES.OPERATIONS_MANAGER || currentUser.role === ROLES.FLEET_MANAGER) &&
             (user.role === ROLES.FLEET_SUPERVISOR || user.role === ROLES.FLEET_STAFF || user.role === ROLES.DRIVER)) {
        canEdit = true;
    }
    // Fleet Supervisor can edit Fleet Staff and Driver
    else if (currentUser.role === ROLES.FLEET_SUPERVISOR &&
             (user.role === ROLES.FLEET_STAFF || user.role === ROLES.DRIVER)) {
        canEdit = true;
    }

    // Don't allow editing yourself
    if (user.id === currentUser.id) {
        canEdit = false;
    }

    return `
        <tr>
            <td>${user.name}</td>
            <td>${user.email}</td>
            <td>${getUserRole(user.role)}</td>
            <td>
                <div class="action-buttons">
                    ${canEdit ? `
                        <button class="action-btn edit-btn" data-id="${user.id}" title="Edit">
                            <i class="fas fa-edit"></i>
                        </button>
                        ${currentUser.role === ROLES.SUPER_ADMIN ? `
                            <button class="action-btn delete-btn" data-id="${user.id}" title="Delete">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                        ` : ''}
                    ` : '—'}
                </div>
            </td>
        </tr>
    `;
}

// Add event listeners for user buttons
function addUserActionListeners() {
    // Edit buttons
    document.querySelectorAll('#users-page .edit-btn').forEach(btn => {
        btn.addEventListener('click', () => {
            const userId = btn.getAttribute('data-id');
            openUserModal(userId);
        });
    });

    // Delete buttons
    document.querySelectorAll('#users-page .delete-btn').forEach(btn => {
        btn.addEventListener('click', () => {
            const userId = btn.getAttribute('data-id');
            confirmDeleteUser(userId);
        });
    });
}

// Open add/edit user modal
export function openUserModal(userId = null) {
    const modal = document.getElementById('user-modal');
    if (!modal) {
        createUserModal();
        return;
    }

    // Update modal title
    modal.querySelector('.modal-title').textContent =
        userId ? 'Edit User' : 'Add New User';

    // Reset form
    const form = modal.querySelector('form');
    form.reset();
    form.querySelector('#user-id').value = userId || '';

    // Hide password field for edit mode
    const passwordGroup = form.querySelector('#user-password-group');
    if (passwordGroup) {
        passwordGroup.style.display = userId ? 'none' : 'block';
    }

    // Fill user data if editing
    let user = null;
    if (userId) {
        user = users.find(u => u.id === userId);
        if (user) {
            fillUserForm(user);
        }
    }

    // Show/hide fields based on user role and permissions
    const roleField = form.querySelector('#user-role');
    const branchField = form.querySelector('#user-branch');
    const managerField = form.querySelector('#user-manager');

    // Only Super Admin can change roles
    if (roleField) {
        if (currentUser.role !== ROLES.SUPER_ADMIN) {
            roleField.disabled = userId ? true : false; // Can set role when creating, not when editing
        } else {
            roleField.disabled = false;
        }
    }

    // Branch field is editable based on role relationships
    if (branchField) {
        let canEditBranch = false;

        if (user) {
            // Super Admin can edit all branches
            if (currentUser.role === ROLES.SUPER_ADMIN) {
                canEditBranch = true;
            }
            // General Manager can edit branch for Operations Manager and Fleet Manager
            else if (currentUser.role === ROLES.GENERAL_MANAGER &&
                    (user.role === ROLES.OPERATIONS_MANAGER || user.role === ROLES.FLEET_MANAGER)) {
                canEditBranch = true;
            }
            // Operations Manager and Fleet Manager can edit branch for Fleet Supervisor, Fleet Staff, and Driver
            else if ((currentUser.role === ROLES.OPERATIONS_MANAGER || currentUser.role === ROLES.FLEET_MANAGER) &&
                    (user.role === ROLES.FLEET_SUPERVISOR || user.role === ROLES.FLEET_STAFF || user.role === ROLES.DRIVER)) {
                canEditBranch = true;
            }
            // Fleet Supervisor can edit branch for Fleet Staff and Driver
            else if (currentUser.role === ROLES.FLEET_SUPERVISOR &&
                    (user.role === ROLES.FLEET_STAFF || user.role === ROLES.DRIVER)) {
                canEditBranch = true;
            }
        } else {
            // For new users, allow branch setting
            canEditBranch = true;
        }

        branchField.disabled = !canEditBranch;
    }

    // Update managers list
    updateManagersList();

    // Open modal
    openModal('user-modal');
}

// Create user modal
function createUserModal() {
    const modalHTML = `
        <div id="user-modal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title">Add New User</h3>
                    <button id="close-user-modal" class="close-btn">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <form id="user-form">
                    <input type="hidden" id="user-id">
                    <div class="form-group">
                        <label for="user-name">Name</label>
                        <input type="text" id="user-name" required>
                    </div>
                    <div class="form-group">
                        <label for="user-email">Email</label>
                        <input type="email" id="user-email" required>
                    </div>
                    <div class="form-group" id="user-password-group">
                        <label for="user-password">Password</label>
                        <input type="password" id="user-password" required>
                    </div>
                    <div class="form-group">
                        <label for="user-role">Role</label>
                        <select id="user-role" required>
                            <option value="">Select Role</option>
                            <option value="${ROLES.SUPER_ADMIN}">${ROLES.SUPER_ADMIN}</option>
                            <option value="${ROLES.GENERAL_MANAGER}">${ROLES.GENERAL_MANAGER}</option>
                            <option value="${ROLES.OPERATIONS_MANAGER}">${ROLES.OPERATIONS_MANAGER}</option>
                            <option value="${ROLES.FLEET_MANAGER}">${ROLES.FLEET_MANAGER}</option>
                            <option value="${ROLES.FLEET_SUPERVISOR}">${ROLES.FLEET_SUPERVISOR}</option>
                            <option value="${ROLES.FLEET_STAFF}">${ROLES.FLEET_STAFF}</option>
                            <option value="${ROLES.DRIVER}">${ROLES.DRIVER}</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="user-branch">Branch</label>
                        <select id="user-branch">
                            <option value="">Select Branch</option>
                            <option value="Cairo">Cairo</option>
                            <option value="Alexandria">Alexandria</option>
                            <option value="Gouna">Gouna</option>
                            <option value="Hurghada">Hurghada</option>
                            <option value="Sharm El Sheikh">Sharm El Sheikh</option>
                        </select>
                    </div>
                    <div class="form-group" id="user-manager-group" style="display: none;">
                        <label for="user-manager">Manager</label>
                        <select id="user-manager"></select>
                    </div>
                    <div class="modal-footer">
                        <button type="submit" class="btn btn-primary">Save</button>
                        <button type="button" id="cancel-user-form" class="btn btn-secondary">Cancel</button>
                    </div>
                </form>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHTML);
    document.getElementById('user-form').addEventListener('submit', handleUserSubmit);

    // Add event listener to the cancel button
    const cancelBtn = document.getElementById('cancel-user-form');
    if (cancelBtn) {
        cancelBtn.addEventListener('click', () => {
            closeModal('user-modal');
        });
    }

    // Add event listener to the close button
    const closeBtn = document.getElementById('close-user-modal');
    if (closeBtn) {
        closeBtn.addEventListener('click', () => {
            closeModal('user-modal');
        });
    }
}

// Fill user form with user data
function fillUserForm(user) {
    const form = document.getElementById('user-form');
    form.querySelector('#user-name').value = user.name;
    form.querySelector('#user-email').value = user.email;
    form.querySelector('#user-role').value = user.role;

    // Set branch if available
    const branchSelect = form.querySelector('#user-branch');
    if (branchSelect && user.branch) {
        branchSelect.value = user.branch;
    }

    const managerGroup = form.querySelector('#user-manager-group');
    const managerSelect = form.querySelector('#user-manager');

    if (user.role === ROLES.DRIVER || user.role === ROLES.FLEET_STAFF || user.role === ROLES.FLEET_SUPERVISOR) {
        managerGroup.style.display = 'block';
        managerSelect.value = user.manager || '';
    } else {
        managerGroup.style.display = 'none';
    }
}

// Update managers list
function updateManagersList() {
    const managerSelect = document.getElementById('user-manager');
    if (!managerSelect) return;

    const managers = users.filter(user =>
        user.role === ROLES.FLEET_MANAGER ||
        user.role === ROLES.OPERATIONS_MANAGER ||
        user.role === ROLES.GENERAL_MANAGER
    );

    managerSelect.innerHTML = '<option value="">Select Manager</option>' +
        managers.map(manager =>
            `<option value="${manager.id}">${manager.name}</option>`
        ).join('');
}

// Handle user form submission
export async function handleUserSubmit(event) {
    event.preventDefault();

    const form = event.target;
    const userData = {
        id: form.querySelector('#user-id').value,
        name: form.querySelector('#user-name').value,
        email: form.querySelector('#user-email').value,
        password: form.querySelector('#user-password')?.value,
        role: form.querySelector('#user-role').value,
        branch: form.querySelector('#user-branch').value || null,
        manager: form.querySelector('#user-manager').value || null
    };

    // Add current user role for permission checking on server
    userData.currentUserRole = currentUser.role;

    try {
        const response = await fetch(API_URL, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                action: userData.id ? 'updateUser' : 'addUser',
                ...userData
            })
        });

        const result = await response.json();

        if (result.status === 'success') {
            if (userData.id) {
                const index = users.findIndex(u => u.id === userData.id);
                if (index !== -1) users[index] = result.data;
                showNotification(`User ${userData.name} updated successfully`, 'success');
            } else {
                users.push(result.data);
                showNotification(`User ${userData.name} added successfully`, 'success');
            }

            closeModal('user-modal');
            updateUsersTable();
        } else {
            showNotification(result.message, 'error');
            throw new Error(result.message);
        }
    } catch (error) {
        console.error('Error submitting user form:', error);
        showNotification('Error saving data: ' + error.message, 'error');
    }
}

// Confirm delete user
function confirmDeleteUser(userId) {
    const user = users.find(u => u.id === userId);
    if (!user) return;

    // Create confirmation modal if it doesn't exist
    let confirmModal = document.getElementById('confirm-delete-modal');
    if (!confirmModal) {
        const modalHTML = `
            <div id="confirm-delete-modal" class="modal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3 class="modal-title">Confirm Delete</h3>
                        <button id="close-confirm-modal" class="close-btn">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <p id="confirm-message"></p>
                    </div>
                    <div class="modal-footer">
                        <button id="confirm-delete-btn" class="btn btn-danger">Delete</button>
                        <button id="cancel-delete-btn" class="btn btn-secondary">Cancel</button>
                    </div>
                </div>
            </div>
        `;
        document.body.insertAdjacentHTML('beforeend', modalHTML);
        confirmModal = document.getElementById('confirm-delete-modal');

        // Add event listener to the close button
        const closeBtn = document.getElementById('close-confirm-modal');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                closeModal('confirm-delete-modal');
            });
        }

        // Add event listener to the cancel button
        const cancelBtn = document.getElementById('cancel-delete-btn');
        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => {
                closeModal('confirm-delete-modal');
            });
        }
    }

    // Set the confirmation message
    const confirmMessage = document.getElementById('confirm-message');
    if (confirmMessage) {
        confirmMessage.textContent = `Are you sure you want to delete user ${user.name}?`;
    }

    // Add event listener to the confirm button
    const confirmBtn = document.getElementById('confirm-delete-btn');
    if (confirmBtn) {
        // Remove existing event listeners
        const newConfirmBtn = confirmBtn.cloneNode(true);
        confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);

        // Add new event listener
        newConfirmBtn.addEventListener('click', () => {
            closeModal('confirm-delete-modal');
            deleteUser(userId);
        });
    }

    // Open the confirmation modal
    openModal('confirm-delete-modal');
}

// Delete user
async function deleteUser(userId) {
    try {
        const response = await fetch(API_URL, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                action: 'deleteUser',
                id: userId,
                currentUserRole: currentUser.role
            })
        });

        const result = await response.json();

        if (result.status === 'success') {
            const userName = users.find(u => u.id === userId)?.name || 'User';
            users = users.filter(u => u.id !== userId);
            updateUsersTable();
            showNotification(`User ${userName} deleted successfully`, 'success');
        } else {
            showNotification(result.message, 'error');
            throw new Error(result.message);
        }
    } catch (error) {
        console.error('Error deleting user:', error);
        showNotification('Error deleting user: ' + error.message, 'error');
    }
}

// Translate user role
function getUserRole(role) {
    // Return the role directly as we're now using the display names from ROLES
    return role || 'Unknown';
}

// Update users table with current data
function updateUsersTable() {
    console.log('Updating users table with', users.length, 'users');

    const page = document.getElementById('users-page');
    if (!page) {
        console.error('Users page element not found');
        return;
    }

    // Get the users content container
    const usersContent = document.getElementById('users-content');
    if (!usersContent) {
        console.error('Users content container not found');
        return;
    }

    // Get the table body
    const tableBody = usersContent.querySelector('table tbody');
    if (!tableBody) {
        console.error('Table body element not found');
        return;
    }

    // Create table rows
    tableBody.innerHTML = users.length ?
        users.map(createUserRow).join('') :
        '<tr><td colspan="4" class="text-center">No users found</td></tr>';

    // Add event listeners to buttons
    addUserActionListeners();
}

// Import required modules
import { showNotification } from './script.js';

// Function to calculate and display maintenance statistics
export function calculateMaintenanceStats(maintenanceRecords) {
    try {
        if (!maintenanceRecords || !Array.isArray(maintenanceRecords) || maintenanceRecords.length === 0) {
            console.log('No maintenance records available for statistics');
            return;
        }

        console.log('Calculating maintenance statistics from', maintenanceRecords.length, 'records');

        // 1. Total number of maintenance records
        const totalRecords = maintenanceRecords.length;
        updateStatElement('total-records', totalRecords);

        // 2. Total maintenance cost (sum of "Total Cost")
        const totalCost = maintenanceRecords.reduce((sum, record) => {
            const cost = parseFloat(record['Total Cost'] || 0);
            return sum + (isNaN(cost) ? 0 : cost);
        }, 0);
        updateStatElement('total-cost', '£' + totalCost.toLocaleString());

        // 3. Average cost per maintenance
        const avgCost = totalRecords > 0 ? totalCost / totalRecords : 0;
        updateStatElement('avg-cost', '£' + avgCost.toFixed(2));

        // 4. Number of unique "Service Type"
        const uniqueServiceTypes = new Set(
            maintenanceRecords.map(record => record['Service Type']).filter(Boolean)
        ).size;
        updateStatElement('service-types', uniqueServiceTypes);

        // 5. Number of unique "Vehicle ID"
        const uniqueVehicles = new Set(
            maintenanceRecords.map(record => record['Vehicle ID']).filter(Boolean)
        ).size;
        updateStatElement('unique-vehicles', uniqueVehicles);

        // 6. Total "Parts Cost"
        const totalPartsCost = maintenanceRecords.reduce((sum, record) => {
            const cost = parseFloat(record['Parts Cost'] || 0);
            return sum + (isNaN(cost) ? 0 : cost);
        }, 0);
        updateStatElement('parts-cost', '£' + totalPartsCost.toLocaleString());

        // 7. Total "Labor Cost"
        const totalLaborCost = maintenanceRecords.reduce((sum, record) => {
            const cost = parseFloat(record['Labor Cost'] || 0);
            return sum + (isNaN(cost) ? 0 : cost);
        }, 0);
        updateStatElement('labor-cost', '£' + totalLaborCost.toLocaleString());

        // 8. Count of records with critical notes
        const criticalKeywords = ['high-cost', 'critical'];
        const criticalRecords = maintenanceRecords.filter(record => {
            if (!record['Notes']) return false;
            const notes = record['Notes'].toLowerCase();
            return criticalKeywords.some(keyword => notes.includes(keyword));
        }).length;
        updateStatElement('critical-records', criticalRecords);

        // 9. Number of unique "Service Center"
        const uniqueServiceCenters = new Set(
            maintenanceRecords.map(record => record['Service Center']).filter(Boolean)
        ).size;
        updateStatElement('service-centers', uniqueServiceCenters);

        // 10. Nearest "Next Service Date" from today
        const today = new Date();
        const futureServiceDates = maintenanceRecords
            .filter(record => record['Next Service Date'])
            .map(record => ({
                date: new Date(record['Next Service Date']),
                record: record
            }))
            .filter(item => item.date >= today)
            .sort((a, b) => a.date - b.date);

        if (futureServiceDates.length > 0) {
            const nearestDate = futureServiceDates[0].date;
            updateStatElement('next-service', nearestDate.toLocaleDateString());
        } else {
            updateStatElement('next-service', 'N/A');
        }

    } catch (error) {
        console.error('Error calculating maintenance statistics:', error);
        showNotification('Error calculating maintenance statistics', 'error');
    }
}

// Helper function to update all stat elements with the same ID across different pages
function updateStatElement(elementId, value) {
    // Get all elements with the given ID (one in maintenance page, one in dashboard)
    const elements = document.querySelectorAll(`#${elementId}`);

    // Update each element
    elements.forEach(element => {
        if (element) {
            // Update the text content
            element.textContent = value;

            // Highlight the stat card briefly
            const statCard = element.closest('.maintenance-stat');
            if (statCard) {
                statCard.classList.add('update-highlight');
                setTimeout(() => {
                    statCard.classList.remove('update-highlight');
                }, 1000);
            }
        }
    });
}

// Function to initialize maintenance stats dashboard
export function initializeMaintenanceStats() {
    try {
        // Initialize stats in the maintenance page
        initializeMaintenancePageStats();

        // Initialize stats in the dashboard page
        initializeDashboardMaintenanceStats();

        // If maintenanceRecords is available, calculate stats
        if (window.maintenanceRecords && Array.isArray(window.maintenanceRecords)) {
            calculateMaintenanceStats(window.maintenanceRecords);
        } else {
            console.log('Maintenance records not available yet, will calculate stats when data is loaded');
        }
    } catch (error) {
        console.error('Error initializing maintenance stats:', error);
    }
}

// Function to initialize maintenance stats in the maintenance page
function initializeMaintenancePageStats() {
    // Check if stats container exists, if not create it
    let statsContainer = document.querySelector('#maintenance-page .maintenance-stats-container');
    if (!statsContainer) {
        // Find the maintenance page
        const maintenancePage = document.getElementById('maintenance-page');
        if (!maintenancePage) {
            console.error('Maintenance page not found');
            return;
        }

        // Create stats container
        statsContainer = document.createElement('div');
        statsContainer.className = 'maintenance-stats-container';

        // Find the page header and insert the stats container after it
        const pageHeader = maintenancePage.querySelector('.page-header');
        if (pageHeader) {
            pageHeader.insertAdjacentElement('afterend', statsContainer);
        } else {
            // If no page header, insert at the beginning of the page
            const firstChild = maintenancePage.firstChild;
            if (firstChild) {
                maintenancePage.insertBefore(statsContainer, firstChild);
            } else {
                maintenancePage.appendChild(statsContainer);
            }
        }

        // Create stats grid
        createMaintenanceStatsGrid(statsContainer);
    }
}

// Function to initialize maintenance stats in the dashboard page
function initializeDashboardMaintenanceStats() {
    // Check if stats container exists in dashboard, if not create it
    let dashboardStatsContainer = document.querySelector('#dashboard-page .maintenance-stats-container');
    if (!dashboardStatsContainer) {
        // Find the dashboard page
        const dashboardPage = document.getElementById('dashboard-page');
        if (!dashboardPage) {
            console.error('Dashboard page not found');
            return;
        }

        // Create stats container
        dashboardStatsContainer = document.createElement('div');
        dashboardStatsContainer.className = 'maintenance-stats-container';

        // Find the services-stats section and insert the stats container after it
        const servicesStats = dashboardPage.querySelector('.services-stats');
        if (servicesStats) {
            // Add a title for the maintenance stats section
            const titleDiv = document.createElement('div');
            titleDiv.className = 'dashboard-section-title';
            titleDiv.innerHTML = '<h2 class="section-title">Maintenance Statistics</h2>';

            // Insert the title and stats container after services-stats
            servicesStats.insertAdjacentElement('afterend', titleDiv);
            titleDiv.insertAdjacentElement('afterend', dashboardStatsContainer);
        } else {
            // If no services-stats, insert after dashboard-stats
            const dashboardStats = dashboardPage.querySelector('.dashboard-stats');
            if (dashboardStats) {
                // Add a title for the maintenance stats section
                const titleDiv = document.createElement('div');
                titleDiv.className = 'dashboard-section-title';
                titleDiv.innerHTML = '<h2 class="section-title">Maintenance Statistics</h2>';

                // Insert the title and stats container after dashboard-stats
                dashboardStats.insertAdjacentElement('afterend', titleDiv);
                titleDiv.insertAdjacentElement('afterend', dashboardStatsContainer);
            } else {
                // If no dashboard-stats, insert at the beginning of the page
                const firstChild = dashboardPage.firstChild;
                if (firstChild) {
                    // Add a title for the maintenance stats section
                    const titleDiv = document.createElement('div');
                    titleDiv.className = 'dashboard-section-title';
                    titleDiv.innerHTML = '<h2 class="section-title">Maintenance Statistics</h2>';

                    // Insert the title and stats container at the beginning
                    dashboardPage.insertBefore(dashboardStatsContainer, firstChild);
                    dashboardPage.insertBefore(titleDiv, dashboardStatsContainer);
                } else {
                    // Add a title for the maintenance stats section
                    const titleDiv = document.createElement('div');
                    titleDiv.className = 'dashboard-section-title';
                    titleDiv.innerHTML = '<h2 class="section-title">Maintenance Statistics</h2>';

                    // Append the title and stats container
                    dashboardPage.appendChild(titleDiv);
                    dashboardPage.appendChild(dashboardStatsContainer);
                }
            }
        }

        // Create stats grid
        createMaintenanceStatsGrid(dashboardStatsContainer);
    }
}

// Function to create maintenance stats grid
function createMaintenanceStatsGrid(container) {
    const statsGridHTML = `
        <div class="maintenance-stats-grid">
            <!-- Card 1: Total Maintenance Records -->
            <div class="maintenance-stat">
                <div class="stat-icon blue">
                    <i class="fas fa-clipboard-list"></i>
                </div>
                <div class="stat-info">
                    <h3>Total Records</h3>
                    <p id="total-records">0</p>
                </div>
            </div>

            <!-- Card 2: Total Maintenance Cost -->
            <div class="maintenance-stat">
                <div class="stat-icon green">
                    <i class="fas fa-dollar-sign"></i>
                </div>
                <div class="stat-info">
                    <h3>Total Cost</h3>
                    <p id="total-cost">£0</p>
                </div>
            </div>

            <!-- Card 3: Average Cost Per Maintenance -->
            <div class="maintenance-stat">
                <div class="stat-icon purple">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="stat-info">
                    <h3>Average Cost</h3>
                    <p id="avg-cost">£0</p>
                </div>
            </div>

            <!-- Card 4: Unique Service Types -->
            <div class="maintenance-stat">
                <div class="stat-icon orange">
                    <i class="fas fa-tools"></i>
                </div>
                <div class="stat-info">
                    <h3>Service Types</h3>
                    <p id="service-types">0</p>
                </div>
            </div>

            <!-- Card 5: Unique Vehicles -->
            <div class="maintenance-stat">
                <div class="stat-icon indigo">
                    <i class="fas fa-car"></i>
                </div>
                <div class="stat-info">
                    <h3>Unique Vehicles</h3>
                    <p id="unique-vehicles">0</p>
                </div>
            </div>

            <!-- Card 6: Total Parts Cost -->
            <div class="maintenance-stat">
                <div class="stat-icon red">
                    <i class="fas fa-cogs"></i>
                </div>
                <div class="stat-info">
                    <h3>Parts Cost</h3>
                    <p id="parts-cost">£0</p>
                </div>
            </div>

            <!-- Card 7: Total Labor Cost -->
            <div class="maintenance-stat">
                <div class="stat-icon teal">
                    <i class="fas fa-user-cog"></i>
                </div>
                <div class="stat-info">
                    <h3>Labor Cost</h3>
                    <p id="labor-cost">£0</p>
                </div>
            </div>

            <!-- Card 8: Critical Maintenance Records -->
            <div class="maintenance-stat">
                <div class="stat-icon amber">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="stat-info">
                    <h3>Critical Records</h3>
                    <p id="critical-records">0</p>
                </div>
            </div>

            <!-- Card 9: Unique Service Centers -->
            <div class="maintenance-stat">
                <div class="stat-icon pink">
                    <i class="fas fa-building"></i>
                </div>
                <div class="stat-info">
                    <h3>Service Centers</h3>
                    <p id="service-centers">0</p>
                </div>
            </div>

            <!-- Card 10: Nearest Service Date -->
            <div class="maintenance-stat">
                <div class="stat-icon cyan">
                    <i class="fas fa-calendar-alt"></i>
                </div>
                <div class="stat-info">
                    <h3>Next Service</h3>
                    <p id="next-service">N/A</p>
                </div>
            </div>
        </div>
    `;

    container.innerHTML = statsGridHTML;
}

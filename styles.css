/* الخطوط والإعدادات العامة */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap');
@import url('https://fonts.cdnfonts.com/css/sf-pro-display');
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap');
:root {
    --primary-color: #0057ff;
    --secondary-color: #222222;
    --success-color: #00b478;
    --danger-color: #ff3c4c;
    --warning-color: #ffa500;
    --background-color: #ffffff;
    --text-color: #3d3d3d;
    --text-color-light: #666666;
    --text-color-dark: #1b1b1b;
    --border-color: #e8e8e8;
    --hover-color: #f9f9f9;
    --card-bg-color: #ffffff;
    --accent-color: #0057ff;
    --info-color: #0057ff;
    --sidebar-width: 250px;
    --header-height: 60px;
    --border-radius: 4px;
    --box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    --transition-speed: 0.2s;
    --primary-color-dark: #0046cc;
    --warning-color-rgb: 255, 165, 0;
    --danger-color-rgb: 255, 60, 76;
    --primary-color-rgb: 0, 87, 255;
    --border-radius-lg: 12px;
    --box-shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.15);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
    background-color: #fafafa;
    color: var(--text-color);
    line-height: 1.5;
    direction: ltr;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

a {
    text-decoration: none;
    color: inherit;
}

ul {
    list-style: none;
}

/* تنسيق الأزرار */
.btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--border-radius);
    font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-speed);
    font-size: 0.95rem;
    letter-spacing: -0.01em;
    position: relative;
    overflow: hidden;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-success {
    background-color: var(--success-color);
    color: white;
}

.btn-danger {
    background-color: var(--secondary-color);
    color: white;
}

.btn-secondary {
    background-color: var(--secondary-color);
    color: white;
}

.btn-warning {
    background-color: var(--warning-color);
    color: white;
}

/* هيكل التطبيق */
.app-container {
    display: flex;
    min-height: 100vh;
}

/* تنسيق صفحة المركبات */
#vehicles-page, #maintenance-page {
    padding: 24px;
    background-color: var(--background-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow);
}

#vehicles-page .page-header, #maintenance-page .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    gap: 16px;
}

#vehicles-page .search-filter {
    flex: 1;
}

#vehicles-page .search-box {
    position: relative;
    max-width: 400px;
}

#vehicles-page .search-box input {
    width: 100%;
    padding: 12px 16px 12px 40px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 0.95rem;
    transition: all 0.3s ease;
}

@media (max-width: 768px) {
    #vehicles-page .search-box input {
        padding: 10px 16px 10px 35px;
        font-size: 0.9rem;
    }
}

/* Modal Responsive Styles */
.modal {
    padding: 15px;
}

.modal-content {
    width: 95%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
}

/* Dashboard Responsive Styles */
/* Dashboard and services stats grid layouts are now in unified-stats.css */

@media (max-width: 480px) {
    .modal {
        padding: 10px;
    }

    .modal-content {
        width: 100%;
        border-radius: 8px;
    }

    .modal-header {
        padding: 15px;
    }

    .modal-body {
        padding: 15px;
    }

    /* Stat card mobile styles are now in unified-stats.css */
}

#vehicles-page .search-box input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.1);
}

#vehicles-page .search-box i {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-color-light);
}

#vehicles-page .button-group {
    display: flex;
    gap: 12px;
}

#vehicles-page .btn-icon {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    font-weight: 600;
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
}

#vehicles-page .btn-icon i {
    font-size: 1.1rem;
}

#vehicles-page .table, #maintenance-page .table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    margin-top: 16px;
    background: white;
    border-radius: var(--border-radius);
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

@media (max-width: 768px) {
    #vehicles-page .table, #maintenance-page .table {
        font-size: 0.9rem;
    }

    #vehicles-page .table th,
    #vehicles-page .table td,
    #maintenance-page .table th,
    #maintenance-page .table td {
        padding: 12px 8px;
        white-space: nowrap;
    }

    #vehicles-page .status-badge, #maintenance-page .status-badge {
        padding: 4px 8px;
        font-size: 0.8rem;
    }
}

#vehicles-page .table th, #maintenance-page .table th {
    background-color: #f8f9fa;
    padding: 16px;
    font-weight: 600;
    text-align: left;
    border-bottom: 2px solid var(--border-color);
    color: var(--text-color-dark);
}

#vehicles-page .table td, #maintenance-page .table td {
    padding: 16px;
    border-bottom: 1px solid var(--border-color);
    color: var(--text-color);
}

#vehicles-page .table tbody tr:hover, #maintenance-page .table tbody tr:hover {
    background-color: var(--hover-color);
}

#vehicles-page .status-badge {
    display: inline-flex;
    align-items: center;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
}

#vehicles-page .status-badge.active {
    background-color: rgba(var(--primary-color-rgb), 0.1);
    color: var(--primary-color);
}

#vehicles-page .status-badge.maintenance {
    background-color: rgba(var(--warning-color-rgb), 0.1);
    color: var(--warning-color);
}

#vehicles-page .status-badge.inactive {
    background-color: rgba(var(--danger-color-rgb), 0.1);
    color: var(--danger-color);
}

/* Dark mode styles for vehicles table */
body.dark-mode #vehicles-page, body.dark-mode #maintenance-page {
    background-color: #1a1f2e;
}

body.dark-mode #vehicles-page .table, body.dark-mode #maintenance-page .table {
    background-color: #1e2433;
    border: 1px solid #2d3748;
}

body.dark-mode #vehicles-page .table th, body.dark-mode #maintenance-page .table th {
    background-color: #2d3748;
    color: #e2e8f0;
    border-bottom: 2px solid #4a5568;
}

body.dark-mode #vehicles-page .table td, body.dark-mode #maintenance-page .table td {
    color: #e2e8f0;
    border-bottom: 1px solid #2d3748;
}

body.dark-mode #vehicles-page .table tbody tr:hover, body.dark-mode #maintenance-page .table tbody tr:hover {
    background-color: #2d3748;
}

body.dark-mode #vehicles-page .status-badge {
    background-color: #2d3748;
}

body.dark-mode #vehicles-page .status-badge.active {
    background-color: rgba(52, 211, 153, 0.2);
    color: #4ade80;
}

body.dark-mode #vehicles-page .status-badge.maintenance {
    background-color: rgba(245, 158, 11, 0.2);
    color: #fbbf24;
}

body.dark-mode #vehicles-page .status-badge.inactive {
    background-color: rgba(239, 68, 68, 0.2);
    color: #f87171;
}

body.dark-mode #vehicles-page .search-box input {
    background-color: #2d3748;
    border-color: #4a5568;
    color: #e2e8f0;
}

body.dark-mode #vehicles-page .search-box input:focus {
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.2);
}

body.dark-mode #vehicles-page .search-box i {
    color: #9ca3af;
}

/* تنسيق صفحة الصيانة */
#maintenance-page .maintenance-records-section {
    margin-top: 24px;
    margin-bottom: 24px;
    background-color: var(--card-bg-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow);
    padding: 20px;
}

#maintenance-page .section-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 16px;
    color: var(--text-color-dark);
}

#maintenance-page .action-buttons {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
}

#maintenance-page .action-buttons .btn {
    padding: 6px 12px;
    font-size: 0.85rem;
    transition: all 0.2s ease;
}

#maintenance-page .action-buttons .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

#maintenance-page .btn-sm {
    padding: 5px 10px;
    font-size: 0.85rem;
}

#maintenance-page .btn-primary {
    background-color: var(--primary-color);
    color: white;
}

#maintenance-page .btn-danger {
    background-color: var(--danger-color);
    color: white;
}

#maintenance-page .table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    margin-bottom: 20px;
}

/* تنسيق نافذة الصيانة المنبثقة */
#maintenance-modal .modal-content {
    max-width: 700px;
}

#maintenance-modal .form-group {
    margin-bottom: 16px;
}

#maintenance-modal label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--text-color-dark);
}

#maintenance-modal input,
#maintenance-modal select,
#maintenance-modal textarea {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 0.95rem;
    transition: all 0.3s ease;
}

#maintenance-modal input:focus,
#maintenance-modal select:focus,
#maintenance-modal textarea:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.1);
    outline: none;
}

/* تنسيق حالات الصيانة المختلفة */
#maintenance-records-table .license-expired,
#maintenance-records-table .license-expiry-warning {
    position: relative;
}

#maintenance-records-table .license-expired {
    color: var(--danger-color);
    font-weight: 600;
}

#maintenance-records-table .license-expiry-warning {
    color: var(--warning-color);
    font-weight: 600;
}

/* تنسيق الوضع المظلم لصفحة الصيانة */
body.dark-mode #maintenance-page .section-title {
    color: #e2e8f0;
}

body.dark-mode #maintenance-modal label {
    color: #e2e8f0;
}

body.dark-mode #maintenance-modal input,
body.dark-mode #maintenance-modal select,
body.dark-mode #maintenance-modal textarea {
    background-color: #2d3748;
    border-color: #4a5568;
    color: #e2e8f0;
}

body.dark-mode #maintenance-modal input:focus,
body.dark-mode #maintenance-modal select:focus,
body.dark-mode #maintenance-modal textarea:focus {
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.2);
}

body.dark-mode #maintenance-page .maintenance-records-section {
    background-color: #1e2433;
    border: 1px solid #2d3748;
}

/* تنسيق صفحة تسجيل الدخول */
.header {
    text-align: center;
    margin-bottom: 15px; /* تقليل المسافة من 20px */
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    max-width: 600px;
}

.header img {
    width: 150px; /* تقليل حجم الصورة من 200px */
    height: auto;
    margin-bottom: 20px; /* تقليل المسافة من 32px */
    animation: scale-in 0.5s ease-out;
}

.header h1 {
    font-size: 24px; /* تقليل حجم الخط من 28px */
    font-weight: 700;
    color: var(--text-color-dark);
    line-height: 1.3; /* تقليل ارتفاع السطر من 1.4 */
    letter-spacing: -0.02em;
    max-width: 500px;
    margin-bottom: 12px; /* تقليل المسافة من 16px */
    animation: fade-in 0.6s ease-out;
}

@keyframes fade-in {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes scale-in {
    from {
        transform: scale(0.95);
        opacity: 0;
    }
    to {
        transform: scale(1);
        opacity: 1;
    }
}

.login-box {
    background-color: var(--card-bg-color);
    border: none;
    padding: 25px 30px;
    border-radius: var(--border-radius);
    box-shadow: 0 1px 2px rgba(0,0,0,0.07),
                0 2px 4px rgba(0,0,0,0.07),
                0 4px 8px rgba(0,0,0,0.07);
    width: 90%;
    max-width: 500px;
    animation: slide-up 0.4s ease-out;
    margin: 0 auto;
}

@media (max-width: 480px) {
    .login-box {
        padding: 20px;
        width: 95%;
    }

    .input-container {
        margin-bottom: 15px;
    }

    .input-container input {
        font-size: 14px;
        padding: 10px 12px 10px 35px;
    }

    .input-container .icon {
        font-size: 16px;
        left: 10px;
    }
}

.login-box h2 {
    margin-bottom: 20px; /* تقليل المسافة من 32px */
    font-size: 20px; /* الحفاظ على الحجم 20px */
    font-weight: 600;
    text-align: center;
    color: var(--text-color-dark);
    letter-spacing: -0.02em;
}

@keyframes slide-up {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Animation delays for sequential appearance */
.header img {
    animation: scale-in 0.6s ease-out;
}

.header h1 {
    animation: fade-in 0.6s ease-out 0.2s both;
}

.login-box {
    animation: slide-up 0.5s ease-out 0.4s both;
}

/* Add Vehicle Form Styles */
.form-section {
    background-color: var(--card-bg-color);
    border-radius: var(--border-radius);
    padding: 2rem;
    border: 1px solid var(--border-color);
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
}

.form-group {
    margin-bottom: 0.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-color);
    font-size: 0.875rem;
}

.form-control {
    width: 100%;
    padding: 0.75rem;
    background-color: var(--background-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 0.875rem;
    color: var(--text-color);
    transition: all var(--transition-speed);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(0, 87, 255, 0.2);
}

.submit-btn {
    width: 100%;
    padding: 1rem;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    font-size: 0.975rem;
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-speed);
    margin-top: 1.5rem;
}

.submit-btn:hover {
    opacity: 0.9;
    transform: translateY(-1px);
}

.submit-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.header-top {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 0.5rem;
}

.header-top img {
    height: 100px;
    width: auto;
    margin-left: auto;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .dashboard-charts {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 1024px) {
    .form-grid {
        grid-template-columns: 1fr;
    }

    .dashboard-stats {
        grid-template-columns: repeat(2, 1fr);
    }

    .services-stats {
        grid-template-columns: repeat(2, 1fr);
    }

    .chart-container {
        min-height: 300px;
    }
}

@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        left: -250px;
        top: 0;
        height: 100vh;
        z-index: 1000;
        transition: left 0.3s ease;
    }

    .sidebar.active {
        left: 0;
    }

    .main-content {
        margin-left: 0;
        width: 100%;
    }

    .page-header {
        flex-direction: column;
    }

    .search-box {
        max-width: 100%;
        margin-bottom: 1rem;
    }

    .button-group {
        width: 100%;
        justify-content: space-between;
    }

    .table-responsive {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    .table th,
    .table td {
        min-width: 120px;
    }

    .modal-content {
        width: 95%;
        margin: 20px auto;
    }

    .header h1 {
        font-size: 20px;
    }

    .login-box {
        width: 90%;
        padding: 20px;
    }
}

@media (max-width: 576px) {
    .dashboard-stats,
    .services-stats {
        grid-template-columns: 1fr;
    }

    .dashboard-charts {
        grid-template-columns: 1fr;
    }

    .stat-card {
        padding: 15px;
    }

    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }

    .form-section {
        padding: 1rem;
    }

    .chart-container {
        min-height: 250px;
    }

    .report-filters {
        flex-direction: column;
    }

    .filter-group {
        width: 100%;
        margin-bottom: 1rem;
    }

    .filter-actions {
        flex-direction: column;
        gap: 1rem;
    }

    #vehicle-details-tabs {
        flex-wrap: wrap;
    }

    .tab-btn {
        flex: 1 1 calc(50% - 5px);
        margin: 2.5px;
    }
}

/* Table Controls */
.table-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 1rem;
    padding: 1rem;
    background-color: var(--card-bg-color);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.control-group {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.control-btn {
    padding: 0.5rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: var(--card-bg-color);
    color: var(--text-color);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all var(--transition-speed);
    font-size: 0.9rem;
    font-weight: 500;
}

.control-btn:hover {
    background-color: var(--hover-color);
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.control-btn.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.control-btn i {
    font-size: 1rem;
}

/* Dropdown styles */
.dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-menu {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    min-width: 200px;
    padding: 0.5rem;
    background-color: var(--card-bg-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.dropdown:hover .dropdown-menu {
    display: block;
}

.column-option {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    cursor: pointer;
}

.column-option:hover {
    background-color: var(--hover-color);
}

.column-option input[type="checkbox"] {
    margin: 0;
}

/* Service type and status filters */
.service-type-filter,
.status-filter {
    display: flex;
    gap: 0.5rem;
}

/* Reset button */
#reset-columns {
    width: 100%;
    margin-top: 0.5rem;
    justify-content: center;
    background-color: var(--danger-color);
    color: white;
    border-color: var(--danger-color);
}

#reset-columns:hover {
    background-color: var(--danger-color);
    opacity: 0.9;
}

.login-box h2 {
    margin-bottom: 32px;
    font-size: 24px;
    font-weight: 600;
    text-align: center;
    color: var(--secondary-color);
    letter-spacing: -0.02em;
}
/* Upcoming Services Filter */
.upcoming-services .services-filter {
    margin-bottom: 10px;
    display: flex;
    gap: 10px;
    justify-content: flex-start; /* Align buttons to the start */
}

.upcoming-services .filter-btn {
    padding: 8px 15px;
    border: none;
    border-radius: 5px;
    background-color: #f0f0f0;
    color: #333;
    cursor: pointer;
    font-size: 0.9em;
    transition: background-color 0.3s ease;
}

.upcoming-services .filter-btn.active,
.upcoming-services .filter-btn:hover {
    background-color: #e0e0e0;
}

/* Style for urgent and warning rows in table */
#upcoming-services-table tr.urgent {
    background-color: #ffe0b2; /* Light orange for urgent */
}

#upcoming-services-table tr.warning {
    background-color: #ffcc80; /* Light orange for warning */
}

/* Service type colors in table rows */
#upcoming-services-table tr.maintenance-service {
    background-color: #e8f5e9; /* Light green for maintenance */
}

#upcoming-services-table tr.tires-service {
    background-color: #fff3e0; /* Light orange for tires */
}

#upcoming-services-table tr.license-service {
    background-color: #e3f2fd; /* Light blue for license */
}

/* Vehicle Modal Styles */
.form-section {
    background-color: var(--card-bg-color);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    margin-bottom: 1.5rem;
    box-shadow: var(--box-shadow-lg);
    border: 1px solid var(--border-color);
    transition: all var(--transition-speed);
}

.form-section h4 {
    color: var(--text-color-dark);
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
    letter-spacing: -0.02em;
}

.form-section h4 i {
    color: var(--primary-color);
    font-size: 1.5rem;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(var(--primary-color-rgb), 0.1);
    border-radius: 8px;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 1.5rem;
    position: relative;
}

.input-container {
    position: relative;
    margin-bottom: 1.25rem;
    transition: all var(--transition-speed);
}

.input-container:hover .input-with-icon i {
    color: var(--primary-color);
    opacity: 1;
}

/* Enhanced form styles with icons in labels */
.form-group label {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
    color: var(--text-color-dark);
    font-weight: 600;
    font-size: 0.95rem;
    letter-spacing: -0.01em;
}

.form-group label i {
    color: var(--primary-color);
    font-size: 1rem;
    width: 1.2rem;
    text-align: center;
}

/* Input field styling with icons */
/* Input and label styling */
.form-group label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    color: var(--text-color-dark);
    font-weight: 500;
    font-size: 0.95rem;
}

.form-group label i {
    color: var(--primary-color);
    font-size: 0.9rem;
    width: 1.2rem;
    text-align: center;
}

.input-with-icon {
    position: relative;
    width: 100%;
}

.input-with-icon i {
    position: absolute;
    right: 14px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-color-light);
    font-size: 1rem;
    transition: all var(--transition-speed);
    width: 20px;
    height: 20px;
    text-align: center;
    opacity: 0.8;
    display: flex;
    align-items: center;
    justify-content: center;
}

.input-with-icon input,
.input-with-icon select,
.input-with-icon textarea {
    width: 100%;
    padding: 0.875rem 2.5rem 0.875rem 1rem;
    font-size: 0.95rem;
    border: 1.5px solid var(--border-color);
    border-radius: var(--border-radius);
    font-family: inherit;
    transition: all var(--transition-speed);
    background-color: white;
    color: var(--text-color-dark);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);
}

/* Read-only input styling */
.input-with-icon input[readonly] {
    background-color: var(--hover-color);
    cursor: not-allowed;
    border-color: var(--border-color);
    color: var(--text-color-light);
}

.input-with-icon input[readonly] + i.fa-lock {
    color: var(--text-color-light);
    opacity: 1;
}

.input-with-icon input:focus,
.input-with-icon select:focus,
.input-with-icon textarea:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.15);
    outline: none;
}

.input-with-icon input:focus + i {
    color: var(--primary-color);
}

.checkbox-group {
    display: flex;
    align-items: center;
    margin: 1rem 0;
}

.checkbox-group label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    margin-bottom: 0;
}

.checkbox-group input[type="checkbox"] {
    margin-right: 0.5rem;
}

.services-section {
    background-color: #f8f9fa;
    border-left: 4px solid var(--primary-color);
}

.form-group input[type="text"]:focus,
.form-group input[type="date"]:focus,
.form-group select:focus,
.form-group textarea:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(0, 87, 255, 0.1);
    outline: none;
}

.input-with-icon textarea {
    min-height: 100px;
    resize: vertical;
    line-height: 1.5;
    padding-right: 2.5rem;
}

.input-with-icon textarea + i {
    top: 1rem;
    transform: none;
}

.input-with-icon input:focus + i,
.input-with-icon select:focus + i,
.input-with-icon textarea:focus + i {
    color: var(--primary-color);
    opacity: 1;
}

/* Modal footer buttons */
.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--border-color);
}

.modal-footer .btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
}

.modal-footer .btn i {
    font-size: 1rem;
}

/* Enhance modal header and close button */
.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.close-btn {
    background: transparent;
    border: none;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-speed);
    color: var(--text-color-light);
}

.close-btn:hover {
    background-color: var(--hover-color);
    color: var(--danger-color);
    transform: scale(1.1);
}

.close-btn i {
    font-size: 1.2rem;
}

.modal-header .modal-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.5rem;
    color: var(--text-color-dark);
}

.modal-header .modal-title i {
    color: var(--primary-color);
}

.input-container input {
    width: 100%;
    padding: 12px 16px 12px 45px; /* تقليل الحشو العلوي والسفلي من 14px */
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 16px;
    transition: all 0.2s ease;
    background-color: #ffffff;
}

.input-container input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(0, 87, 255, 0.1);
    outline: none;
}

.input-container .icon {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #95a5a6;
    font-size: 18px;
    transition: color 0.3s ease;
}

.input-container input:focus + .icon {
    color: var(--secondary-color);
}

.login-box button {
    background-color: var(--primary-color);
    color: white;
    padding: 14px 20px; /* تقليل الحشو من 16px 24px */
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    width: 100%;
    font-size: 16px;
    font-weight: 600;
    font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    transition: all var(--transition-speed);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.07);
    margin-top: 12px; /* تقليل المسافة من 16px */
    letter-spacing: -0.01em;
}

.login-box button:hover {
    background-color: #0046cc;
    box-shadow: 0 4px 12px rgba(0, 87, 255, 0.2);
    transform: translateY(-1px);
}

.login-box button:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(0, 87, 255, 0.1);
}

body.login-page {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-height: 100vh;
    background: linear-gradient(135deg, #f5f7fa 0%, #e4eaf1 100%);
    padding: 0 15px; /* تقليل الحشو من 0 20px */
}

.login-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center; /* إضافة التوسيط الرأسي */
    width: 100%;
    max-width: 1200px;
    min-height: 100vh; /* الاحتفاظ بالارتفاع الكامل */
    padding: 20px; /* تقليل الحشو من 60px 20px */
    box-sizing: border-box; /* ضمان عدم تجاوز الحشو للحدود */
}

.login-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
}

.error-message {
    color: var(--danger-color);
    font-size: 0.9rem;
    margin-top: 1rem;
    text-align: center;
}

/* القائمة الجانبية */
.sidebar {
    width: var(--sidebar-width);
    background-color: var(--background-color);
    color: white;
    position: fixed;
    left: 0;
    height: 100vh;
    transition: transform var(--transition-speed);
    z-index: 1000;
}

.sidebar-header {
    padding: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-logo {
    display: flex;
    justify-content: center;
    margin-bottom: 1rem;
}

.sidebar-logo img {
    max-width: 150px;
    height: auto;
}

.sidebar-header h2 {
    font-size: 1.2rem;
    font-weight: 700;
}
.user-info {
    display: flex;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.user-avatar {
    margin-left: 0.8rem;
    font-size: 2rem;
}

.user-details h3 {
    font-size: 1rem;
    font-weight: 600;
}

.user-details p {
    font-size: 0.8rem;
    opacity: 0.8;
}

.sidebar-nav {
    padding: 0.75rem 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.sidebar-nav ul li a {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 0.8rem 1rem;
    color: var(--text-color-light);
    transition: all var(--transition-speed);
    text-align: left;
    font-weight: 500;
    letter-spacing: -0.01em;
    border-radius: 0.5rem;
}

.sidebar-nav ul li a:hover {
    background-color: var(--hover-color);
    color: var(--text-color-dark);
}

.sidebar-nav ul li a.active {
    background-color: var(--primary-color);
    color: white;
    font-weight: 700;
}

.sidebar-nav ul li a i {
    margin-right: 0.8rem; /* change margin-left to margin-right to move icon to the left */
    font-size: 1.1rem;
}

.sidebar-footer {
    padding: 1rem;
    position: absolute;
    bottom: 0;
    width: 100%;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-footer .btn {
    width: 100%;
    text-align: center;
}

/* المحتوى الرئيسي */
.main-content {
    flex: 1;
    margin-left: var(--sidebar-width);
    transition: margin-left var(--transition-speed);
    margin-right: 0; /* Reset margin-right */
}

.main-header {
    display: flex;
    align-items: center;
    padding: 0 1.5rem;
    height: var(--header-height);
    background-color: var(--card-bg-color);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
    /* margin-left: var(--sidebar-width);  Remove margin-left to align with sidebar */
    transition: margin-left var(--transition-speed); /* Add transition for smooth animation */
}

.toggle-sidebar {
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    color: var(--primary-color);
    margin-left: 1rem;
    display: none;
}

.page-title {
    flex: 1;
    margin-right: auto; /* دفع العنوان إلى اليسار */
    order: -1; /* نقل العنوان إلى اليسار */ /* Ensuring order is still -1 */
}

#current-page-title {
    font-size: 1.5rem;
    color: var(--secondary-color);
    margin-right: auto;
    margin-left: 0;
}

.user-menu {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex-direction: row-reverse;
}

#header-user-name, #user-role {
    font-size: 0.9rem;
    color: var(--text-color);
    font-weight: 500;
}

#user-role {
    opacity: 0.8;
    margin-right: 0.5rem;
}

/* محتوى الصفحات */
.page-content {
    padding: 1rem;
}

.page {
    display: none;
}

.page.active {
    display: block;
}

/* لوحة التحكم الإحصائيات */
.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.stat-card {
    display: flex;
    align-items: center;
    background-color: var(--card-bg-color);
    border-radius: 16px;
    padding: 0.75rem 1rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    margin-bottom: 0.5rem;
}

.stat-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.stat-icon {
    width: 52px;
    height: 52px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.4rem;
    margin-right: 1.25rem;
    color: white;
    flex-shrink: 0;
}

.stat-card .stat-icon.blue {
    background: linear-gradient(135deg, #0061ff 0%, #60a5fa 100%);
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.15);
}

.stat-card .stat-icon.green {
    background: linear-gradient(135deg, #059669 0%, #34d399 100%);
    box-shadow: 0 4px 12px rgba(5, 150, 105, 0.15);
}

.stat-card .stat-icon.orange {
    background: linear-gradient(135deg, #d97706 0%, #fbbf24 100%);
    box-shadow: 0 4px 12px rgba(217, 119, 6, 0.15);
}

.stat-card .stat-icon.red {
    background: linear-gradient(135deg, #dc2626 0%, #f87171 100%);
    box-shadow: 0 4px 12px rgba(220, 38, 38, 0.15);
}

.stat-card .stat-icon.purple {
    background: linear-gradient(135deg, #8b5cf6 0%, #a78bfa 100%);
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.15);
}

.stat-card .stat-icon.teal {
    background: linear-gradient(135deg, #0d9488 0%, #5eead4 100%);
    box-shadow: 0 4px 12px rgba(13, 148, 136, 0.15);
}

.stat-info {
    flex: 1;
    overflow: hidden;
}

.stat-info h3 {
    font-size: 0.875rem;
    color: var(--text-color-light);
    margin-bottom: 0.3rem;
    font-weight: 500;
    letter-spacing: -0.01em;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.stat-info p {
    font-size: 1.8rem;
    font-weight: 600;
    color: var(--text-color-dark);
    letter-spacing: -0.025em;
    line-height: 1.2;
    margin: 0;
}

.stat-percentage {
    display: block;
    font-size: 0.85rem;
    color: var(--text-color-light);
    font-weight: 500;
    margin-top: 0.25rem;
}

.stat-percentage.positive {
    color: var(--success-color);
}

.stat-percentage.negative {
    color: var(--danger-color);
}

.dashboard-stats,
.stats-row,
.services-stats,
.fuel-stats,
.maintenance-stats,
.vehicle-stats,
.driver-stats,
.report-stats,
.stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
    margin-top: 0.5rem;
}

@keyframes stat-card-update {
    0% { transform: scale(1); }
    50% { transform: scale(1.02); box-shadow: 0 8px 15px rgba(var(--primary-color-rgb), 0.2); }
    100% { transform: scale(1); }
}

.stat-card.update-highlight {
    animation: stat-card-update 0.8s ease;
}

body.dark-mode .stat-card {
    background: linear-gradient(145deg, #111b30, #162341);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 5px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid #1d283a;
}

body.dark-mode .stat-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
}

body.dark-mode .stat-info h3 {
    color: #94a3b8;
}

body.dark-mode .stat-info p {
    color: #f8fafc;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

body.dark-mode .stat-percentage {
    color: #94a3b8;
}

body.dark-mode .stat-percentage.positive {
    color: #4ade80;
}

body.dark-mode .stat-percentage.negative {
    color: #f87171;
}

@media (max-width: 1200px) {
    .dashboard-stats,
    .stats-row,
    .services-stats,
    .fuel-stats,
    .maintenance-stats,
    .vehicle-stats,
    .driver-stats,
    .report-stats,
    .stats-container {
        grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    }
}

@media (max-width: 768px) {
    .stat-card {
        padding: 0.75rem;
    }

    .stat-info p {
        font-size: 1.5rem;
    }

    .stat-icon {
        width: 45px;
        height: 45px;
        font-size: 1.2rem;
        margin-right: 1rem;
    }

    .dashboard-stats,
    .stats-row,
    .services-stats,
    .fuel-stats,
    .maintenance-stats,
    .vehicle-stats,
    .driver-stats,
    .report-stats,
    .stats-container {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        gap: 0.75rem;
    }
}

@media (max-width: 480px) {
    .dashboard-stats,
    .stats-row,
    .services-stats,
    .fuel-stats,
    .maintenance-stats,
    .vehicle-stats,
    .driver-stats,
    .report-stats,
    .stats-container {
        grid-template-columns: 1fr;
    }

    .stat-info p {
        font-size: 1.4rem;
    }
}

/* Dashboard Sections */
.dashboard-section {
    margin-bottom: 2rem;
}

.section-title {
    font-size: 1.25rem;
    color: var(--text-color-dark);
    margin-bottom: 1rem;
    padding-left: 0.5rem;
    border-left: 4px solid var(--primary-color);
}

/* Charts Layout */
.dashboard-charts {
    display: grid;
    grid-template-columns: repeat(2, 1fr); /* تقسيم إلى 3 أعمدة متساوية */
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

/* Chart Toggle Buttons */
.chart-toggle-group {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    padding: 0.5rem;
    background-color: var(--card-bg-color);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.chart-toggle-btn {
    padding: 0.5rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: var(--card-bg-color);
    color: var(--text-color);
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all var(--transition-speed);
}

.chart-toggle-btn:hover {
    background-color: var(--hover-color);
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.chart-toggle-btn.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Chart Toggle Buttons */
.chart-toggle-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 1rem;
    padding: 1rem;
    background-color: var(--card-bg-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow);
    position: relative;
    z-index: 1;
}

.chart-toggle-buttons .btn {
    padding: 0.75rem 1.25rem;
    font-size: 0.9rem;
    min-width: 140px;
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
    background-color: var(--card-bg-color);
    color: var(--text-color);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    font-weight: 500;
    letter-spacing: -0.01em;
    border-radius: var(--border-radius);
}

.chart-toggle-buttons .btn:hover {
    background-color: var(--hover-color);
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 87, 255, 0.1);
}

.chart-toggle-buttons .btn.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    box-shadow: 0 4px 12px rgba(0, 87, 255, 0.2);
}

.chart-toggle-buttons .btn i {
    font-size: 1.1rem;
    transition: transform 0.2s ease;
}

.chart-toggle-buttons .btn:hover i {
    transform: scale(1.1);
}

.chart-toggle-buttons .btn:hover {
    background-color: var(--hover-color);
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.chart-toggle-buttons .btn.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.chart-toggle-buttons .btn i {
    font-size: 1rem;
}

.chart-toggle-buttons .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* تخصيص عرض مخططات Maintenance & Compliance */
.maintenance-charts {
    grid-template-columns: 1fr !important; /* عرض كامل للصفحة */
}

.maintenance-charts {
    grid-template-columns: 1fr;
}

.chart-container {
    min-height: 400px;
    margin: 0 auto;
    width: 100%;
    max-width: 100%;
}

.chart-container {
    background-color: var(--card-bg-color);
    border-radius: var(--border-radius);
    padding: 2.5rem;
    box-shadow: var(--box-shadow);
}

.chart-container h3 {
    font-size: 1rem;
    color: var(--text-color-dark);
    margin-bottom: 1rem;
    text-align: center;
    letter-spacing: -0.01em;
    font-weight: 600;
}

/* الجداول */
.table-responsive {
    overflow-x: auto;
    background-color: var(--card-bg-color);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-top: 1rem;
    width: 100%;
    overflow-x: auto;
}

table {
    width: 100%;
    border-collapse: collapse;
}

table th, table td {
    padding: 0.8rem 1rem;
    text-align: left;
}

table th {
    background-color: var(--hover-color);
    font-weight: 600;
    color: var(--text-color-dark);
    border-bottom: 2px solid var(--border-color);
    letter-spacing: -0.01em;
}

table td {
    border-bottom: 1px solid var(--border-color);
    color: var(--text-color-light);
}

table tr:last-child td {
    border-bottom: none;
}

/* أزرار الإجراءات في الجداول */
.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.action-btn {
    background: none;
    border: none;
    font-size: 1rem;
    cursor: pointer;
    padding: 0.3rem;
    border-radius: 4px;
    transition: background-color var(--transition-speed);
}

.edit-btn {
    color: var(--info-color);
}

.view-btn {
    color: var(--success-color);
}

.action-btn:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

/* شريط البحث وإجراءات الصفحة */
.page-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.search-bar {
    display: flex;
    align-items: center;
}

.search-bar input {
    padding: 0.5rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius) 0 0 var(--border-radius);
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    width: 280px;
}

.search-btn {
    background-color: var(--secondary-color);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    cursor: pointer;
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
}

/* النوافذ المنبثقة والنماذج */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    align-items: center;
    justify-content: center;
    overflow-y: auto;
    padding: 20px;
}

.modal.active {
    display: flex;
}

.modal-content {
    background-color: var(--card-bg-color);
    border-radius: var(--border-radius);
    width: 100%;
    max-width: 700px;
    max-height: 85vh;
    box-shadow: var(--box-shadow);
    animation: modal-animation 0.3s ease-out;
    overflow-y: auto;
    margin: auto;
    position: relative;
    padding-bottom: 10px;
}

@keyframes modal-animation {
    from {
        transform: translateY(-30px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h2 {
    font-size: 1.2rem;
    color: var(--primary-color);
}

.close-modal {
    font-size: 1.5rem;
    cursor: pointer;
    color: #7f8c8d;
    transition: color var(--transition-speed);
}

.close-modal:hover {
    color: var(--danger-color);
}

.modal-body {
    padding: 1.5rem;
}

/* النماذج */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.8rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-family: 'Cairo', sans-serif;
    font-size: 0.9rem;
}

.form-group textarea {
    resize: vertical;
}

.form-actions {
    display: flex;
    justify-content: flex-start;
    gap: 1rem;
}

/* تنسيق صفحة التقارير */
.report-filters {
    background-color: var(--card-bg-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: var(--box-shadow);
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    align-items: flex-end;
}

.filter-group {
    flex: 1;
    min-width: 200px;
}

.filter-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-color-dark);
}

.filter-group select,
.filter-group input {
    width: 100%;
    padding: 0.8rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    transition: border-color var(--transition-speed);
}

.filter-group select:focus,
.filter-group input:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 2px rgba(0, 87, 255, 0.1);
}

.filter-actions {
    display: flex;
    gap: 0.75rem;
}

.report-content {
    background-color: var(--card-bg-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--box-shadow);
    min-height: 200px;
    margin-top: 1.5rem;
}

.report-header {
    text-align: center;
    margin-bottom: 20px;
}

.report-header h2 {
    font-size: 1.5rem;
    color: var (--text-color-dark);
    margin-bottom: 0.5rem;
}

.report-summary {
    margin-top: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

.report-summary h3 {
    font-size: 1.1rem;
    color: var(--text-color-dark);
    margin-bottom: 0.5rem;
}

/* Toggle Dark Mode */
.dark-mode-toggle {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background-color: var(--secondary-color);
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    border: none;
    transition: all var(--transition-speed);
}

.dark-mode-toggle:hover {
    transform: scale(1.1);
}

/* Dark Mode Colors - تحديث ألوان الوضع المظلم بأسلوب DeepSeek */
body.dark-mode {
    --primary-color: #4d84ff;
    --secondary-color: #1f2937;
    --background-color: #0f172a;
    --surface-color: #111b30;
    --text-color: #f8fafc;
    --text-color-light: #cbd5e1;
    --text-color-dark: #ffffff;
    --border-color: #1d283a;
    --hover-color: #1e293b;
    --card-bg-color: #111b30;
    background: #0f172a; /* تبسيط خلفية الوضع المظلم لتحسين الأداء */
    color: #f8fafc;
    will-change: background-color, color; /* تحسين أداء التغييرات على المتصفح */
}

/* تحسين مظهر القائمة الجانبية في الوضع المظلم */
body.dark-mode .sidebar {
    background-color: rgba(16, 24, 43, 0.95);
    border-right: 1px solid #1d283a;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

body.dark-mode .sidebar-nav ul li a {
    color: #cbd5e1;
    transition: all 0.3s ease;
    border-radius: 0.5rem;
}

body.dark-mode .sidebar-nav ul li a:hover {
    background-color: rgba(30, 41, 59, 0.7);
    color: #ffffff;
    transform: translateX(5px);
}

body.dark-mode .sidebar-nav ul li a.active {
    background-color: rgba(79, 132, 255, 0.15);
    color: #4d84ff;
    border-left: 3px solid #4d84ff;
    font-weight: 500;
}

body.dark-mode .sidebar-footer {
    border-top: 1px solid #1d283a;
}

/* تحسين مظهر الشريط العلوي */
body.dark-mode .main-header {
    background-color: rgba(15, 23, 42, 0.8);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    border-bottom: 1px solid #1d283a;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

body.dark-mode #current-page-title {
    color: #ffffff;
    text-shadow: 0 0 10px rgba(77, 132, 255, 0.3);
}

body.dark-mode #header-user-name,
body.dark-mode #user-role {
    color: #cbd5e1;
}

/* تحسين بطاقات الإحصائيات */
body.dark-mode .stat-card {
    background: linear-gradient(145deg, #111b30, #162341);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 5px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid #1d283a;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

body.dark-mode .stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 15px rgba(0, 0, 0, 0.2);
}

body.dark-mode .stat-info h3 {
    color: #94a3b8;
}

body.dark-mode .stat-info p {
    color: #f8fafc;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

/* تحسين حاويات الرسومات البيانية */
body.dark-mode .chart-container {
    background: rgba(17, 27, 48, 0.6);
    border: 1px solid #1d283a;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
}

body.dark-mode .chart-container h3 {
    color: #e2e8f0;
    font-weight: 500;
    letter-spacing: 0.02em;
}

/* تحسين الجداول في الوضع المظلم */
body.dark-mode .table-responsive {
    background-color: rgba(17, 27, 48, 0.6);
    border: 1px solid #1d283a;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    border-radius: 8px;
    overflow: hidden;
}

body.dark-mode table th {
    background-color: rgba(30, 41, 59, 0.5);
    color: #e2e8f0;
    font-weight: 500;
    border-bottom: 2px solid #334155;
    padding: 1rem 1.25rem;
    letter-spacing: 0.02em;
}

body.dark-mode table td {
    border-bottom: 1px solid #1e293b;
    color: #cbd5e1;
    padding: 0.875rem 1.25rem;
}

body.dark-mode table tr:hover td {
    background-color: rgba(30, 41, 59, 0.3);
}

/* تحسين الأزرار في الوضع المظلم */
body.dark-mode .btn-primary {
    background: linear-gradient(135deg, #4d84ff 0%, #3a6fd8 100%);
    border: none;
    box-shadow: 0 2px 5px rgba(77, 132, 255, 0.3);
    transition: all 0.3s ease;
}

body.dark-mode .btn-primary:hover {
    background: linear-gradient(135deg, #5a8eff 0%, #4a7fe8 100%);
    box-shadow: 0 4px 12px rgba(77, 132, 255, 0.5);
    transform: translateY(-2px);
}

body.dark-mode .btn-primary:active {
    transform: translateY(0);
    box-shadow: 0 2px 5px rgba(77, 132, 255, 0.3);
}

body.dark-mode .btn-secondary {
    background: rgba(30, 41, 59, 0.8);
    color: #e2e8f0;
    border: 1px solid #334155;
}

body.dark-mode .btn-secondary:hover {
    background: rgba(30, 41, 59, 1);
    color: white;
    border-color: #475569;
}

/* تحسين النوافذ المنبثقة */
body.dark-mode .modal-content {
    background: linear-gradient(145deg, #111b30, #162341);
    border: 1px solid #1d283a;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

body.dark-mode .modal-header {
    border-bottom: 1px solid #1e293b;
}

body.dark-mode .modal-header h3 {
    color: #f1f5f9;
    font-weight: 500;
}

/* تحسين مدخلات النماذج في الوضع المظلم */
body.dark-mode .form-group input,
body.dark-mode .form-group select,
body.dark-mode .form-group textarea,
body.dark-mode .filter-group input,
body.dark-mode .filter-group select {
    background-color: #1e293b;
    border: 1px solid #334155;
    color: #e2e8f0;
    transition: all 0.3s ease;
}

body.dark-mode .form-group input:focus,
body.dark-mode .form-group select:focus,
body.dark-mode .form-group textarea:focus,
body.dark-mode .filter-group input:focus,
body.dark-mode .filter-group select:focus {
    border-color: #4d84ff;
    box-shadow: 0 0 0 3px rgba(77, 132, 255, 0.2);
    outline: none;
}

/* تحسين صفحة تسجيل الدخول */
body.dark-mode.login-page {
    background: linear-gradient(130deg, #0f172a 0%, #0f0f20 100%);
}

body.dark-mode .login-box {
    background: rgba(17, 27, 48, 0.7);
    border: 1px solid #1d283a;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(16px);
}

body.dark-mode .login-box h2 {
    color: #f1f5f9;
    font-weight: 500;
}

body.dark-mode .input-container input {
    background-color: rgba(30, 41, 59, 0.7);
    border: 1px solid #334155;
    color: #e2e8f0;
}

body.dark-mode .input-container input:focus {
    border-color: #4d84ff;
    box-shadow: 0 0 0 3px rgba(77, 132, 255, 0.2);
}

/* تحسين نظام الإشعارات */
body.dark-mode #notification-container .notification {
    background: rgba(15, 23, 42, 0.9);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    border-left: 4px solid #4d84ff;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

body.dark-mode .notification-content {
    color: #e2e8f0;
}

body.dark-mode .notification-close {
    color: #94a3b8;
}

body.dark-mode .notification-close:hover {
    color: #ffffff;
}

/* تحسين تأثير زر التبديل للوضع المظلم */
body.dark-mode .dark-mode-toggle {
    background: rgba(30, 41, 59, 0.8);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
    border: 1px solid #334155;
}

body.dark-mode .dark-mode-toggle:hover {
    background: rgba(30, 41, 59, 1);
    transform: rotate(15deg) scale(1.1);
}

/* تخصيص عناوين الأقسام */
body.dark-mode .section-title {
    color: #e2e8f0;
    border-left: 4px solid #4d84ff;
    text-shadow: 0 0 15px rgba(79, 132, 255, 0.2);
}

body.dark-mode .upcoming-services h3 {
    color: #e2e8f0;
    text-shadow: 0 0 15px rgba(79, 132, 255, 0.2);
}

/* تحسينات للعناصر في الوضع المظلم */
body.dark-mode .sidebar {
    background-color: #1a1a1a;
    border-right: 1px solid #333;
}

body.dark-mode .sidebar-nav ul li a {
    color: #b0b0b0;
}

body.dark-mode .sidebar-nav ul li a:hover {
    background-color: #252525;
    color: white;
}

body.dark-mode .sidebar-nav ul li a.active {
    background-color: var(--primary-color);
    color: white;
}

body.dark-mode .main-header {
    background-color: #1a1a1a;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
    border-bottom: 1px solid #333;
}

body.dark-mode .stat-card {
    background-color: #252525;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

body.dark-mode .stat-info h3 {
    color: #b0b0b0;
}

body.dark-mode .stat-info p {
    color: white;
}

body.dark-mode .chart-container {
    background-color: #252525;
    border: 1px solid #333;
}

body.dark-mode .chart-container h3 {
    color: #e0e0e0;
}

body.dark-mode .upcoming-services {
    background-color: #252525;
    border: 1px solid #333;
}

body.dark-mode .upcoming-services h3 {
    color: #e0e0e0;
}

body.dark-mode .filter-btn {
    background-color: #333;
    color: #e0e0e0;
}

body.dark-mode .filter-btn.active,
body.dark-mode .filter-btn:hover {
    background-color: #444;
}

body.dark-mode .table-responsive {
    background-color: #252525;
    border: 1px solid #333;
}

body.dark-mode table th {
    background-color: #1a1a1a;
    border-bottom: 2px solid #333;
    color: #e0e0e0;
}

body.dark-mode table td {
    border-bottom: 1px solid #333;
    color: #b0b0b0;
}

body.dark-mode .report-filters {
    background-color: #252525;
    border: 1px solid #333;
}

body.dark-mode .report-content {
    background-color: #252525;
    border: 1px solid #333;
}

body.dark-mode .filter-group label {
    color: #e0e0e0;
}

body.dark-mode .filter-group select,
body.dark-mode .filter-group input,
body.dark-mode .form-group input,
body.dark-mode .form-group select,
body.dark-mode .form-group textarea {
    background-color: #333;
    border-color: #444;
    color: #e0e0e0;
}

body.dark-mode .filter-group select:focus,
body.dark-mode .filter-group input:focus {
    border-color: var(--primary-color);
}

body.dark-mode .modal-content {
    background-color: #1e1e1e;
    border: 1px solid #333;
}

body.dark-mode .modal-header {
    border-bottom: 1px solid #333;
}

body.dark-mode .modal-header h3 {
    color: #e0e0e0;
}

body.dark-mode .close-btn {
    color: #b0b0b0;
}

body.dark-mode .close-btn:hover {
    color: white;
}

body.dark-mode .form-group label {
    color: #e0e0e0;
}

/* تعديل تنسيقات إضافية للوضع المظلم */
body.dark-mode .section-title {
    color: #e0e0e0;
}

/* Fix dark mode colors for current-page-title */
body.dark-mode #current-page-title {
    color: #ffffff;
}

/* Login page fixes for dark mode */
body.dark-mode.login-page {
    background: linear-gradient(135deg, #1a1a1a 0%, #000000 100%);
}

body.dark-mode .login-box {
    background-color: #1e1e1e;
    box-shadow: 0 1px 2px rgba(255,255,255,0.05),
                0 2px 4px rgba(255,255,255,0.05),
                0 4px 8px rgba(255,255,255,0.05);
}

body.dark-mode .login-box h2 {
    color: #e0e0e0;
}

body.dark-mode .header h1 {
    color: #ffffff;
}

body.dark-mode .input-container input {
    background-color: #333333;
    border-color: #444444;
    color: #e0e0e0;
}

body.dark-mode .input-container .icon {
    color: #b0b0b0;
}

body.dark-mode .login-box button:hover {
    background-color: #1565C0;
}

/* Improved Responsive Layout */
@media (max-width: 991px) {
    .filter-actions {
        flex-basis: 100%;
        margin-top: 0.5rem;
        justify-content: flex-end;
    }
}

@media (max-width: 768px) {
    .report-filters {
        flex-direction: column;
    }

    .filter-group {
        width: 100%;
    }

    .filter-actions {
        width: 100%;
        justify-content: space-between;
    }

    .filter-actions .btn {
        flex: 1;
    }
}

/* الخدمات القادمة في لوحة التحكم */
.upcoming-services {
    background-color: var(--card-bg-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--box-shadow);
    margin-top: 1.5rem;
}

.upcoming-services h3 {
    font-size: 1.1rem;
    color: var(--secondary-color);
    margin-bottom: 1rem;
}

/* التكيف مع أحجام الشاشة المختلفة */
@media (max-width: 991px) {
    .sidebar {
        transform: translateX(100%);
        z-index: 1001;
    }

    .sidebar.active {
        transform: translateX(0);
    }

    .main-content {
        margin-right: 0;
    }

    .toggle-sidebar {
        display: block;
    }

    .dashboard-charts {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 768px) {
    .dashboard-stats {
        grid-template-columns: 1fr;
    }

    .page-actions {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .search-bar {
        width: 100%;
    }

    .search-bar input {
        width: 100%;
    }

    .login-form {
        width: 90%;
        max-width: 400px;
    }

    .modal-content {
        width: 95%;
        max-height: 80vh;
    }
}

/* تخصيص العناصر للأدوار المختلفة */
.admin-only {
    display: none;
}

.manager-only {
    display: none;
}

body.admin .admin-only {
    display: block;
}

body.admin .sidebar-nav ul li.admin-only {
    display: list-item;
}

body.manager .manager-only {
    display: block;
}

/* حالات المركبات وتنبيهات الرخص */
.status-active {
    color: var(--success-color);
}

.status-maintenance {
    color: var(--warning-color);
}

.status-inactive {
    color: var(--danger-color);
}

.license-expiry-warning {
    color: var(--warning-color);
}

.license-expired {
    color: var(--danger-color);
}

.profile-btn {
    padding: 0.4rem 0.8rem;
    font-size: 0.85rem;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-right: 5px;
}

.profile-btn:hover {
    background-color: var(--primary-color-dark);
    transform: translateY(-1px);
}

.profile-btn i {
    margin-right: 4px;
}

/* Notification System */
#notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    display: flex;
    flex-direction: column;
    gap: 10px;
    max-width: 350px;
}

.notification {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    background-color: white;
    border-left: 4px solid #3498db;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 10px;
    transform: translateX(120%);
    opacity: 0;
    transition: transform 0.3s ease, opacity 0.3s ease;
}

.notification.show {
    transform: translateX(0);
    opacity: 1;
}

.notification.closing {
    transform: translateX(120%);
    opacity: 0;
}

.notification.success {
    border-left-color: var (--success-color);
}

.notification.error {
    border-left-color: var(--danger-color);
}

.notification.warning {
    border-left-color: var(--warning-color);
}

.notification-icon {
    font-size: 1.2rem;
    margin-right: 12px;
    flex-shrink: 0;
}

.notification.info .notification-icon {
    color: var(--info-color);
}

.notification.success .notification-icon {
    color: var(--success-color);
}

.notification.error .notification-icon {
    color: var(--danger-color);
}

.notification.warning .notification-icon {
    color: var(--warning-color);
}

.notification-content {
    flex: 1;
    font-size: 0.9rem;
    color: var(--text-color);
}

.notification-close {
    background: none;
    border: none;
    color: #95a5a6;
    cursor: pointer;
    font-size: 0.9rem;
    padding: 0;
    margin-left: 10px;
    transition: color 0.2s;
}

.notification-close:hover {
    color: var(--text-color-dark);
}

/* Dark mode support for notifications */
body.dark-mode .notification {
    background-color: #2d2d2d;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

body.dark-mode .notification-content {
    color: #e0e0e0;
}

body.dark-mode .notification-close {
    color: #b0b0b0;
}

body.dark-mode .notification-close:hover {
    color: #ffffff;
}

/* Logo switching for dark/light mode */
.light-logo {
    display: block;
}

.dark-logo {
    display: none;
}

/* Dark Mode Logo Switching */
body.dark-mode .light-logo {
    display: none;
}

body.dark-mode .dark-logo {
    display: block;
}

/* تحسينات استجابة الواجهة لصفحة تسجيل الدخول */
@media (max-height: 700px) {
    .header img {
        width: 120px;
        margin-bottom: 15px;
    }

    .header h1 {
        font-size: 20px;
        margin-bottom: 10px;
    }

    .login-box {
        padding: 20px;
    }

    .login-box h2 {
        margin-bottom: 15px;
    }

    .input-container {
        margin-bottom: 12px;
    }

    .input-container input {
        padding: 10px 16px 10px 45px;
    }

    .login-box button {
        padding: 12px 18px;
    }
}

@media (max-height: 600px) {
    .login-container {
        padding: 10px;
    }

    .header img {
        width: 100px;
        margin-bottom: 10px;
    }

    .header h1 {
        font-size: 18px;
        margin-bottom: 8px;
    }
}

/* تحسين أداء الوضع المظلم */
body.dark-mode {
    --primary-color: #4d84ff;
    --secondary-color: #1f2937;
    --background-color: #0f172a;
    --surface-color: #111b30;
    --text-color: #f8fafc;
    --text-color-light: #cbd5e1;
    --text-color-dark: #ffffff;
    --border-color: #1d283a;
    --hover-color: #1e293b;
    --card-bg-color: #111b30;
    background: #0f172a; /* تبسيط خلفية الوضع المظلم لتحسين الأداء */
    color: #f8fafc;
    will-change: background-color, color; /* تحسين أداء التغييرات على المتصفح */
}

/* تحسين أداء التبديل في القائمة الجانبية */
body.dark-mode .sidebar {
    background-color: rgba(16, 24, 43, 0.95);
    border-right: 1px solid #1d283a;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
    transition: background-color 0.3s ease, box-shadow 0.3s ease;
    will-change: background-color;
}

/* تسريع الانتقال للعناصر الرئيسية */
body.dark-mode .main-header {
    background-color: rgba(15, 23, 42, 0.9);
    border-bottom: 1px solid #1d283a;
    transition: background-color 0.3s ease, border-color 0.3s ease;
    will-change: background-color;
    backdrop-filter: none; /* إزالة تأثير الـ blur لتحسين الأداء */
}

/* تحسين أداء بطاقات الإحصائيات */
body.dark-mode .stat-card {
    background: #111b30;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border: 1px solid #1d283a;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

/* تحسين أداء الجداول */
body.dark-mode .table-responsive {
    background-color: rgba(17, 27, 48, 0.95);
    transition: background-color 0.3s ease;
    backdrop-filter: none; /* إزالة تأثير الـ blur لتحسين الأداء */
}

/* تحسين أداء زر التبديل للوضع المظلم */
.dark-mode-toggle {
    transition: transform 0.2s ease;
}

/* تنسيق موحد للعناصر في الوضع المظلم */
body.dark-mode .chart-container,
body.dark-mode .upcoming-services,
body.dark-mode .report-filters,
body.dark-mode .report-content,
body.dark-mode .table-responsive {
    background-color: rgba(17, 27, 48, 0.95);
    border: 1px solid #1d283a;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
    border-radius: 8px;
    transition: background-color 0.3s ease, box-shadow 0.3s ease, transform 0.3s ease;
    will-change: transform, box-shadow;
}

body.dark-mode .chart-container:hover,
body.dark-mode .upcoming-services:hover,
body.dark-mode .report-filters:hover,
body.dark-mode .report-content:hover,
body.dark-mode .table-responsive:hover {
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
    transform: translateY(-2px);
}

/* توحيد ألوان العناوين داخل العناصر */
body.dark-mode .chart-container h3,
body.dark-mode .upcoming-services h3,
body.dark-mode .report-header h2,
body.dark-mode .report-summary h3,
body.dark-mode .section-title {
    color: #e2e8f0;
    text-shadow: 0 0 10px rgba(77, 132, 255, 0.2);
    font-weight: 500;
    letter-spacing: 0.02em;
}

/* توحيد عناصر الإدخال في التقارير */
body.dark-mode .filter-group label,
body.dark-mode .form-group label {
    color: #e2e8f0;
}

body.dark-mode .filter-group select,
body.dark-mode .filter-group input,
body.dark-mode .form-group input,
body.dark-mode .form-group select,
body.dark-mode .form-group textarea {
    background-color: #1e293b;
    border: 1px solid #334155;
    color: #e2e8f0;
    transition: all 0.3s ease;
}

/* توحيد أزرار الفلترة في الخدمات القادمة */
body.dark-mode .filter-btn {
    background-color: #1e293b;
    color: #cbd5e1;
    border: 1px solid #334155;
    transition: all 0.3s ease;
}

body.dark-mode .filter-btn.active,
body.dark-mode .filter-btn:hover {
    background-color: rgba(79, 132, 255, 0.15);
    color: #4d84ff;
    border-color: #4d84ff;
}

/* تصميم محسن لنافذة تفاصيل المركبة */
#vehicle-details-modal .modal-content {
    border-radius: var(--border-radius-lg, 12px);
    max-width: 700px;
    overflow: hidden;
    box-shadow: var(--box-shadow-lg, 0 10px 25px rgba(0, 0, 0, 0.15));
    border: none;
}

#vehicle-details-modal .modal-header {
    background: linear-gradient(135deg, var(--primary-color, #0057ff), var(--primary-color-dark, #0046cc));
    color: #fff;
    padding: 1.5rem;
    position: relative;
    border-bottom: none;
}

/* إضافة شريط تنقل لأقسام المركبة */
#vehicle-details-tabs {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50px;
    display: flex;
    margin: 0.5rem 0 0 0;
    padding: 0.25rem;
    position: relative;
    overflow-x: auto;
    white-space: nowrap;
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
}

#vehicle-details-tabs::-webkit-scrollbar {
    display: none;
}

#vehicle-details-tabs .tab-btn {
    background: transparent;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    cursor: pointer;
    font-size: 0.85rem;
    font-weight: 500;
    padding: 0.5rem 1rem;
    position: relative;
    transition: all 0.3s ease;
    border-radius: 50px;
}

#vehicle-details-tabs .tab-btn.active {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

#vehicle-details-tabs .tab-btn:hover:not(.active) {
    color: white;
}

#vehicle-details-tabs .tab-btn i {
    margin-right: 5px;
}

#vehicle-details-modal .modal-header h3 {
    font-size: 1.4rem;
    font-weight: 600;
    color: #fff;
    margin: 0;
}

#vehicle-details-modal .modal-header .license-number {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-top: 0.25rem;
    display: flex;
    align-items: center;
}

#vehicle-details-modal .modal-header .license-number i {
    margin-right: 0.5rem;
}

#vehicle-details-modal .modal-header .close-btn {
    color: rgba(255, 255, 255, 0.8);
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    position: absolute;
    top: 1rem;
    right: 1rem;
    border: none;
    cursor: pointer;
}

#vehicle-details-modal .modal-header .close-btn:hover {
    color: #fff;
    background: rgba(255, 255, 255, 0.2);
    transform: rotate(90deg);
}

#vehicle-details-modal .modal-body {
    max-height: 60vh;
    overflow-y: auto;
    overflow-x: hidden;
}

#vehicle-details-modal .modal-body::-webkit-scrollbar {
    width: 8px;
}

#vehicle-details-modal .modal-body::-webkit-scrollbar-track {
    background: var(--hover-color, #f9f9f9);
    border-radius: 4px;
}

#vehicle-details-modal .modal-body::-webkit-scrollbar-thumb {
    background-color: var(--border-color, #e8e8e8);
    border-radius: 4px;
}

#vehicle-details-modal .modal-body::-webkit-scrollbar-thumb:hover {
    background: var(--text-color-light, #666666);
}

/* قسم المعلومات */
.vehicle-details-section {
    padding: 0.5rem 0;
    display: none;
}

.vehicle-details-section.active {
    display: block;
    animation: fadeIn 0.3s ease forwards;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

#vehicle-details-content .section-wrapper {
    padding: 1rem 1.5rem;
}

#vehicle-details-content .data-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
}

#vehicle-details-content h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--primary-color, #0057ff);
    margin: 0 0 1rem 0;
    padding: 0.75rem 1.5rem;
    background-color: rgba(var(--primary-color-rgb, 0, 87, 255), 0.05);
    display: flex;
    align-items: center;
    border-left: 3px solid var(--primary-color, #0057ff);
    border-bottom: 1px solid var(--border-color, #e8e8e8);
}

#vehicle-details-content h3 i {
    color: var(--primary-color, #0057ff);
    margin-right: 0.75rem;
    font-size: 1rem;
}

#vehicle-details-content .info-item {
    background: rgba(var(--primary-color-rgb, 0, 87, 255), 0.02);
    border-radius: var(--border-radius, 4px);
    padding: 0.75rem;
    border: 1px solid rgba(var(--primary-color-rgb, 0, 87, 255), 0.1);
}

#vehicle-details-content .info-item.alert-warning {
    background: rgba(var(--warning-color-rgb, 255, 165, 0), 0.1);
    border: 1px solid rgba(var(--warning-color-rgb, 255, 165, 0), 0.2);
}

#vehicle-details-content .info-item.alert-danger {
    background: rgba(var(--danger-color-rgb, 255, 60, 76), 0.1);
    border: 1px solid rgba(var(--danger-color-rgb, 255, 60, 76), 0.2);
}

#vehicle-details-content .info-item label {
    display: block;
    font-size: 0.8rem;
    margin-bottom: 0.25rem;
    color: var(--text-color-light, #666666);
    font-weight: 500;
}

#vehicle-details-content .info-item .value {
    font-size: 1rem;
    color: var(--text-color-dark, #1b1b1b);
    font-weight: 600;
}

#vehicle-details-content .info-item.alert-warning .value {
    color: var(--warning-color, #ffa500);
}

#vehicle-details-content .info-item.alert-danger .value {
    color: var(--danger-color, #ff3c4c);
}

#vehicle-details-content .vehicle-status {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

#vehicle-details-content .status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
}

#vehicle-details-content .status-active {
    background-color: var(--success-color, #00b478);
}

#vehicle-details-content .status-maintenance {
    background-color: var(--warning-color, #ffa500);
}

#vehicle-details-content .status-inactive {
    background-color: var(--danger-color, #ff3c4c);
}

#vehicle-details-content .status-text {
    font-weight: 600;
}

#vehicle-details-content .progress-wrapper {
    margin-top: 0.5rem;
}

#vehicle-details-content .progress {
    height: 5px;
    border-radius: 3px;
    background: var(--border-color, #e8e8e8);
    margin-top: 0.5rem;
    overflow: hidden;
}

#vehicle-details-content .progress-bar {
    height: 100%;
    border-radius: 3px;
}

#vehicle-details-content .progress-good {
    background: var(--success-color, #00b478);
}

#vehicle-details-content .progress-warning {
    background: var(--warning-color, #ffa500);
}

#vehicle-details-content .progress-danger {
    background: var(--danger-color, #ff3c4c);
}

#vehicle-details-actions {
    display: flex;
    justify-content: space-between;
    padding: 1rem 1.5rem;
    background: var(--hover-color, #f9f9f9);
    border-top: 1px solid var(--border-color, #e8e8e8);
}

/* توافق الوضع المظلم لنافذة تفاصيل المركبة */
body.dark-mode #vehicle-details-modal .modal-header {
    background: linear-gradient(135deg, var(--primary-color, #4d84ff), #2b4c8a);
}

body.dark-mode #vehicle-details-content h3 {
    background-color: rgba(77, 132, 255, 0.1);
    color: #e2e8f0;
    border-bottom-color: #1d283a;
}

body.dark-mode #vehicle-details-modal .modal-body::-webkit-scrollbar-track {
    background: rgba(30, 41, 59, 0.2);
}

body.dark-mode #vehicle-details-modal .modal-body::-webkit-scrollbar-thumb {
    background-color: rgba(30, 41, 59, 0.6);
}

body.dark-mode #vehicle-details-modal .modal-body::-webkit-scrollbar-thumb:hover {
    background: rgba(30, 41, 59, 0.8);
}

body.dark-mode #vehicle-details-content .info-item {
    background: rgba(30, 41, 59, 0.2);
    border-color: rgba(77, 132, 255, 0.2);
}

body.dark-mode #vehicle-details-content .info-item.alert-warning {
    background: rgba(255, 165, 0, 0.1);
    border-color: rgba(255, 165, 0, 0.3);
}

body.dark-mode #vehicle-details-content .info-item.alert-danger {
    background: rgba(255, 60, 76, 0.1);
    border-color: rgba(255, 60, 76, 0.3);
}

body.dark-mode #vehicle-details-content .info-item label {
    color: #94a3b8;
}

body.dark-mode #vehicle-details-content .info-item .value {
    color: #e2e8f0;
}

body.dark-mode #vehicle-details-actions {
    background: #1a1a1a;
    border-top-color: #1d283a;
}

body.dark-mode #vehicle-details-content .progress {
    background: #334155;
}

/* تحسين نافذة تفاصيل المركبة باستخدام تصميم Preview */
.vehicle-details-section .preview-details {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
    padding: 1.5rem;
}

@media (max-width: 768px) {
    .vehicle-details-section .preview-details {
        grid-template-columns: 1fr;
    }
}

.vehicle-details-section .preview-item {
    display: flex;
    align-items: center;
    background: var(--card-bg-color);
    border-radius: var(--border-radius);
    padding: 1.25rem;
    border: 1px solid rgba(var(--primary-color-rgb, 0, 87, 255), 0.1);
    margin-bottom: 0.75rem;
    transition: all 0.2s ease;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.03);
}

.vehicle-details-section .preview-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.08);
}

.vehicle-details-section .preview-item i {
    font-size: 1.75rem;
    color: var(--primary-color);
    margin-right: 1.25rem;
    width: 36px;
    text-align: center;
    opacity: 0.9;
}

.vehicle-details-section .preview-label {
    color: var(--text-color-light);
    font-size: 0.85rem;
    font-weight: 600;
    margin-bottom: 0.35rem;
    display: block;
}

.vehicle-details-section .preview-value {
    color: var(--text-color-dark);
    font-weight: 500;
    font-size: 1.1rem;
}

.vehicle-details-section .status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    border-radius: 50px;
    font-size: 1rem;
    font-weight: 600;
    margin: 1rem 1.5rem;
}

.vehicle-details-section .status-badge.active {
    background-color: rgba(0, 180, 120, 0.15);
    color: var(--success-color);
}

.vehicle-details-section .status-badge.maintenance {
    background-color: rgba(255, 165, 0, 0.15);
    color: var(--warning-color);
}

.vehicle-details-section .status-badge.inactive {
    background-color: rgba(255, 60, 76, 0.15);
    color: var(--danger-color);
}

.vehicle-details-section .status-badge i {
    margin-right: 0.75rem;
    font-size: 1.1rem;
}

.vehicle-details-section .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid rgba(var(--primary-color-rgb, 0, 87, 255), 0.08);
}

.vehicle-details-section .section-subtitle {
    color: var(--text-color-light);
    font-size: 0.95rem;
    font-weight: 500;
}

.vehicle-details-section h4 {
    color: var(--text-color-dark);
    font-size: 1.1rem;
    margin: 0;
    font-weight: 600;
}

.progress-wrapper {
    padding: 0 1.5rem 1.5rem !important;
}

/* تحسين نمط شريط التقدم */
.vehicle-details-section .progress {
    height: 8px;
    border-radius: 4px;
    background: var(--border-color);
    margin-top: 0.75rem;
    overflow: hidden;
}

.vehicle-details-section .progress-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    color: var(--text-color-light);
}

/* تخصيصات الوضع المظلم */
body.dark-mode .vehicle-details-section .preview-item {
    background: rgba(30, 41, 59, 0.3);
    border-color: rgba(77, 132, 255, 0.2);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.12);
}

body.dark-mode .vehicle-details-section .preview-item:hover {
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
    background: rgba(30, 41, 59, 0.4);
}

body.dark-mode .vehicle-details-section .preview-label {
    color: #94a3b8;
}

body.dark-mode .vehicle-details-section .preview-value {
    color: #e2e8f0;
}

body.dark-mode .vehicle-details-section .section-header {
    border-bottom-color: rgba(255, 255, 255, 0.05);
}

body.dark-mode .vehicle-details-section h4 {
    color: #e2e8f0;
}

/* أنماط مؤشرات الحالة في جدول الخدمات القادمة */
.status-indicator {
    display: flex;
    align-items: center;
    padding: 6px 10px;
    border-radius: 4px;
    font-size: 0.85rem;
    font-weight: 500;
    gap: 8px;
    white-space: nowrap;
}

.status-indicator i {
    font-size: 1rem;
}

.status-indicator.critical {
    background-color: rgba(239, 68, 68, 0.1);
    color: #ef4444;
    border-left: 3px solid #ef4444;
}

.status-indicator.warning {
    background-color: rgba(245, 158, 11, 0.1);
    color: #f59e0b;
    border-left: 3px solid #f59e0b;
}

.status-indicator.good {
    background-color: rgba(34, 197, 94, 0.1);
    color: #22c55e;
    border-left: 3px solid #22c55e;
}

.status-indicator.neutral {
    background-color: rgba(148, 163, 184, 0.1);
    color: #94a3b8;
    border-left: 3px solid #94a3b8;
}

/* تنسيق صفوف الجدول بناءً على الحالة */
#upcoming-services-table tr.critical-row {
    background-color: rgba(239, 68, 68, 0.05);
}

#upcoming-services-table tr.warning-row {
    background-color: rgba(245, 158, 11, 0.05);
}

#upcoming-services-table tr.good-row {
    background-color: rgba(34, 197, 94, 0.05);
}

/* أنماط الوضع المظلم */
body.dark-mode .status-indicator.critical {
    background-color: rgba(239, 68, 68, 0.15);
    color: #f87171;
}

body.dark-mode .status-indicator.warning {
    background-color: rgba(245, 158, 11, 0.15);
    color: #fbbf24;
}

body.dark-mode .status-indicator.good {
    background-color: rgba(34, 197, 94, 0.15);
    color: #4ade80;
}

body.dark-mode .status-indicator.neutral {
    background-color: rgba(148, 163, 184, 0.15);
    color: #cbd5e1;
}

body.dark-mode #upcoming-services-table tr.critical-row {
    background-color: rgba(239, 68, 68, 0.1);
}

body.dark-mode #upcoming-services-table tr.warning-row {
    background-color: rgba(245, 158, 11, 0.1);
}

body.dark-mode #upcoming-services-table tr.good-row {
    background-color: rgba(34, 197, 94, 0.1);
}

/* تحسين القابلية للقراءة في الجدول */
@media (max-width: 992px) {
    .status-indicator span {
        display: none; /* إخفاء النص في الشاشات الصغيرة */
    }

    .status-indicator {
        justify-content: center;
        padding: 6px;
    }

    .status-indicator i {
        margin: 0;
    }
}

/* أزرار تبديل المخططات البيانية */
.chart-toggle-buttons {
    display: flex;
    justify-content: center;
    margin-bottom: 15px;
    gap: 10px;
}

.chart-toggle-btn {
    padding: 6px 15px;
    border: 1px solid var(--border-color);
    background-color: var(--card-bg-color);
    color: var(--text-color);
    border-radius: 4px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.chart-toggle-btn.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.chart-toggle-btn:hover:not(.active) {
    background-color: var(--hover-color);
}

/* Dark mode styles for chart toggle buttons */
body.dark-mode .chart-toggle-btn {
    border-color: #334155;
    background-color: #1e293b;
    color: #cbd5e1;
}

body.dark-mode .chart-toggle-btn.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

body.dark-mode .chart-toggle-btn:hover:not(.active) {
    background-color: #334155;
    color: #f1f5f9;
}

/* Branch Comparison Chart Toggle Buttons */
.btn-branch-comparison {
    padding: 6px 15px;
    border: 1px solid var(--border-color);
    background-color: var(--card-bg-color);
    color: var(--text-color);
    border-radius: 4px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-left: 5px;
}

.btn-branch-comparison.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.btn-branch-comparison:hover:not(.active) {
    background-color: var(--hover-color);
}

/* Dark mode styles for branch comparison toggle buttons */
body.dark-mode .btn-branch-comparison {
    border-color: #334155;
    background-color: #1e293b;
    color: #cbd5e1;
}

body.dark-mode .btn-branch-comparison.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

body.dark-mode .btn-branch-comparison:hover:not(.active) {
    background-color: #334155;
    color: #f1f5f9;
}

/* Add these styles to your existing styles.css file */

/* Services controls layout */
.services-controls {
    display: flex;
    justify-content: flex-start; /* تغيير من space-between إلى flex-start */
    align-items: center;
    margin-bottom: 15px;
    flex-wrap: wrap;
    gap: 10px;
}

/* تعليق أنماط أزرار التصفية التي تمت إزالتها */
/*.services-filter {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
}

.upcoming-services .filter-btn {
    padding: 8px 15px;
    border: none;
    border-radius: 5px;
    background-color: #f0f0f0;
    color: #333;
    cursor: pointer;
    font-size: 0.9em;
    transition: background-color 0.3s ease;
}

.upcoming-services .filter-btn.active,
.upcoming-services .filter-btn:hover {
    background-color: #e0e0e0;
}*/

.services-actions {
    display: flex;
    flex-direction: row-reverse; /* تعديل إلى row-reverse لعكس ترتيب الأزرار */
    gap: 8px;
    align-items: center;
    justify-content: flex-start;
}

/* Dropdown styling */
.dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-toggle {
    display: flex;
    align-items: center;
    gap: 5px;
    cursor: pointer;
}

.dropdown-content {
    display: none;
    position: absolute;
    right: 0;
    background-color: #fff;
    min-width: 200px;
    box-shadow: 0 8px 16px rgba(0,0,0,0.2);
    z-index: 1;
    padding: 10px;
    border-radius: 4px;
}

.dropdown:hover .dropdown-content {
    display: block;
}

/* Column management styling */
.column-options {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.column-option {
    display: flex;
    align-items: center;
    gap: 8px;
}

.column-option input[type="checkbox"] {
    margin: 0;
}

.column-option label {
    margin: 0;
    cursor: pointer;
}

/* Status filter styling */
#status-filter {
    width: 100%;
    padding: 5px;
}

/* Export button styling */
#export-excel-btn {
    white-space: nowrap;
    display: flex;
    align-items: center;
    gap: 5px;
}

/* Dark mode adjustments */
body.dark-mode .dropdown-content {
    background-color: #1e293b;
    color: #e0e0e0;
    border: 1px solid #374151;
}

body.dark-mode #status-filter {
    background-color: #374151;
    color: #e0e0e0;
    border: 1px solid #4b5563;
}

body.dark-mode .column-option label {
    color: #e0e0e0;
}

/* Status filter actions styling */
.status-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
    gap: 5px;
}

.status-actions .btn {
    font-size: 0.75rem;
    padding: 4px 8px;
    flex: 1;
}

#status-filter-options {
    min-width: 200px;
}

/* Ensure dropdown stays open during interactions */
.dropdown-content.active {
    display: block;
}

/* Adjust dropdown-content for better interaction */
.dropdown-content {
    padding: 12px;
    z-index: 1001;
}

/* Status checkboxes styling */
#status-filter-options .column-option {
    margin-bottom: 8px;
}

#status-filter-options .column-option:last-child {
    margin-bottom: 0;
}

/* Status badge colors for checkboxes */
#status-required + label {
    color: #ef4444;
}

#status-upcoming + label {
    color: #f59e0b;
}

#status-good + label {
    color: #22c55e;
}

/* Dark mode adjustments */
body.dark-mode .status-actions .btn-primary {
    background-color: #4d84ff;
}

body.dark-mode .status-actions .btn-secondary {
    background-color: #374151;
}

/* Add animation for Excel button when columns change */
@keyframes excel-button-highlight {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); background-color: #1a9e5c; }
    100% { transform: scale(1); }
}

#export-excel-btn.columns-modified {
    animation: excel-button-highlight 0.5s ease;
}

/* Make sure dropdown menus for column management stay visible during interaction */
.dropdown:hover .dropdown-content,
.dropdown-content:hover {
    display: block !important;
}

/* Improved dropdown menu behavior for column management */
.dropdown-content {
    display: none;
    position: absolute;
    right: 0;
    background-color: #fff;
    min-width: 200px;
    box-shadow: 0 8px 16px rgba(0,0,0,0.2);
    z-index: 1001; /* Higher z-index to prevent being hidden */
    padding: 10px;
    border-radius: 4px;
    margin-top: 5px;
}

.dropdown {
    position: relative;
    display: inline-block;
}

/* Show dropdown on hover and keep visible during interaction */
.dropdown:hover .dropdown-content,
.dropdown-content:hover,
.dropdown-content:focus-within {
    display: block !important;
}

.dropdown-toggle {
    cursor: pointer;
}

/* Make checkboxes more clickable */
.column-option {
    padding: 6px 0;
    cursor: pointer;
    transition: background-color 0.2s;
}

.column-option:hover {
    background-color: rgba(0, 0, 0, 0.03);
}

.column-option input[type="checkbox"] {
    cursor: pointer;
    margin-right: 8px;
    transform: scale(1.2); /* Make checkbox slightly larger */
}

.column-option label {
    cursor: pointer;
    font-size: 0.95rem;
    display: inline-block;
    width: calc(100% - 30px); /* Make label take up most of the width */
}

/* Reset button styling */
#reset-columns {
    width: 100%;
    margin-top: 8px;
    padding: 6px;
    font-size: 0.85rem;
}

/* Dark mode styles for dropdown */
body.dark-mode .dropdown-content {
    background-color: #1e293b;
    border: 1px solid #374151;
}

body.dark-mode .column-option:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

/* Add margin to dropdown caret */
.ml-1 {
    margin-left: 4px;
}

/* Rotate caret when dropdown is active */
.dropdown-toggle.active .fa-caret-down {
    transform: rotate(180deg);
}

/* Add transition for smooth rotation */
.dropdown-toggle .fa-caret-down {
    transition: transform 0.2s ease;
}

/* Change dropdown behavior to click instead of hover */
.dropdown-content {
    display: none;
    position: absolute;
    right: 0;
    background-color: #fff;
    min-width: 200px;
    box-shadow: 0 8px 16px rgba(0,0,0,0.2);
    z-index: 1001;
    padding: 12px;
    border-radius: 4px;
    margin-top: 5px;
}

/* Remove hover trigger for dropdowns */
.dropdown:hover .dropdown-content {
    display: none;
}

/* Show dropdown when active class is added */
.dropdown-content.show {
    display: block;
}

/* Ensure dropdowns stay visible during interaction when shown */
.dropdown-content.show:hover,
.dropdown-content.show:focus-within {
    display: block;
}

/* Dark mode adjustments for dropdown */
body.dark-mode .dropdown-content {
    background-color: #1e293b;
    border: 1px solid #334155;
}

body.dark-mode .dropdown-content .column-option:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

/* Style for service type filter checkboxes */
#type-maintenance + label {
    color: #22c55e;
}

#type-tires + label {
    color: #f59e0b;
}

#type-license + label {
    color: #3498db;
}

/* Add service type filter button styles */
#service-type-filter-btn {
    display: flex;
    align-items: center;
    gap: 5px;
}

/* Make sure dropdowns are properly spaced */
.services-actions {
    display: flex;
    gap: 8px;
    align-items: center;
}

/* تنسيق رأس قسم الخدمات القادمة */
.upcoming-services-header {
    display: flex;
    justify-content: center; /* تغيير من space-between إلى center */
    align-items: center;
    margin-bottom: 1rem;
}

.upcoming-services-header h3 {
    margin-bottom: 0;
    text-align: center;
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-color-dark);
}

/* إزالة تنسيقات زر التبديل */
#toggle-services-btn {
    display: none; /* إخفاء الزر تماماً */
}

/* إزالة تأثير الانتقال للمحتوى حيث أصبح دائماً ظاهراً */
#upcoming-services-content {
    display: block !important; /* إظهار المحتوى دائماً */
    opacity: 1 !important;
    max-height: none !important;
    overflow: visible !important;
    width: 100%;
    overflow: hidden;
}

/* إزالة أنماط الحالات المختلفة للمحتوى */
#upcoming-services-content.expanded {
    max-height: none;
    opacity: 1;
}

/* تعديل زر Profile في جدول الخدمات القادمة */
#upcoming-services-table .action-buttons {
    display: flex;
    justify-content: flex-start; /* تغيير من flex-end إلى flex-start */
    gap: 5px;
}

/* تحديد محاذاة النص في خلايا جدول الخدمات القادمة للأعمدة التي تحتوي على أزرار */
#upcoming-services-table td:last-child {
    text-align: left; /* تغيير من right إلى left لمحاذاة الأزرار إلى اليسار */
    width: 1%;
    white-space: nowrap;
}

/* تعديل ترتيب الأزرار في مربع حوار dropdown - عودة من row-reverse إلى row */
.services-actions {
    display: flex;
    flex-direction: row; /* إعادة الاتجاه من row-reverse إلى row */
    gap: 8px;
    align-items: center;
}

/* تعديل موضع القوائم المنسدلة لضمان ظهورها بالكامل */
.dropdown-content {
    display: none;
    position: absolute;
    right: 0;
    background-color: #fff;
    min-width: 200px;
    box-shadow: 0 8px 16px rgba(0,0,0,0.2);
    z-index: 1001;
    padding: 12px;
    border-radius: 4px;
    margin-top: 5px;
    /* إضافة الخصائص التالية لضمان ظهور القائمة بالكامل */
    max-height: 80vh; /* تحديد الحد الأقصى للارتفاع بنسبة من ارتفاع الشاشة */
    overflow-y: auto; /* إضافة شريط تمرير عند الحاجة */
}

/* تحسين مظهر شريط التمرير في القوائم المنسدلة */
.dropdown-content::-webkit-scrollbar {
    width: 6px;
}

.dropdown-content::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.dropdown-content::-webkit-scrollbar-thumb {
    background: #b3b3b3;
    border-radius: 3px;
}

.dropdown-content::-webkit-scrollbar-thumb:hover {
    background: #888;
}

/* تعديل نمط القائمة المنسدلة في زر Status Filter بشكل خاص */
#status-filter-options {
    min-width: 220px; /* زيادة العرض قليلاً */
    margin-bottom: 5px; /* إضافة هامش في الأسفل */
}

/* تأكيد أن خيارات التصفية تظهر بشكل صحيح */
#status-filter-options .column-option {
    margin-bottom: 10px;
    padding: 5px 0;
}

/* توافق مع الوضع المظلم */
body.dark-mode .dropdown-content::-webkit-scrollbar-track {
    background: #2d3748;
}

body.dark-mode .dropdown-content::-webkit-scrollbar-thumb {
    background: #4a5568;
}

body.dark-mode .dropdown-content::-webkit-scrollbar-thumb:hover {
    background: #718096;
}

/* تنسيق زر استعادة الإعدادات الافتراضية */
#reset-services-default-btn {
    display: flex;
    align-items: center;
    gap: 5px;
    transition: all 0.3s ease;
}

#reset-services-default-btn:hover {
    background-color: #0046cc;
    transform: scale(1.02);
}

#reset-services-default-btn.resetting {
    animation: spin-button 0.7s ease;
}

@keyframes spin-button {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* تنسيق بطاقات إحصائيات الخدمات */
.services-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1.25rem;
}

.services-stat {
    padding: 0.6rem;
    border-radius: 10px;
    transition: all 0.3s ease;
    position: relative;
}

.services-stat .stat-icon {
    width: 45px;
    height: 45px;
    font-size: 1.2rem;
}

.services-stat .stat-icon.purple {
    background: linear-gradient(135deg, #8b5cf6 0%, #a78bfa 100%);
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.15);
}

.services-stat .stat-info h3 {
    font-size: 0.8rem;
    margin-bottom: 0.35rem;
}

.services-stat .stat-info p {
    font-size: 1.6rem;
    margin-bottom: 0.1rem;
}

.services-stat:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.1);
}

/* تنسيق النسب المئوية */
.stat-percentage {
    display: block;
    font-size: 0.75rem;
    color: #6b7280;
    font-weight: 500;
    margin-top: -0.25rem;
}

/* توافق الوضع المظلم للبطاقات الإحصائية */
body.dark-mode .services-stat {
    background: linear-gradient(145deg, #111b30, #162341);
    border: 1px solid #1d283a;
}

body.dark-mode .stat-percentage {
    color: #94a3b8;
}

@media (max-width: 768px) {
    .services-stats {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .services-stats {
        grid-template-columns: 1fr;
    }
}

/* إضافة تأثير تحديث البطاقات */
@keyframes update-pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.03); box-shadow: 0 8px 15px rgba(0, 87, 255, 0.2); }
    100% { transform: scale(1); }
}

.services-stat.update-highlight {
    animation: update-pulse 1s ease;
}

/* تحسين عرض الجدول في قسم الخدمات القادمة */
#upcoming-services-table {
    width: 100%;
    border-collapse: collapse;
    table-layout: auto;
    margin-bottom: 20px;
}

#upcoming-services-table th,
#upcoming-services-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

#upcoming-services-table th {
    background-color: var(--hover-color);
    font-weight: 600;
    color: var(--text-color-dark);
    position: sticky;
    top: 0;
    z-index: 10;
}

#upcoming-services-table tbody tr:hover {
    background-color: var(--hover-color);
}

/* ضمان عرض الجدول بشكل كامل */
.upcoming-services .table-responsive {
    overflow-x: auto;
    max-height: 600px; /* ارتفاع أقصى مع إمكانية التمرير */
    overflow-y: auto;
}

/* تحسين تنسيق خانات الجدول لمنع التفاف النص */
#upcoming-services-table td {
    white-space: nowrap;
}

/* تنسيق عمود الإجراءات */
#upcoming-services-table td:last-child {
    width: 1%; /* أصغر عرض ممكن */
    white-space: nowrap;
}

/* ضمان أن يكون العرض الكامل للجدول هو 100% من المساحة المتاحة */
#upcoming-services-content {
    width: 100%;
    overflow: hidden;
}

/* توسيع جدول الخدمات القادمة لملء المساحة المتاحة */
.table-responsive {
    background-color: var(--card-bg-color);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-top: 1rem;
    width: 100%;
    overflow-x: auto;
}

/* تعديل عرض الخلايا في الجدول */
#upcoming-services-table th:nth-child(1),
#upcoming-services-table td:nth-child(1) {
    min-width: 80px; /* عرض خلية المركبة */
}

#upcoming-services-table th:nth-child(2),
#upcoming-services-table td:nth-child(2) {
    min-width: 120px; /* عرض خلية نوع الخدمة */
}

#upcoming-services-table th:nth-child(3),
#upcoming-services-table td:nth-child(3) {
    min-width: 110px; /* عرض خلية التاريخ المتوقع */
}

#upcoming-services-table th:nth-child(4),
#upcoming-services-table td:nth-child(4) {
    min-width: 150px; /* عرض خلية الحالة */
}

#upcoming-services-table th:nth-child(5),
#upcoming-services-table td:nth-child(5) {
    min-width: 130px; /* عرض خلية المسافة المتبقية */
}

/* تعزيز وضوح شريط التمرير للجدول */
.table-responsive::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.table-responsive::-webkit-scrollbar-track {
    background: var(--hover-color);
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb {
    background-color: var(--border-color);
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
    background: var(--text-color-light);
}

/* توافق الوضع المظلم */
body.dark-mode .table-responsive::-webkit-scrollbar-track {
    background: rgba(30, 41, 59, 0.2);
}

body.dark-mode .table-responsive::-webkit-scrollbar-thumb {
    background-color: rgba(30, 41, 59, 0.6);
}

body.dark-mode .table-responsive::-webkit-scrollbar-thumb:hover {
    background: rgba(30, 41, 59, 0.8);
}

/* تنسيق للقيم المتأخرة في عمود المسافة المتبقية */
.overdue-value {
    color: #ef4444;
    font-weight: 600;
    position: relative;
}

/* تخصيص لعرض الصف للخدمات المتأخرة */
#upcoming-services-table tr.critical-row {
    background-color: rgba(239, 68, 68, 0.05);
}

#upcoming-services-table tr.critical-row:hover {
    background-color: rgba(239, 68, 68, 0.1);
}

/* تنسيق إضافي لمؤشر الحالة للخدمات المتأخرة */
.status-indicator.critical {
    background-color: rgba(239, 68, 68, 0.1);
    color: #ef4444;
    border-left: 3px solid #ef4444;
    font-weight: 600;
}

/* توافق الوضع المظلم */
body.dark-mode .status-indicator.critical {
    background-color: rgba(239, 68, 68, 0.15);
    color: #f87171;
}

body.dark-mode .overdue-value {
    color: #f87171;
}

body.dark-mode #upcoming-services-table tr.critical-row {
    background-color: rgba(239, 68, 68, 0.1);
}

body.dark-mode #upcoming-services-table tr.critical-row:hover {
    background-color: rgba(239, 68, 68, 0.15);
}

/* تعديل نمط البطاقات الإحصائية في صفحة الصيانة - جعلها أكبر وأكثر وضوحاً */
#maintenance-page .services-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 1.25rem;
    margin-bottom: 2rem;
}

#maintenance-page .services-stat {
    padding: 1rem;
    border-radius: 12px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.08);
}

#maintenance-page .services-stat .stat-icon {
    width: 50px;
    height: 50px;
    font-size: 1.3rem;
}

#maintenance-page .services-stat .stat-info h3 {
    font-size: 0.9rem;
    margin-bottom: 0.4rem;
}

#maintenance-page .services-stat .stat-info p {
    font-size: 1.8rem;
    margin-bottom: 0.2rem;
}

#maintenance-page .services-stat:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
}

/* تحسين تأثير التحديث للبطاقات الإحصائية في صفحة الصيانة */
@keyframes maintenance-stats-update {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); box-shadow: 0 10px 20px rgba(0, 87, 255, 0.2); }
    100% { transform: scale(1); }
}

#maintenance-page .services-stat.update-highlight {
    animation: maintenance-stats-update 1.2s ease;
}

/* إضافة تنسيق متقدم للبطاقات الإحصائية في صفحة الصيانة */
#maintenance-page .stat-percentage {
    font-size: 0.85rem;
    font-weight: 500;
    background-color: rgba(0, 0, 0, 0.05);
    padding: 0.2rem 0.5rem;
    border-radius: 10px;
    display: inline-block;
    margin-top: 0.3rem;
}

/* توافق الوضع المظلم للبطاقات الإحصائية في صفحة الصيانة */
body.dark-mode #maintenance-page .services-stat {
    background: linear-gradient(145deg, #111b30, #162341);
    border: 1px solid #1d283a;
}

body.dark-mode #maintenance-page .stat-percentage {
    background-color: rgba(255, 255, 255, 0.1);
    color: #94a3b8;
}

/* تصغير العناوين في جدول الخدمات القادمة */
.upcoming-services-header h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-color-dark);
}

/* تنسيق زر Profile في جداول الصيانة */
.profile-btn {
    padding: 0.4rem 0.8rem;
    font-size: 0.85rem;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-right: 5px;
}

.profile-btn:hover {
    background-color: var(--primary-color-dark);
    transform: translateY(-1px);
}

.profile-btn i {
    margin-right: 4px;
}

/* Ensure modal is positioned correctly within the maintenance page */
#maintenance-page #vehicle-details-modal {
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
}

#maintenance-page #vehicle-details-modal .modal-content {
    background-color: var(--card-bg-color);
    border-radius: var(--border-radius);
    width: 100%;
    max-width: 600px;
    box-shadow: var(--box-shadow);
    animation: modal-animation 0.3s ease-out;
    overflow: hidden;
}

#maintenance-page #vehicle-details-modal .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background-color: var(--primary-color);
    color: white;
}

#maintenance-page #vehicle-details-modal .modal-body {
    padding: 1rem;
    max-height: 70vh;
    overflow-y: auto;
}

/* Modified: Fleet Overview Charts - 3 column layout */
.fleet-overview-charts {
    display: grid;
    grid-template-columns: repeat(3, 1fr); /* 3 equal columns */
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

/* Ensure each chart container in fleet overview has proper sizing */
.fleet-overview-charts .chart-container {
    background-color: var(--card-bg-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--box-shadow);
    height: 100%;
    display: flex;
    flex-direction: column;
}

.fleet-overview-charts .chart-container h4 {
    font-size: 1rem;
    color: var(--text-color-dark);
    margin-bottom: 1rem;
    text-align: center;
    letter-spacing: -0.01em;
    font-weight: 600;
}

/* Ensure responsiveness for smaller screens */
@media (max-width: 991px) {
    .fleet-overview-charts {
        grid-template-columns: repeat(2, 1fr); /* 2 columns on medium screens */
    }
}

@media (max-width: 767px) {
    .fleet-overview-charts {
        grid-template-columns: 1fr; /* Single column on small screens */
    }
}

/* تخصيص عرض المخططات في صفحة الوقود لتكون بعرض كامل مثل مخطط Cost and Reading Difference */
#fuel-page .chart-container.full-width {
    width: 100%;
    margin-bottom: 1.5rem;
}

/* تخصيص حجم مخطط استهلاك الوقود ليكون بعرض كامل */
#fuel-page #fuel-consumption-chart,
#fuel-page #driver-efficiency-chart,
#fuel-page #vehicle-efficiency-chart {
    max-height: 350px;
    width: 100%;
}

/* تنسيق العناوين الفرعية للمخططات في صفحة الوقود */
#fuel-page .chart-container .chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

/* تنسيق أزرار العرض لمخططات صفحة الوقود */
#fuel-page .chart-container .chart-actions {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

/* تنسيق عناوين المخططات في صفحة الوقود */
#fuel-page .chart-container h4 {
    margin-bottom: 0;
    font-size: 1rem;
    color: var(--text-color-dark);
    font-weight: 600;
}

/* تنسيق موحد لحاويات مخططات صفحة الوقود */
#fuel-page .chart-container.full-width {
    background-color: var(--card-bg-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--box-shadow);
    margin-bottom: 1.5rem;
    grid-column: 1 / -1;
}

/* توحيد مظهر حاويات المخططات في الوضع المظلم */
body.dark-mode #fuel-page .chart-container.full-width {
    background: rgba(17, 27, 48, 0.6);
    border: 1px solid #1d283a;
}

/* تحسين تجربة المستخدم عند تحديد نوع المخطط */
#fuel-page .btn-chart-view {
    padding: 0.5rem 1rem;
    background-color: var(--hover-color);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 0.9rem;
    color: var(--text-color);
    cursor: pointer;
    transition: all 0.3s ease;
}

#fuel-page .btn-chart-view.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

#fuel-page .btn-chart-view:hover:not(.active) {
    background-color: var(--border-color);
}

/* تنسيق أزرار العرض في الوضع المظلم */
body.dark-mode #fuel-page .btn-chart-view {
    background-color: #1e293b;
    border-color: #334155;
    color: #cbd5e1;
}

body.dark-mode #fuel-page .btn-chart-view.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

body.dark-mode #fuel-page .btn-chart-view:hover:not(.active) {
    background-color: #334155;
    color: #f1f5f9;
}

/* ضمان توافق تنسيقات الرسوم البيانية مع كافة أحجام الشاشات */
@media (max-width: 768px) {
    #fuel-page .chart-container .chart-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    #fuel-page .chart-container .chart-actions {
        width: 100%;
        justify-content: flex-start;
    }
}

/* =============================================
   EFFECTS CSS - تأثيرات إضافية لتحسين واجهة المستخدم
   (Moved from effects.css)
   ============================================= */

/* تبسيط تأثير الخلفية المتموجة في الوضع المظلم */
body.dark-mode::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: none; /* إلغاء التأثير المتموج لتحسين الأداء */
    z-index: -1;
    pointer-events: none;
}

/* تبسيط تأثير ضبابي للقائمة الجانبية والهيدر */
.sidebar, .main-header {
    transition: all 0.2s ease;
}

body.dark-mode .sidebar,
body.dark-mode .main-header {
    backdrop-filter: none; /* إزالة تأثير الـ blur لتحسين الأداء */
    -webkit-backdrop-filter: none;
}

/* تبسيط تأثير الظلال الزجاجية للبطاقات */
body.dark-mode .stat-card,
body.dark-mode .chart-container,
body.dark-mode .table-responsive,
body.dark-mode .upcoming-services,
body.dark-mode .modal-content,
body.dark-mode .report-filters,
body.dark-mode .report-content {
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2); /* تبسيط الظلال */
}

/* إزالة تأثير الانعكاس على البطاقات لتحسين الأداء */
body.dark-mode .stat-card::after,
body.dark-mode .chart-container::after,
body.dark-mode .table-responsive::after,
body.dark-mode .upcoming-services::after,
body.dark-mode .modal-content::after,
body.dark-mode .report-filters::after,
body.dark-mode .report-content::after {
    display: none; /* إلغاء التأثير المتحرك لتحسين الأداء */
}

/* تبسيط تأثيرات الأزرار */
.btn {
    position: relative;
    overflow: hidden;
    transition: all 0.2s ease;
}

.btn::before {
    display: none; /* إلغاء تأثير الانتشار الدائري */
}

/* تبسيط تأثير التبديل للوضع المظلم */
.dark-mode-toggle {
    transition: all 0.2s ease;
}

body.dark-mode .dark-mode-toggle {
    transform: none;
}

/* تأثير النص المضيء في الوضع المظلم */
body.dark-mode .stat-info p,
body.dark-mode #current-page-title,
body.dark-mode .section-title {
    text-shadow: 0 0 15px rgba(79, 132, 255, 0.3);
}

/* تأثير الحركة للإشعارات */
.notification {
    transition: transform 0.4s cubic-bezier(0.34, 1.56, 0.64, 1),
                opacity 0.3s ease;
}

/* تحسين تأثير تمرير الجدول */
body.dark-mode .table-responsive::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

body.dark-mode .table-responsive::-webkit-scrollbar-track {
    background: rgba(30, 41, 59, 0.2);
    border-radius: 4px;
}

body.dark-mode .table-responsive::-webkit-scrollbar-thumb {
    background: rgba(79, 132, 255, 0.3);
    border-radius: 4px;
}

body.dark-mode .table-responsive::-webkit-scrollbar-thumb:hover {
    background: rgba(79, 132, 255, 0.5);
}

/* تأثير تحويم القائمة الجانبية */
body.dark-mode .sidebar-nav ul li a {
    position: relative;
    transition: transform 0.3s ease, background-color 0.3s ease, color 0.3s ease;
}

body.dark-mode .sidebar-nav ul li a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -5px;
    width: 5px;
    height: 100%;
    background: linear-gradient(to bottom, #4d84ff, #3a6fd8);
    border-radius: 0 4px 4px 0;
    opacity: 0;
    transition: all 0.3s ease;
}

body.dark-mode .sidebar-nav ul li a:hover::before {
    opacity: 1;
    left: 0;
}

body.dark-mode .sidebar-nav ul li a.active::before {
    opacity: 1;
    left: 0;
}

/* Fuel Dashboard Styles */

/* Dashboard layout */
.fuel-dashboard-container {
    margin-bottom: 2rem;
}

.dashboard-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 1.5rem;
    background-color: var(--card-bg-color);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

/* Custom date range inputs */
.date-range-inputs {
    display: flex;
    gap: 0.5rem;
}

.date-range-inputs > div {
    flex: 1;
}

/* Make all charts full width (for consistent layout) */
.chart-container {
    background-color: var(--card-bg-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--box-shadow);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    display: flex;
    flex-direction: column;
    height: 400px;
    max-height: 400px;
    overflow: hidden;
}

.chart-container:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

/* Keep the full-width class for backwards compatibility */
.chart-container.full-width {
    height: 450px;
    max-height: 450px;
}

/* ضبط حجم حاويات الرسومات */
.chart-container canvas {
    width: 100% !important;
    height: calc(100% - 50px) !important; /* الارتفاع مع مراعاة مساحة للعنوان */
    max-height: 350px !important;
    object-fit: contain;
}

/* Enhanced Chart container styles for the driver comparison chart */
.chart-container.full-width canvas {
    max-height: 400px !important;
}

/* Special styling for the driver efficiency chart */
#driver-efficiency-chart {
    position: relative;
    transition: all 0.3s ease;
    font-feature-settings: "tnum"; /* Use tabular numbers for better alignment */
}

/* Customize chart container height when many drivers are present */
.chart-container:has(#driver-efficiency-chart) {
    height: auto !important;
    min-height: 450px !important;
    max-height: 600px !important;
}

/* Hover effect for the driver efficiency chart container */
.chart-container:has(#driver-efficiency-chart):hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 20px rgba(0, 0, 0, 0.15);
}

body.dark-mode .chart-container:has(#driver-efficiency-chart):hover {
    box-shadow: 0 12px 20px rgba(0, 0, 0, 0.3);
}

/* Customize chart container height when many vehicles are present */
.chart-container:has(#vehicle-efficiency-chart) {
    height: auto !important;
    min-height: 450px !important;
    max-height: 600px !important;
}

/* Hover effect for the vehicle efficiency chart container */
.chart-container:has(#vehicle-efficiency-chart):hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 20px rgba(0, 0, 0, 0.15);
}

body.dark-mode .chart-container:has(#vehicle-efficiency-chart):hover {
    box-shadow: 0 12px 20px rgba(0, 0, 0, 0.3);
}

/* Add animation for the driver efficiency bars when they first appear */
@keyframes growBar {
    from { transform: scaleX(0); }
    to { transform: scaleX(1); }
}

/* Enhanced tooltip styles for the driver comparison chart */
.chart-tooltip-enhanced {
    background-color: rgba(0, 0, 0, 0.8) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 6px !important;
    padding: 10px 14px !important;
    font-family: Cairo, Arial, sans-serif !important;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3) !important;
}

/* Dark mode tooltip styles */
body.dark-mode .chart-tooltip-enhanced {
    background-color: rgba(30, 41, 59, 0.95) !important;
    border: 1px solid rgba(79, 132, 255, 0.3) !important;
}

/* Add Cairo font for Arabic text rendering */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap');

/* Ensure all chart text is properly displayed with Cairo font */
.chart-container text,
.chart-container .chartjs-tooltip {
    font-family: 'Cairo', Arial, sans-serif !important;
}

/* Loading indicator for charts */
.chart-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 300px;
    background-color: var(--card-bg-color);
    border-radius: var(--border-radius);
}

.chart-loading .spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(var(--primary-color-rgb), 0.3);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* No data message for charts */
.no-data-message {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 300px;
    background-color: var(--card-bg-color);
    border-radius: var(--border-radius);
}

.no-data-message i {
    font-size: 3rem;
    color: var(--border-color);
    margin-bottom: 1rem;
}

.no-data-message p {
    color: var(--text-color-light);
    font-size: 1.1rem;
}

/* Add tooltip enhancements for full name display */
.chartjs-tooltip {
    max-width: 250px !important;
    white-space: normal !important;
    word-wrap: break-word !important;
}

/* Add a special class for the driver name tooltips */
.driver-name-tooltip {
    font-weight: bold;
    border-bottom: 1px solid rgba(255,255,255,0.2);
    padding-bottom: 5px;
    margin-bottom: 5px;
    max-width: 100%;
    display: block;
    direction: rtl;
    text-align: right;
}

/* تنسيق مخططات صفحة الوقود بحيث تكون بعرض كامل ومتناسقة */
#fuel-page .chart-container {
    width: 100%;
    margin-bottom: 1.5rem;
    background-color: var(--card-bg-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--box-shadow);
    overflow: hidden;
    height: auto;
    min-height: 520px;
}

/* تنسيق المخططات لتكون بعرض كامل للصفحة */
#fuel-page .chart-container.full-width {
    width: 100%;
    margin-bottom: 2rem;
    min-height: 470px;
}

/* تنسيق خاص للمخططات المحددة لتكون بنفس حجم وعرض الصفحة الكامل */
#fuel-page #fuel-consumption-chart,
#fuel-page #driver-efficiency-chart,
#fuel-page #vehicle-efficiency-chart,
#fuel-page #cost-reading-chart {
    width: 100% !important;
    height: 380px !important;
    max-height: 100%;
}

/* تنسيق العنوان وأدوات التحكم في المخططات */
#fuel-page .chart-container .chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    width: 100%;
}

#fuel-page .chart-container h4 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-color-dark);
}

#fuel-page .chart-container .chart-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    align-items: center;
}

/* أزرار تبديل عرض المخططات في صفحة الوقود */
#fuel-page .btn-chart-view,
#fuel-page .btn-branch-comparison {
    padding: 0.5rem 1rem;
    background-color: var(--hover-color);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 0.9rem;
    color: var(--text-color);
    cursor: pointer;
    transition: all 0.3s ease;
}

#fuel-page .btn-chart-view.active,
#fuel-page .btn-branch-comparison.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var (--primary-color);
}

#fuel-page .btn-chart-view:hover:not(.active),
#fuel-page .btn-branch-comparison:hover:not(.active) {
    background-color: var(--border-color);
}

/* توافق الوضع المظلم مع مخططات صفحة الوقود */
body.dark-mode #fuel-page .chart-container {
    background: rgba(17, 27, 48, 0.6);
    border: 1px solid #1d283a;
}

body.dark-mode #fuel-page .chart-container h4 {
    color: #e2e8f0;
}

body.dark-mode #fuel-page .btn-chart-view,
body.dark-mode #fuel-page .btn-branch-comparison {
    background-color: #1e293b;
    border-color: #334155;
    color: #cbd5e1;
}

body.dark-mode #fuel-page .btn-chart-view.active,
body.dark-mode #fuel-page .btn-branch-comparison.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

body.dark-mode #fuel-page .btn-chart-view:hover:not(.active),
body.dark-mode #fuel-page .btn-branch-comparison:hover:not(.active) {
    background-color: #334155;
}

/* تحسين الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    #fuel-page .chart-container .chart-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
    }

    #fuel-page .chart-container .chart-actions {
        width: 100%;
        justify-content: flex-start;
    }

    #fuel-page .chart-container.full-width {
        min-height: 400px;
    }

    #fuel-page #fuel-consumption-chart,
    #fuel-page #driver-efficiency-chart,
    #fuel-page #vehicle-efficiency-chart,
    #fuel-page #cost-reading-chart {
        height: 300px !important;
    }
}

/**
 * Effects CSS - تأثيرات إضافية لتحسين واجهة المستخدم
 */

/* تبسيط تأثير الخلفية المتموجة في الوضع المظلم */
body.dark-mode::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: none; /* إلغاء التأثير المتموج لتحسين الأداء */
    z-index: -1;
    pointer-events: none;
}

/* تبسيط تأثير ضبابي للقائمة الجانبية والهيدر */
.sidebar, .main-header {
    transition: all 0.2s ease;
}

body.dark-mode .sidebar,
body.dark-mode .main-header {
    backdrop-filter: none; /* إزالة تأثير الـ blur لتحسين الأداء */
    -webkit-backdrop-filter: none;
}

/* تبسيط تأثير الظلال الزجاجية للبطاقات */
body.dark-mode .stat-card,
body.dark-mode .chart-container,
body.dark-mode .table-responsive,
body.dark-mode .upcoming-services,
body.dark-mode .modal-content,
body.dark-mode .report-filters,
body.dark-mode .report-content {
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2); /* تبسيط الظلال */
}

/* إزالة تأثير الانعكاس على البطاقات لتحسين الأداء */
body.dark-mode .stat-card::after,
body.dark-mode .chart-container::after,
body.dark-mode .table-responsive::after,
body.dark-mode .upcoming-services::after,
body.dark-mode .modal-content::after,
body.dark-mode .report-filters::after,
body.dark-mode .report-content::after {
    display: none; /* إلغاء التأثير المتحرك لتحسين الأداء */
}

/* تبسيط تأثيرات الأزرار */
.btn {
    position: relative;
    overflow: hidden;
    transition: all 0.2s ease;
}

.btn::before {
    display: none; /* إلغاء تأثير الانتشار الدائري */
}

/* تبسيط تأثير التبديل للوضع المظلم */
.dark-mode-toggle {
    transition: all 0.2s ease;
}

body.dark-mode .dark-mode-toggle {
    transform: none;
}

/* تأثير النص المضيء في الوضع المظلم */
body.dark-mode .stat-info p,
body.dark-mode #current-page-title,
body.dark-mode .section-title {
    text-shadow: 0 0 15px rgba(79, 132, 255, 0.3);
}

/* تأثير الحركة للإشعارات */
.notification {
    transition: transform 0.4s cubic-bezier(0.34, 1.56, 0.64, 1),
                opacity 0.3s ease;
}

/* تحسين تأثير تمرير الجدول */
body.dark-mode .table-responsive::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

body.dark-mode .table-responsive::-webkit-scrollbar-track {
    background: rgba(30, 41, 59, 0.2);
    border-radius: 4px;
}

body.dark-mode .table-responsive::-webkit-scrollbar-thumb {
    background: rgba(79, 132, 255, 0.3);
    border-radius: 4px;
}

body.dark-mode .table-responsive::-webkit-scrollbar-thumb:hover {
    background: rgba(79, 132, 255, 0.5);
}

/* تأثير تحويم القائمة الجانبية */
body.dark-mode .sidebar-nav ul li a {
    position: relative;
    transition: transform 0.3s ease, background-color 0.3s ease, color 0.3s ease;
}

body.dark-mode .sidebar-nav ul li a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -5px;
    width: 5px;
    height: 100%;
    background: linear-gradient(to bottom, #4d84ff, #3a6fd8);
    border-radius: 0 4px 4px 0;
    opacity: 0;
    transition: all 0.3s ease;
}

body.dark-mode .sidebar-nav ul li a:hover::before {
    opacity: 1;
    left: 0;
}

body.dark-mode .sidebar-nav ul li a.active::before {
    opacity: 1;
    left: 0;
}

/* Fleet Composition Chart Styles */
.fleet-composition-container {
    height: 400px;
    width: 100%;
    margin-bottom: 1.5rem;
    padding: 1.5rem;
}

.fleet-composition-container h4 {
    text-align: center;
    margin-bottom: 1rem;
    color: var(--text-color-dark);
    font-size: 1.1rem;
    font-weight: 600;
}

.fleet-composition-container canvas {
    width: 100% !important;
    height: 320px !important;
}

/* Dark mode adjustments for fleet composition chart */
body.dark-mode .fleet-composition-container h4 {
    color: #e2e8f0;
    text-shadow: 0 0 10px rgba(77, 132, 255, 0.2);
}

/* Make sure the Fleet Composition Chart is responsive */
@media (max-width: 768px) {
    .fleet-composition-container {
        height: 350px;
        padding: 1rem;
    }

    .fleet-composition-container canvas {
        height: 280px !important;
    }
}

@media (max-width: 480px) {
    .fleet-composition-container {
        height: 300px;
    }

    .fleet-composition-container canvas {
        height: 240px !important;
    }
}

/* Upcoming Services Table Styles */
.upcoming-services {
    margin: 1rem 0;
}

.upcoming-services .services-filter {
    margin-bottom: 10px;
    display: flex;
    gap: 10px;
    justify-content: flex-start;
}

.upcoming-services .filter-btn {
    padding: 8px 15px;
    border: none;
    border-radius: 5px;
    background-color: #f0f0f0;
    color: #333;
    cursor: pointer;
    font-size: 0.9em;
    transition: background-color 0.3s ease;
}

.upcoming-services .filter-btn.active,
.upcoming-services .filter-btn:hover {
    background-color: #e0e0e0;
}

/* Table Row Status Colors */
#upcoming-services-table tr.critical-row {
    background-color: rgba(239, 68, 68, 0.1);
}

#upcoming-services-table tr.warning-row {
    background-color: rgba(245, 158, 11, 0.1);
}

#upcoming-services-table tr.good-row {
    background-color: rgba(34, 197, 94, 0.1);
}

/* Service Type Colors */
#upcoming-services-table tr.maintenance-service {
    background-color: #e8f5e9;
}

#upcoming-services-table tr.tires-service {
    background-color: #fff3e0;
}

#upcoming-services-table tr.license-service {
    background-color: #e3f2fd;
}

/* Status Indicator */
.status-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.9rem;
}

.status-indicator i {
    font-size: 1rem;
}

.status-indicator.critical {
    background-color: rgba(239, 68, 68, 0.1);
    color: #ef4444;
}

.status-indicator.warning {
    background-color: rgba(245, 158, 11, 0.1);
    color: #f59e0b;
}

.status-indicator.good {
    background-color: rgba(34, 197, 94, 0.1);
    color: #22c55e;
}

.status-indicator.neutral {
    background-color: rgba(107, 114, 128, 0.1);
    color: #6b7280;
}

.status-indicator span {
    font-weight: 500;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-end;
}

/* Dark Mode Styles */
body.dark-mode .status-indicator.critical {
    background-color: rgba(239, 68, 68, 0.2);
}

body.dark-mode .status-indicator.warning {
    background-color: rgba(245, 158, 11, 0.2);
}

body.dark-mode .status-indicator.good {
    background-color: rgba(34, 197, 94, 0.2);
}

body.dark-mode .status-indicator.neutral {
    background-color: rgba(107, 114, 128, 0.2);
}

body.dark-mode #upcoming-services-table tr.critical-row {
    background-color: rgba(239, 68, 68, 0.15);
}

body.dark-mode #upcoming-services-table tr.warning-row {
    background-color: rgba(245, 158, 11, 0.15);
}

body.dark-mode #upcoming-services-table tr.good-row {
    background-color: rgba(34, 197, 94, 0.15);
}

/* Table Styles */
#upcoming-services-table {
    width: 100%;
    border-collapse: collapse;
    margin: 1rem 0;
}

#upcoming-services-table th,
#upcoming-services-table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

#upcoming-services-table th {
    font-weight: 600;
    background-color: var(--card-bg-color);
    color: var(--text-color-dark);
}

#upcoming-services-table tbody tr:hover {
    background-color: var(--hover-color);
}

/* Service Type Filter */
.service-type-filter {
    display: flex;
    gap: 0.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .upcoming-services .services-filter {
        flex-wrap: wrap;
    }

    #upcoming-services-table {
        font-size: 0.9rem;
    }

    .status-indicator {
        font-size: 0.8rem;
    }
}
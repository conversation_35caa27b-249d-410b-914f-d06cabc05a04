<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة الأسطول - Fleet Management System</title>
    <meta name="description" content="نظام شامل لإدارة الأسطول وتتبع المركبات والصيانة والعمليات">
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="./assets/css/main.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    
    <!-- External Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    
    <!-- PWA Support -->
    <link rel="manifest" href="./manifest.json">
    <meta name="theme-color" content="#0057ff">
    <link rel="apple-touch-icon" href="./assets/icons/icon-192.png">
    
    <!-- Favicon -->
    <link rel="icon" href="./favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="./favicon.ico" type="image/x-icon">
    
    <!-- Service Worker Registration -->
    <script>
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('./sw.js')
                    .then(registration => {
                        console.log('ServiceWorker registration successful');
                    })
                    .catch(error => {
                        console.log('ServiceWorker registration failed:', error);
                    });
            });
        }
    </script>
</head>
<body class="login-page">
    <!-- Login Container -->
    <div id="login-container" class="login-container">
        <div class="header">
            <img src="./assets/images/logo.png" alt="شعار الشركة" class="logo">
            <h1>مرحباً بك في نظام إدارة الأسطول</h1>
        </div>
        <div class="login-box">
            <h2>تسجيل الدخول</h2>
            <form id="login-form">
                <div class="input-container">
                    <i class="fas fa-user icon"></i>
                    <input type="text" id="email" placeholder="اسم المستخدم" required>
                </div>
                <div class="input-container">
                    <i class="fas fa-lock icon"></i>
                    <input type="password" id="password" placeholder="كلمة المرور" required>
                </div>
                <div id="login-error" class="error-message" style="display: none;"></div>
                <button type="button" id="login-btn" class="btn btn-primary">
                    <i class="fas fa-sign-in-alt"></i> دخول
                </button>
            </form>
        </div>
    </div>

    <!-- Main Application Container -->
    <div id="main-container" class="app-container" style="display: none;">
        <!-- Sidebar -->
        <div id="sidebar" class="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <img src="./assets/images/logo.png" alt="شعار الشركة">
                </div>
                <div class="user-info">
                    <div id="user-name"></div>
                    <div id="user-role"></div>
                </div>
            </div>
            
            <nav class="sidebar-nav">
                <ul>
                    <li><a href="#" data-page="dashboard" class="active">
                        <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                    </a></li>
                    <li><a href="#" data-page="vehicles">
                        <i class="fas fa-car"></i> المركبات
                    </a></li>
                    <li><a href="#" data-page="maintenance">
                        <i class="fas fa-tools"></i> الصيانة
                    </a></li>
                    <li><a href="#" data-page="fuel">
                        <i class="fas fa-gas-pump"></i> الوقود
                    </a></li>
                    <li><a href="#" data-page="drivers">
                        <i class="fas fa-users"></i> السائقين
                    </a></li>
                    <li><a href="#" data-page="reports">
                        <i class="fas fa-chart-bar"></i> التقارير
                    </a></li>
                    <li id="users-nav-item"><a href="#" data-page="users">
                        <i class="fas fa-user-cog"></i> المستخدمين
                    </a></li>
                </ul>
            </nav>
            
            <div class="sidebar-footer">
                <button id="logout-btn" class="btn btn-danger">
                    <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                </button>
            </div>
        </div>

        <!-- Main Content -->
        <div id="content" class="main-content">
            <!-- Header -->
            <header class="main-header">
                <button id="toggle-sidebar" class="toggle-sidebar">
                    <i class="fas fa-bars"></i>
                </button>
                <h2 id="current-page-title">لوحة التحكم</h2>
                <div class="user-menu">
                    <span id="header-user-name"></span>
                </div>
            </header>

            <!-- Page Content -->
            <div class="page-content">
                <!-- Dashboard Page -->
                <div id="dashboard-page" class="page active">
                    <div class="page-header">
                        <h1 class="page-title">لوحة التحكم</h1>
                    </div>
                    
                    <!-- Dashboard content will be loaded dynamically -->
                    <div id="dashboard-content">
                        <div class="loading">جاري تحميل البيانات...</div>
                    </div>
                </div>

                <!-- Vehicles Page -->
                <div id="vehicles-page" class="page">
                    <div class="page-header">
                        <h1 class="page-title">إدارة المركبات</h1>
                        <div class="page-actions">
                            <button id="add-vehicle-btn" class="btn btn-primary">
                                <i class="fas fa-plus"></i> إضافة مركبة
                            </button>
                        </div>
                    </div>
                    
                    <div id="vehicles-content">
                        <div class="loading">جاري تحميل المركبات...</div>
                    </div>
                </div>

                <!-- Maintenance Page -->
                <div id="maintenance-page" class="page">
                    <div class="page-header">
                        <h1 class="page-title">إدارة الصيانة</h1>
                        <div class="page-actions">
                            <button id="add-maintenance-btn" class="btn btn-primary">
                                <i class="fas fa-plus"></i> إضافة سجل صيانة
                            </button>
                        </div>
                    </div>
                    
                    <div id="maintenance-content">
                        <div class="loading">جاري تحميل سجلات الصيانة...</div>
                    </div>
                </div>

                <!-- Fuel Page -->
                <div id="fuel-page" class="page">
                    <div class="page-header">
                        <h1 class="page-title">إدارة الوقود</h1>
                        <div class="page-actions">
                            <button id="add-fuel-btn" class="btn btn-primary">
                                <i class="fas fa-plus"></i> إضافة سجل وقود
                            </button>
                        </div>
                    </div>
                    
                    <div id="fuel-content">
                        <div class="loading">جاري تحميل سجلات الوقود...</div>
                    </div>
                </div>

                <!-- Drivers Page -->
                <div id="drivers-page" class="page">
                    <div class="page-header">
                        <h1 class="page-title">إدارة السائقين</h1>
                        <div class="page-actions">
                            <button id="add-driver-btn" class="btn btn-primary">
                                <i class="fas fa-plus"></i> إضافة سائق
                            </button>
                        </div>
                    </div>
                    
                    <div id="drivers-content">
                        <div class="loading">جاري تحميل السائقين...</div>
                    </div>
                </div>

                <!-- Users Page -->
                <div id="users-page" class="page">
                    <div class="page-header">
                        <h1 class="page-title">إدارة المستخدمين</h1>
                        <div class="page-actions">
                            <button id="add-user-btn" class="btn btn-primary">
                                <i class="fas fa-plus"></i> إضافة مستخدم
                            </button>
                        </div>
                    </div>
                    
                    <div id="users-content">
                        <div class="loading">جاري تحميل المستخدمين...</div>
                    </div>
                </div>

                <!-- Reports Page -->
                <div id="reports-page" class="page">
                    <div class="page-header">
                        <h1 class="page-title">التقارير</h1>
                    </div>
                    
                    <div id="reports-content">
                        <div class="loading">جاري تحميل التقارير...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modals Container -->
    <div id="modals-container"></div>

    <!-- Load Main Application -->
    <script type="module" src="./main.js"></script>
</body>
</html>

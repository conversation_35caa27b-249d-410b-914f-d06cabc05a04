/**
 * Main Application Entry Point - نقطة الدخول الرئيسية للتطبيق
 * يقوم بتهيئة التطبيق وتحميل جميع المكونات الأساسية
 */

// استيراد المكونات الأساسية
import router from './router.js';
import appStore, { storeActions, storeSelectors } from './store/store.js';
import authService from './services/authService.js';
import dataService from './services/dataService.js';
import { showNotification, showSpinner, hideSpinner } from './utils/utility.js';
import { injectSpinnerCSS } from './components/Spinner.js';
import { injectStatCardCSS } from './components/StatCard.js';

/**
 * فئة التطبيق الرئيسية
 */
class FleetManagementApp {
    constructor() {
        this.isInitialized = false;
        this.currentUser = null;
        
        // ربط الأحداث
        this.bindEvents();
    }

    /**
     * تهيئة التطبيق
     */
    async initialize() {
        try {
            showSpinner('جاري تحميل التطبيق...');
            
            // تحميل CSS المطلوب
            this.loadRequiredCSS();
            
            // تهيئة نظام المصادقة
            await this.initializeAuth();
            
            // تهيئة واجهة المستخدم
            this.initializeUI();
            
            // تهيئة الراوتر
            this.initializeRouter();
            
            // تحميل البيانات الأولية
            await this.loadInitialData();
            
            // تهيئة الأحداث العامة
            this.initializeGlobalEvents();
            
            this.isInitialized = true;
            console.log('Fleet Management App initialized successfully');
            
        } catch (error) {
            console.error('Failed to initialize app:', error);
            showNotification('فشل في تحميل التطبيق', 'error');
        } finally {
            hideSpinner();
        }
    }

    /**
     * تحميل CSS المطلوب
     */
    loadRequiredCSS() {
        // تحميل CSS للمكونات
        injectSpinnerCSS();
        injectStatCardCSS();
        
        // تحميل CSS للإشعارات
        this.injectNotificationCSS();
    }

    /**
     * تهيئة نظام المصادقة
     */
    async initializeAuth() {
        // التحقق من وجود جلسة محفوظة
        if (authService.isUserAuthenticated()) {
            const user = authService.getCurrentUser();
            this.currentUser = user;
            storeActions.setUser(user);
            
            // التحقق من صحة الجلسة
            const isValid = await authService.validateSession();
            if (!isValid) {
                await authService.logout();
                this.currentUser = null;
            }
        }
        
        // الاستماع لتغييرات المصادقة
        authService.onAuthChange((type, userData) => {
            if (type === 'login') {
                this.currentUser = userData;
                storeActions.setUser(userData);
                this.showMainInterface();
            } else if (type === 'logout') {
                this.currentUser = null;
                storeActions.logout();
                this.showLoginInterface();
            }
        });
    }

    /**
     * تهيئة واجهة المستخدم
     */
    initializeUI() {
        // إظهار الواجهة المناسبة حسب حالة المصادقة
        if (this.currentUser) {
            this.showMainInterface();
        } else {
            this.showLoginInterface();
        }
        
        // تهيئة الوضع المظلم
        this.initializeDarkMode();
        
        // تهيئة الشريط الجانبي
        this.initializeSidebar();
        
        // تهيئة نظام الإشعارات
        this.initializeNotifications();
    }

    /**
     * إظهار الواجهة الرئيسية
     */
    showMainInterface() {
        const loginContainer = document.getElementById('login-container');
        const mainContainer = document.getElementById('main-container');
        const sidebar = document.getElementById('sidebar');
        
        if (loginContainer) loginContainer.style.display = 'none';
        if (mainContainer) mainContainer.style.display = 'flex';
        if (sidebar) sidebar.style.display = 'block';
        
        // تحديث معلومات المستخدم في الواجهة
        this.updateUserInterface();
        
        // تطبيق قيود الصلاحيات
        this.applyPermissionRestrictions();
    }

    /**
     * إظهار واجهة تسجيل الدخول
     */
    showLoginInterface() {
        const loginContainer = document.getElementById('login-container');
        const mainContainer = document.getElementById('main-container');
        
        if (loginContainer) loginContainer.style.display = 'flex';
        if (mainContainer) mainContainer.style.display = 'none';
        
        document.body.className = 'login-page';
    }

    /**
     * تحديث معلومات المستخدم في الواجهة
     */
    updateUserInterface() {
        if (!this.currentUser) return;
        
        const userName = document.getElementById('user-name');
        const userRole = document.getElementById('user-role');
        const headerUserName = document.getElementById('header-user-name');
        
        if (userName) userName.textContent = this.currentUser.name;
        if (userRole) userRole.textContent = this.currentUser.role;
        if (headerUserName) headerUserName.textContent = `مرحباً، ${this.currentUser.name}`;
        
        // تحديث فئة الجسم حسب الدور
        document.body.className = this.currentUser.role.toLowerCase().replace(/\s+/g, '-');
    }

    /**
     * تطبيق قيود الصلاحيات
     */
    applyPermissionRestrictions() {
        // سيتم تنفيذ هذا لاحقاً بناءً على نظام الصلاحيات
        console.log('Applying permission restrictions for:', this.currentUser?.role);
    }

    /**
     * تهيئة الراوتر
     */
    initializeRouter() {
        // إضافة middleware للتحقق من المصادقة
        router.beforeEach((from, to) => {
            if (to.requiresAuth && !this.currentUser) {
                showNotification('يجب تسجيل الدخول أولاً', 'warning');
                return false;
            }
            return true;
        });
        
        // إضافة middleware لتحديث العنوان
        router.afterEach((route) => {
            document.title = `${route.title} - نظام إدارة الأسطول`;
        });
    }

    /**
     * تحميل البيانات الأولية
     */
    async loadInitialData() {
        if (!this.currentUser) return;
        
        try {
            // تحميل البيانات الأساسية
            const [vehicles, drivers] = await Promise.all([
                dataService.getVehicles(),
                dataService.getDrivers()
            ]);
            
            // تحديث الحالة
            storeActions.setVehicles(vehicles);
            storeActions.setDrivers(drivers);
            
            console.log('Initial data loaded successfully');
        } catch (error) {
            console.error('Failed to load initial data:', error);
            showNotification('فشل في تحميل البيانات الأولية', 'error');
        }
    }

    /**
     * تهيئة الأحداث العامة
     */
    initializeGlobalEvents() {
        // تسجيل الخروج
        const logoutBtn = document.getElementById('logout-btn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', async () => {
                try {
                    await authService.logout();
                } catch (error) {
                    console.error('Logout error:', error);
                }
            });
        }
        
        // تسجيل الدخول
        const loginForm = document.getElementById('login-form');
        const loginBtn = document.getElementById('login-btn');
        
        if (loginForm && loginBtn) {
            loginBtn.addEventListener('click', async (e) => {
                e.preventDefault();
                await this.handleLogin();
            });
            
            loginForm.addEventListener('submit', async (e) => {
                e.preventDefault();
                await this.handleLogin();
            });
        }
        
        // تبديل الشريط الجانبي
        const toggleSidebar = document.getElementById('toggle-sidebar');
        if (toggleSidebar) {
            toggleSidebar.addEventListener('click', () => {
                storeActions.toggleSidebar();
            });
        }
    }

    /**
     * معالجة تسجيل الدخول
     */
    async handleLogin() {
        const emailInput = document.getElementById('email');
        const passwordInput = document.getElementById('password');
        const errorDiv = document.getElementById('login-error');
        
        if (!emailInput || !passwordInput) return;
        
        const email = emailInput.value.trim();
        const password = passwordInput.value.trim();
        
        if (!email || !password) {
            this.showLoginError('يرجى إدخال اسم المستخدم وكلمة المرور');
            return;
        }
        
        try {
            showSpinner('جاري تسجيل الدخول...');
            
            const result = await authService.login({ email, password });
            
            if (result.success) {
                // سيتم التعامل مع النجاح في onAuthChange
                hideSpinner();
            }
        } catch (error) {
            hideSpinner();
            console.error('Login error:', error);
            this.showLoginError(error.message || 'فشل في تسجيل الدخول');
        }
    }

    /**
     * إظهار خطأ تسجيل الدخول
     */
    showLoginError(message) {
        const errorDiv = document.getElementById('login-error');
        if (errorDiv) {
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            
            // إخفاء الخطأ بعد 5 ثوان
            setTimeout(() => {
                errorDiv.style.display = 'none';
            }, 5000);
        }
    }

    /**
     * تهيئة الوضع المظلم
     */
    initializeDarkMode() {
        const savedTheme = localStorage.getItem('fleet_theme');
        if (savedTheme === 'dark') {
            document.body.classList.add('dark-mode');
        }
        
        // إضافة زر تبديل الوضع المظلم إذا لم يكن موجوداً
        this.createDarkModeToggle();
    }

    /**
     * إنشاء زر تبديل الوضع المظلم
     */
    createDarkModeToggle() {
        if (document.getElementById('dark-mode-toggle')) return;
        
        const toggle = document.createElement('button');
        toggle.id = 'dark-mode-toggle';
        toggle.className = 'dark-mode-toggle';
        toggle.innerHTML = '<i class="fas fa-moon"></i>';
        toggle.title = 'تبديل الوضع المظلم';
        
        toggle.addEventListener('click', () => {
            document.body.classList.toggle('dark-mode');
            const isDark = document.body.classList.contains('dark-mode');
            localStorage.setItem('fleet_theme', isDark ? 'dark' : 'light');
            storeActions.setTheme(isDark ? 'dark' : 'light');
        });
        
        // إضافة الزر للهيدر
        const header = document.querySelector('.main-header');
        if (header) {
            header.appendChild(toggle);
        }
    }

    /**
     * تهيئة الشريط الجانبي
     */
    initializeSidebar() {
        // الاستماع لتغييرات حالة الشريط الجانبي
        appStore.subscribe(
            storeSelectors.getCurrentPage,
            (currentPage) => {
                // تحديث التنقل النشط
                this.updateActiveNavigation(currentPage);
            }
        );
    }

    /**
     * تحديث التنقل النشط
     */
    updateActiveNavigation(currentPage) {
        const navLinks = document.querySelectorAll('.sidebar-nav a');
        navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('data-page') === currentPage) {
                link.classList.add('active');
            }
        });
    }

    /**
     * تهيئة نظام الإشعارات
     */
    initializeNotifications() {
        // الاستماع للإشعارات من Store
        appStore.subscribe(
            storeSelectors.getNotifications,
            (notifications) => {
                // عرض الإشعارات الجديدة
                notifications.forEach(notification => {
                    if (!notification.displayed) {
                        showNotification(notification.message, notification.type);
                        notification.displayed = true;
                    }
                });
            }
        );
    }

    /**
     * حقن CSS للإشعارات
     */
    injectNotificationCSS() {
        if (document.getElementById('notification-styles')) return;
        
        const css = `
            <style id="notification-styles">
                .notification-container {
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    z-index: 10000;
                    max-width: 400px;
                }
                
                .notification {
                    background: white;
                    border-radius: 8px;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                    margin-bottom: 10px;
                    transform: translateX(100%);
                    transition: transform 0.3s ease;
                }
                
                .notification.show {
                    transform: translateX(0);
                }
                
                .notification.hide {
                    transform: translateX(100%);
                }
                
                .notification-content {
                    padding: 16px;
                    display: flex;
                    align-items: center;
                    gap: 12px;
                }
                
                .notification-success { border-left: 4px solid #22c55e; }
                .notification-error { border-left: 4px solid #ef4444; }
                .notification-warning { border-left: 4px solid #f59e0b; }
                .notification-info { border-left: 4px solid #3b82f6; }
                
                .notification-close {
                    background: none;
                    border: none;
                    cursor: pointer;
                    color: #666;
                    margin-left: auto;
                }
            </style>
        `;
        
        document.head.insertAdjacentHTML('beforeend', css);
    }
}

// إنشاء وتهيئة التطبيق
const app = new FleetManagementApp();

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    app.initialize();
});

// تصدير التطبيق للاستخدام العام
export default app;

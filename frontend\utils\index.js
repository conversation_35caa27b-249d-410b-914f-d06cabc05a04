/**
 * Utils Index - فهرس الأدوات المساعدة
 * يجمع جميع الأدوات المساعدة ويصدرها من مكان واحد
 */

// استيراد جميع الأدوات
export * from './constants.js';
export * from './utility.js';
export * from './permissions.js';
export * from './validation.js';
export * from './storage.js';

// استيراد افتراضي
import constants from './constants.js';
import utility from './utility.js';
import permissions from './permissions.js';
import validation from './validation.js';
import storage from './storage.js';

/**
 * تهيئة جميع الأدوات المساعدة
 */
export function initializeUtils() {
    try {
        console.log('Initializing utilities...');
        
        // تنظيف التخزين المحلي
        storage.cleanupStorage();
        
        // تحقق من توفر الميزات
        const features = {
            localStorage: utility.isFeatureSupported('localStorage'),
            clipboard: utility.isFeatureSupported('clipboard'),
            notifications: utility.isFeatureSupported('notifications'),
            geolocation: utility.isFeatureSupported('geolocation')
        };
        
        console.log('Available features:', features);
        
        // إعداد معالج الأخطاء العام
        setupGlobalErrorHandler();
        
        console.log('Utilities initialized successfully');
        return true;
        
    } catch (error) {
        console.error('Failed to initialize utilities:', error);
        return false;
    }
}

/**
 * إعداد معالج الأخطاء العام
 */
function setupGlobalErrorHandler() {
    // معالج أخطاء JavaScript
    window.addEventListener('error', (event) => {
        console.error('Global error:', event.error);
        utility.showError('حدث خطأ غير متوقع في التطبيق');
    });
    
    // معالج الوعود المرفوضة
    window.addEventListener('unhandledrejection', (event) => {
        console.error('Unhandled promise rejection:', event.reason);
        utility.showError('حدث خطأ في معالجة البيانات');
        event.preventDefault();
    });
}

/**
 * تنظيف الأدوات المساعدة
 */
export function cleanupUtils() {
    try {
        console.log('Cleaning up utilities...');
        
        // تنظيف التخزين المحلي
        storage.cleanupStorage();
        
        console.log('Utilities cleaned up successfully');
        return true;
        
    } catch (error) {
        console.error('Failed to cleanup utilities:', error);
        return false;
    }
}

/**
 * الحصول على معلومات النظام
 */
export function getSystemInfo() {
    return {
        // معلومات المتصفح
        browser: {
            userAgent: navigator.userAgent,
            language: navigator.language,
            platform: navigator.platform,
            cookieEnabled: navigator.cookieEnabled,
            onLine: navigator.onLine
        },
        
        // معلومات الشاشة
        screen: {
            width: screen.width,
            height: screen.height,
            availWidth: screen.availWidth,
            availHeight: screen.availHeight,
            colorDepth: screen.colorDepth
        },
        
        // معلومات النافذة
        window: {
            innerWidth: window.innerWidth,
            innerHeight: window.innerHeight,
            devicePixelRatio: window.devicePixelRatio
        },
        
        // معلومات التخزين
        storage: storage.getStorageInfo(),
        
        // الميزات المدعومة
        features: {
            localStorage: utility.isFeatureSupported('localStorage'),
            clipboard: utility.isFeatureSupported('clipboard'),
            notifications: utility.isFeatureSupported('notifications'),
            geolocation: utility.isFeatureSupported('geolocation'),
            webWorkers: utility.isFeatureSupported('webWorkers'),
            serviceWorkers: utility.isFeatureSupported('serviceWorkers')
        }
    };
}

/**
 * دوال مساعدة للتطبيق
 */

// دالة موحدة للتحقق من صحة البيانات
export function validateData(data, type) {
    return validation.validateForm(data, type);
}

// دالة موحدة لحفظ البيانات
export function saveData(key, data, options = {}) {
    return storage.setStorageItem(key, data, options);
}

// دالة موحدة لاسترجاع البيانات
export function loadData(key, defaultValue = null) {
    return storage.getStorageItem(key, defaultValue);
}

// دالة موحدة للتحقق من الصلاحيات
export function checkPermission(userRole, permission) {
    return permissions.hasPermission(userRole, permission);
}

// دالة موحدة لعرض الإشعارات
export function notify(message, type = 'info') {
    return utility.showNotification(message, type);
}

// دالة موحدة لتنسيق البيانات
export function format(value, type, options = {}) {
    switch (type) {
        case 'number':
            return utility.formatNumber(value, options);
        case 'currency':
            return utility.formatCurrency(value, options.currency);
        case 'percentage':
            return utility.formatPercentage(value, options.decimals);
        case 'date':
            return utility.formatDate(value, options.format);
        case 'relativeDate':
            return utility.formatRelativeDate(value);
        case 'timeRemaining':
            return utility.formatTimeRemaining(value);
        default:
            return value;
    }
}

/**
 * دوال مساعدة للنماذج
 */
export function validateForm(formElement, validationType) {
    const formData = new FormData(formElement);
    const data = Object.fromEntries(formData.entries());
    
    const result = validation.validateForm(data, validationType);
    
    if (!result.isValid) {
        validation.displayValidationErrors(result.errors, formElement);
    } else {
        validation.clearValidationErrors(formElement);
    }
    
    return result;
}

/**
 * دوال مساعدة للبيانات
 */
export function filterData(data, searchTerm, searchFields = []) {
    return utility.filterArray(data, searchTerm, searchFields);
}

export function sortData(data, field, direction = 'asc') {
    return utility.sortBy(data, field, direction);
}

export function groupData(data, field) {
    return utility.groupBy(data, field);
}

/**
 * دوال مساعدة للتصدير
 */
export function exportToCSV(data, filename = 'data.csv', headers = null) {
    return utility.downloadCSV(data, filename, headers);
}

export function exportToJSON(data, filename = 'data.json') {
    return utility.downloadJSON(data, filename);
}

/**
 * دوال مساعدة للتخزين المؤقت
 */
export function cacheData(key, data, hours = 24) {
    return storage.saveCachedData(key, data, hours);
}

export function getCachedData(key) {
    return storage.getCachedData(key);
}

export function clearCache(key = null) {
    return storage.clearCachedData(key);
}

/**
 * مجموعة أدوات مجمعة للاستخدام السهل
 */
export const utils = {
    // الثوابت
    constants,
    
    // الأدوات العامة
    utility,
    
    // الصلاحيات
    permissions,
    
    // التحقق من صحة البيانات
    validation,
    
    // التخزين
    storage,
    
    // دوال مساعدة
    validateData,
    saveData,
    loadData,
    checkPermission,
    notify,
    format,
    validateForm,
    filterData,
    sortData,
    groupData,
    exportToCSV,
    exportToJSON,
    cacheData,
    getCachedData,
    clearCache,
    
    // معلومات النظام
    getSystemInfo,
    
    // إدارة الأدوات
    initialize: initializeUtils,
    cleanup: cleanupUtils
};

// تصدير افتراضي
export default {
    // الأدوات الأساسية
    constants,
    utility,
    permissions,
    validation,
    storage,
    
    // مجموعة الأدوات
    utils,
    
    // دوال الإدارة
    initializeUtils,
    cleanupUtils,
    getSystemInfo,
    
    // دوال مساعدة
    validateData,
    saveData,
    loadData,
    checkPermission,
    notify,
    format,
    validateForm,
    filterData,
    sortData,
    groupData,
    exportToCSV,
    exportToJSON,
    cacheData,
    getCachedData,
    clearCache
};

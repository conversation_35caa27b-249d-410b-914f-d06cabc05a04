<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة الأسطول - Fleet Management System</title>
    <meta http-equiv="refresh" content="0; url=frontend/">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            direction: rtl;
        }
        .redirect-container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
            max-width: 500px;
            margin: 20px;
        }
        .logo {
            width: 80px;
            height: 80px;
            background: #667eea;
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
        }
        h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 1.8rem;
        }
        p {
            color: #666;
            margin-bottom: 30px;
            line-height: 1.6;
        }
        .btn {
            background: #667eea;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-size: 1.1rem;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            margin: 10px;
        }
        .btn:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }
        .btn-success {
            background: #28a745;
        }
        .btn-success:hover {
            background: #218838;
        }
        .countdown {
            color: #999;
            font-size: 0.9rem;
            margin-top: 20px;
        }
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="redirect-container">
        <div class="logo">
            <i class="fas fa-truck"></i>
        </div>
        <h1>جاري التوجيه للنظام الجديد...</h1>
        <div class="spinner"></div>
        <p>يتم توجيهك تلقائياً إلى النظام المحدث في مجلد frontend</p>
        
        <div style="margin: 20px 0;">
            <a href="frontend/" class="btn">
                <i class="fas fa-arrow-left" style="margin-left: 10px;"></i>
                انتقل الآن
            </a>
            
            <a href="frontend/test.html" class="btn btn-success">
                <i class="fas fa-vial" style="margin-left: 10px;"></i>
                اختبار النظام
            </a>
        </div>
        
        <div class="countdown">
            سيتم التوجيه خلال <span id="countdown">3</span> ثوان...
        </div>
    </div>

    <script>
        // توجيه فوري
        window.location.href = 'frontend/';
        
        // عد تنازلي احتياطي
        let countdown = 3;
        const countdownElement = document.getElementById('countdown');
        
        const timer = setInterval(() => {
            countdown--;
            if (countdownElement) {
                countdownElement.textContent = countdown;
            }
            
            if (countdown <= 0) {
                clearInterval(timer);
                window.location.href = 'frontend/';
            }
        }, 1000);
    </script>
</body>
</html>

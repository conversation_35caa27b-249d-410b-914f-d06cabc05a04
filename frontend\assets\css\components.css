/**
 * Components CSS - تنسيق المكونات
 * يحتوي على تنسيقات المكونات القابلة لإعادة الاستخدام
 */

/* Modal Components - النوافذ المنبثقة */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    padding: 1rem;
}

.modal.active {
    display: flex;
}

.modal-content {
    background-color: var(--card-bg-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow-lg);
    max-width: 90vw;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
    animation: modalSlideIn 0.3s ease-out;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.modal-title {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-color-dark);
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    padding: 1.5rem;
    border-top: 1px solid var(--border-color);
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-color-light);
    transition: color var(--transition-speed);
    padding: 0.25rem;
    border-radius: var(--border-radius);
}

.close-btn:hover {
    color: var(--danger-color);
    background-color: rgba(var(--danger-color-rgb), 0.1);
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Spinner Components - مؤشرات التحميل */
.spinner-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 2rem;
}

.spinner-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9999;
}

.spinner-content {
    text-align: center;
    color: white;
}

.spinner-text {
    margin-top: 1rem;
    font-size: 0.875rem;
    color: var(--text-color-light);
}

.spinner-default .spinner-circle {
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.spinner-small .spinner-circle { width: 20px; height: 20px; }
.spinner-medium .spinner-circle { width: 40px; height: 40px; }
.spinner-large .spinner-circle { width: 60px; height: 60px; }

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Stat Cards - بطاقات الإحصائيات */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: var(--card-bg-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--box-shadow);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all var(--transition-speed);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--box-shadow-lg);
}

.stat-card.clickable {
    cursor: pointer;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    flex-shrink: 0;
}

.stat-icon.blue { background: var(--primary-color); }
.stat-icon.green { background: var(--success-color); }
.stat-icon.orange { background: var(--warning-color); }
.stat-icon.red { background: var(--danger-color); }
.stat-icon.purple { background: #6f42c1; }

.stat-info {
    flex: 1;
    min-width: 0;
}

.stat-title {
    margin: 0 0 0.5rem 0;
    font-size: 0.875rem;
    color: var(--text-color-light);
    font-weight: 500;
}

.stat-value {
    margin: 0;
    font-size: 1.75rem;
    font-weight: bold;
    color: var(--text-color-dark);
    line-height: 1;
}

.stat-subtitle {
    font-size: 0.75rem;
    color: var(--text-color-light);
    margin-top: 0.25rem;
}

.stat-trend {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.75rem;
    margin-top: 0.5rem;
}

.trend-up { color: var(--success-color); }
.trend-down { color: var(--danger-color); }

/* Navigation Components - مكونات التنقل */
.nav-tabs {
    display: flex;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 1rem;
}

.nav-tab {
    padding: 0.75rem 1rem;
    border: none;
    background: none;
    cursor: pointer;
    color: var(--text-color-light);
    border-bottom: 2px solid transparent;
    transition: all var(--transition-speed);
    font-weight: 500;
}

.nav-tab:hover {
    color: var(--primary-color);
}

.nav-tab.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

/* Dropdown Components - القوائم المنسدلة */
.dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-toggle {
    background: var(--card-bg-color);
    border: 1px solid var(--border-color);
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all var(--transition-speed);
}

.dropdown-toggle:hover {
    border-color: var(--primary-color);
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: var(--card-bg-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow-lg);
    min-width: 200px;
    z-index: 1000;
    display: none;
    animation: fadeIn 0.2s ease-out;
}

.dropdown.active .dropdown-menu {
    display: block;
}

.dropdown-item {
    display: block;
    padding: 0.75rem 1rem;
    color: var(--text-color);
    text-decoration: none;
    transition: background-color var(--transition-speed);
    border: none;
    background: none;
    width: 100%;
    text-align: left;
    cursor: pointer;
}

.dropdown-item:hover {
    background-color: var(--hover-color);
}

.dropdown-divider {
    height: 1px;
    background-color: var(--border-color);
    margin: 0.5rem 0;
}

/* Alert Components - تنبيهات */
.alert {
    padding: 1rem;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    border: 1px solid transparent;
}

.alert-success {
    background-color: rgba(var(--success-color-rgb), 0.1);
    border-color: var(--success-color);
    color: var(--success-color);
}

.alert-danger {
    background-color: rgba(var(--danger-color-rgb), 0.1);
    border-color: var(--danger-color);
    color: var(--danger-color);
}

.alert-warning {
    background-color: rgba(var(--warning-color-rgb), 0.1);
    border-color: var(--warning-color);
    color: var(--warning-color);
}

.alert-info {
    background-color: rgba(var(--primary-color-rgb), 0.1);
    border-color: var(--primary-color);
    color: var(--primary-color);
}

/* Progress Components - شرائط التقدم */
.progress {
    width: 100%;
    height: 0.5rem;
    background-color: var(--border-color);
    border-radius: var(--border-radius);
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background-color: var(--primary-color);
    transition: width 0.3s ease;
}

.progress-bar.success { background-color: var(--success-color); }
.progress-bar.warning { background-color: var(--warning-color); }
.progress-bar.danger { background-color: var(--danger-color); }

/* Tooltip Components - تلميحات الأدوات */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltip-text {
    visibility: hidden;
    width: 200px;
    background-color: #333;
    color: #fff;
    text-align: center;
    border-radius: var(--border-radius);
    padding: 0.5rem;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -100px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 0.875rem;
}

.tooltip:hover .tooltip-text {
    visibility: visible;
    opacity: 1;
}

/* Search Components - مكونات البحث */
.search-box {
    position: relative;
    max-width: 400px;
}

.search-box input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 0.95rem;
    transition: all var(--transition-speed);
}

.search-box input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.1);
}

.search-box .search-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-color-light);
    pointer-events: none;
}

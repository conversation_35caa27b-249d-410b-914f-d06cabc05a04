# CSS Directory - مج<PERSON>د التنسيق

هذا المجلد يحتوي على نظام تنسيق شامل ومتجاوب للتطبيق باستخدام CSS خالص.

## هيكل المجلد

```
css/
├── index.css                # الفهرس الرئيسي - يستورد جميع الملفات
├── main.css                 # الأنماط الأساسية والمتغيرات
├── layout.css               # تخطيط التطبيق (Header, Sidebar, etc.)
├── components.css           # مكونات قابلة لإعادة الاستخدام
├── pages.css                # أنماط خاصة بالصفحات
├── utilities.css            # فئات مساعدة وأدوات سريعة
└── README.md               # هذا الملف
```

## الملفات والمحتويات

### 1. index.css - الفهرس الرئيسي
**الوظيفة**: يستورد جميع ملفات CSS بالترتيب الصحيح

#### المحتويات:
- استيراد الخطوط العربية
- استيراد جميع ملفات CSS
- تحسينات للطباعة
- تحسينات لإمكانية الوصول
- تحسينات للوضع المظلم
- تحسينات للأداء

#### الاستخدام:
```html
<link rel="stylesheet" href="css/index.css">
```

### 2. main.css - الأنماط الأساسية
**الوظيفة**: يحتوي على المتغيرات الأساسية والأنماط العامة

#### المتغيرات الرئيسية:
```css
:root {
    /* الألوان */
    --primary-color: #2563eb;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    
    /* المسافات */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    
    /* الخطوط */
    --font-xs: 0.75rem;
    --font-sm: 0.875rem;
    --font-base: 1rem;
    --font-lg: 1.125rem;
    --font-xl: 1.25rem;
    
    /* الظلال */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
}
```

#### المكونات الأساسية:
- إعادة تعيين الأنماط
- نظام الشبكة (Grid System)
- البطاقات (Cards)
- الأزرار (Buttons)
- النماذج (Forms)
- الجداول (Tables)

### 3. layout.css - التخطيط
**الوظيفة**: يحتوي على أنماط التخطيط الأساسي للتطبيق

#### المكونات:
- **Header**: الرأس الثابت مع البحث والإشعارات
- **Sidebar**: الشريط الجانبي القابل للطي
- **Main Content**: المحتوى الرئيسي
- **Breadcrumb**: التنقل المتدرج
- **Dropdown**: القوائم المنسدلة
- **Tabs**: التبويبات

#### مثال الاستخدام:
```html
<div class="app-container">
    <header class="header">
        <!-- محتوى الرأس -->
    </header>
    
    <aside class="sidebar">
        <!-- محتوى الشريط الجانبي -->
    </aside>
    
    <main class="main-wrapper">
        <div class="page-header">
            <!-- رأس الصفحة -->
        </div>
        <div class="page-content">
            <!-- محتوى الصفحة -->
        </div>
    </main>
</div>
```

### 4. components.css - المكونات
**الوظيفة**: يحتوي على مكونات قابلة لإعادة الاستخدام

#### المكونات المتاحة:
- **Stat Cards**: بطاقات الإحصائيات
- **Modals**: النوافذ المنبثقة
- **Spinners**: مؤشرات التحميل
- **Tables**: الجداول التفاعلية
- **Notifications**: الإشعارات
- **Charts**: حاويات الرسوم البيانية
- **Empty States**: حالات فارغة

#### مثال بطاقة إحصائية:
```html
<div class="stat-card">
    <div class="stat-card-header">
        <div class="stat-card-icon blue">
            <i class="fas fa-car"></i>
        </div>
    </div>
    <div class="stat-card-value">150</div>
    <div class="stat-card-title">إجمالي المركبات</div>
    <div class="stat-card-trend up">
        <span>+5%</span>
        <span>من الشهر الماضي</span>
    </div>
</div>
```

### 5. pages.css - الصفحات
**الوظيفة**: يحتوي على أنماط خاصة بصفحات التطبيق

#### الصفحات المدعومة:
- **Dashboard**: لوحة التحكم
- **Vehicles**: صفحة المركبات
- **Drivers**: صفحة السائقين
- **Maintenance**: صفحة الصيانة
- **Reports**: صفحة التقارير
- **Login**: صفحة تسجيل الدخول
- **Settings**: صفحة الإعدادات

#### مثال صفحة المركبات:
```html
<div class="vehicles-page">
    <div class="vehicles-filters">
        <!-- فلاتر المركبات -->
    </div>
    
    <div class="vehicles-grid">
        <div class="vehicle-card">
            <div class="vehicle-card-header">
                <h3 class="vehicle-card-title">أ ب ج 123</h3>
                <span class="vehicle-card-status active">نشط</span>
            </div>
            <div class="vehicle-card-body">
                <!-- تفاصيل المركبة -->
            </div>
        </div>
    </div>
</div>
```

### 6. utilities.css - الأدوات المساعدة
**الوظيفة**: يحتوي على فئات مساعدة للتنسيق السريع

#### الفئات المتاحة:
- **Display**: `d-none`, `d-flex`, `d-block`
- **Flexbox**: `justify-content-center`, `align-items-center`
- **Spacing**: `m-3`, `p-4`, `mb-2`, `px-3`
- **Text**: `text-center`, `text-primary`, `fw-bold`
- **Background**: `bg-primary`, `bg-success`
- **Borders**: `border`, `rounded`, `border-primary`
- **Shadows**: `shadow-sm`, `shadow-lg`
- **Animations**: `fade-in`, `slide-in-up`, `pulse`

#### مثال الاستخدام:
```html
<div class="d-flex justify-content-between align-items-center p-3 bg-primary text-white rounded shadow-md">
    <h3 class="mb-0 fw-semibold">العنوان</h3>
    <button class="btn btn-light btn-sm">إجراء</button>
</div>
```

## المميزات الرئيسية

### 🎨 **نظام ألوان متسق**
- ألوان أساسية وثانوية محددة
- ألوان حالة (نجاح، تحذير، خطأ)
- دعم الوضع المظلم
- متغيرات CSS قابلة للتخصيص

### 📱 **تصميم متجاوب**
- دعم جميع أحجام الشاشات
- نقاط توقف محددة مسبقاً
- شبكة مرنة ومتجاوبة
- تحسينات للأجهزة المحمولة

### 🌐 **دعم اللغة العربية**
- اتجاه RTL كامل
- خطوط عربية محسنة
- تنسيق مناسب للنصوص العربية
- دعم الأرقام العربية والإنجليزية

### ♿ **إمكانية الوصول**
- تباين ألوان مناسب
- دعم قارئات الشاشة
- تنقل بلوحة المفاتيح
- تحسينات للحركة المخفضة

### ⚡ **الأداء المحسن**
- CSS محسن ومضغوط
- استخدام متغيرات CSS
- تحميل تدريجي للخطوط
- تحسينات للرسوم المتحركة

## الاستخدام

### تضمين CSS في HTML
```html
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة الأسطول</title>
    <link rel="stylesheet" href="css/index.css">
</head>
<body>
    <!-- محتوى التطبيق -->
</body>
</html>
```

### استخدام المتغيرات
```css
.custom-component {
    background: var(--primary-color);
    padding: var(--spacing-lg);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-sm);
}
```

### تخصيص الألوان
```css
:root {
    --primary-color: #your-color;
    --success-color: #your-success-color;
    /* المزيد من التخصيصات */
}
```

## أمثلة عملية

### بطاقة مركبة
```html
<div class="vehicle-card">
    <div class="vehicle-card-header">
        <h3 class="vehicle-card-title">تويوتا كامري 2023</h3>
        <span class="vehicle-card-status active">نشط</span>
    </div>
    <div class="vehicle-card-body">
        <div class="vehicle-card-info">
            <div class="vehicle-card-info-item">
                <span class="vehicle-card-info-label">رقم اللوحة</span>
                <span class="vehicle-card-info-value">أ ب ج 123</span>
            </div>
            <div class="vehicle-card-info-item">
                <span class="vehicle-card-info-label">السائق</span>
                <span class="vehicle-card-info-value">أحمد محمد</span>
            </div>
        </div>
        <div class="vehicle-card-actions">
            <button class="btn btn-primary btn-sm">تعديل</button>
            <button class="btn btn-outline-primary btn-sm">عرض</button>
        </div>
    </div>
</div>
```

### نافذة منبثقة
```html
<div class="modal-overlay show">
    <div class="modal medium">
        <div class="modal-header">
            <h3 class="modal-title">إضافة مركبة جديدة</h3>
            <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
            <!-- محتوى النموذج -->
        </div>
        <div class="modal-footer">
            <button class="btn btn-secondary">إلغاء</button>
            <button class="btn btn-primary">حفظ</button>
        </div>
    </div>
</div>
```

### جدول تفاعلي
```html
<div class="table-container">
    <div class="table-header">
        <h3 class="table-title">قائمة المركبات</h3>
        <div class="table-actions">
            <div class="table-search">
                <input type="text" placeholder="البحث...">
                <i class="table-search-icon fas fa-search"></i>
            </div>
            <button class="btn btn-primary">إضافة مركبة</button>
        </div>
    </div>
    <div class="table-wrapper">
        <table class="table">
            <thead>
                <tr>
                    <th class="sortable">رقم اللوحة</th>
                    <th class="sortable">النوع</th>
                    <th class="sortable">الحالة</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                <!-- صفوف الجدول -->
            </tbody>
        </table>
    </div>
</div>
```

## التخصيص والتوسيع

### إضافة ألوان جديدة
```css
:root {
    --custom-color: #your-color;
}

.btn-custom {
    background-color: var(--custom-color);
    border-color: var(--custom-color);
    color: white;
}

.text-custom {
    color: var(--custom-color);
}

.bg-custom {
    background-color: var(--custom-color);
}
```

### إضافة مكونات جديدة
```css
.my-component {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
}

.my-component:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}
```

## أفضل الممارسات

### 1. استخدام المتغيرات
```css
/* ✅ جيد */
.component {
    color: var(--text-primary);
    background: var(--bg-primary);
}

/* ❌ سيء */
.component {
    color: #1e293b;
    background: #ffffff;
}
```

### 2. استخدام الفئات المساعدة
```html
<!-- ✅ جيد -->
<div class="d-flex justify-content-between align-items-center p-3">

<!-- ❌ سيء -->
<div style="display: flex; justify-content: space-between; align-items: center; padding: 1rem;">
```

### 3. التسمية الواضحة
```css
/* ✅ جيد */
.vehicle-card-header {
    /* أنماط */
}

/* ❌ سيء */
.vc-h {
    /* أنماط */
}
```

## الصيانة والتطوير

### إضافة ميزات جديدة
1. حدد الملف المناسب للإضافة
2. اتبع نمط التسمية الموجود
3. استخدم المتغيرات المحددة
4. اختبر في جميع أحجام الشاشات
5. تأكد من إمكانية الوصول

### تحسين الأداء
1. استخدم CSS Grid و Flexbox
2. قلل من استخدام الظلال المعقدة
3. استخدم transform بدلاً من تغيير الموضع
4. اضغط CSS في الإنتاج
5. استخدم will-change بحذر

## الملاحظات

- جميع الأنماط تدعم RTL
- متوافق مع المتصفحات الحديثة
- محسن للأداء والذاكرة
- قابل للتخصيص والتوسيع
- يتبع معايير إمكانية الوصول

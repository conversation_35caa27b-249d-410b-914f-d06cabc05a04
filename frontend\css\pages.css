/**
 * Pages CSS - تنسيق الصفحات
 * يحتوي على أنماط خاصة بصفحات التطبيق المختلفة
 */

/* ===== صفحة لوحة التحكم ===== */
.dashboard {
    display: grid;
    gap: var(--spacing-lg);
}

.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.dashboard-charts {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.dashboard-tables {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
}

.dashboard-widget {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
}

.dashboard-widget-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-lg);
}

.dashboard-widget-title {
    font-size: var(--font-lg);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
}

.dashboard-widget-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
}

.quick-action-card {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    text-align: center;
    cursor: pointer;
    transition: all var(--transition-normal);
    text-decoration: none;
    color: var(--text-primary);
}

.quick-action-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
    border-color: var(--primary-color);
}

.quick-action-icon {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

.quick-action-title {
    font-size: var(--font-lg);
    font-weight: var(--font-semibold);
    margin-bottom: var(--spacing-sm);
}

.quick-action-description {
    font-size: var(--font-sm);
    color: var(--text-secondary);
}

/* ===== صفحة المركبات ===== */
.vehicles-page {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.vehicles-filters {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
}

.vehicles-filters-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
    align-items: end;
}

.vehicles-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: var(--spacing-lg);
}

.vehicle-card {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
}

.vehicle-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.vehicle-card-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.vehicle-card-title {
    font-size: var(--font-lg);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
}

.vehicle-card-status {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    font-size: var(--font-xs);
    font-weight: var(--font-medium);
    text-transform: uppercase;
}

.vehicle-card-status.active {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.vehicle-card-status.maintenance {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.vehicle-card-status.inactive {
    background: rgba(239, 68, 68, 0.1);
    color: var(--error-color);
}

.vehicle-card-body {
    padding: var(--spacing-lg);
}

.vehicle-card-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.vehicle-card-info-item {
    display: flex;
    flex-direction: column;
}

.vehicle-card-info-label {
    font-size: var(--font-xs);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
}

.vehicle-card-info-value {
    font-size: var(--font-sm);
    font-weight: var(--font-medium);
    color: var(--text-primary);
}

.vehicle-card-actions {
    display: flex;
    gap: var(--spacing-sm);
    justify-content: flex-end;
}

/* ===== صفحة السائقين ===== */
.drivers-page {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.driver-card {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
}

.driver-card:hover {
    box-shadow: var(--shadow-md);
}

.driver-card-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.driver-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--primary-color);
    color: var(--text-light);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-xl);
    font-weight: var(--font-bold);
}

.driver-info {
    flex: 1;
}

.driver-name {
    font-size: var(--font-lg);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.driver-id {
    font-size: var(--font-sm);
    color: var(--text-secondary);
}

.driver-status {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    font-size: var(--font-xs);
    font-weight: var(--font-medium);
}

.driver-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
}

/* ===== صفحة الصيانة ===== */
.maintenance-page {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.maintenance-calendar {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
}

.maintenance-timeline {
    position: relative;
    padding-right: var(--spacing-xl);
}

.maintenance-timeline::before {
    content: '';
    position: absolute;
    right: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: var(--border-color);
}

.maintenance-timeline-item {
    position: relative;
    margin-bottom: var(--spacing-xl);
}

.maintenance-timeline-item::before {
    content: '';
    position: absolute;
    right: 7px;
    top: 8px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: var(--bg-primary);
    border: 3px solid var(--primary-color);
}

.maintenance-timeline-content {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    margin-right: var(--spacing-xl);
}

.maintenance-timeline-date {
    font-size: var(--font-sm);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
}

.maintenance-timeline-title {
    font-size: var(--font-lg);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.maintenance-timeline-description {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
}

.maintenance-timeline-meta {
    display: flex;
    gap: var(--spacing-md);
    font-size: var(--font-sm);
    color: var(--text-muted);
}

/* ===== صفحة التقارير ===== */
.reports-page {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.reports-filters {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
}

.reports-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
}

.report-card {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    cursor: pointer;
    transition: all var(--transition-normal);
}

.report-card:hover {
    box-shadow: var(--shadow-md);
    border-color: var(--primary-color);
}

.report-card-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-lg);
    background: var(--primary-color);
    color: var(--text-light);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-xl);
    margin-bottom: var(--spacing-md);
}

.report-card-title {
    font-size: var(--font-lg);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.report-card-description {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
}

.report-card-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: var(--font-sm);
    color: var(--text-muted);
}

/* ===== صفحة تسجيل الدخول ===== */
.login-page {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    padding: var(--spacing-lg);
}

.login-container {
    width: 100%;
    max-width: 400px;
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    overflow: hidden;
}

.login-header {
    text-align: center;
    padding: var(--spacing-2xl) var(--spacing-lg) var(--spacing-lg);
    background: var(--bg-secondary);
}

.login-logo {
    width: 80px;
    height: 80px;
    margin: 0 auto var(--spacing-lg);
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: var(--text-light);
}

.login-title {
    font-size: var(--font-2xl);
    font-weight: var(--font-bold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.login-subtitle {
    color: var(--text-secondary);
}

.login-form {
    padding: var(--spacing-lg);
}

.login-form .form-group {
    margin-bottom: var(--spacing-lg);
}

.login-form .btn {
    width: 100%;
    padding: var(--spacing-md);
    font-size: var(--font-base);
}

.login-footer {
    text-align: center;
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
    background: var(--bg-secondary);
}

.login-footer a {
    color: var(--primary-color);
    text-decoration: none;
}

.login-footer a:hover {
    text-decoration: underline;
}

/* ===== صفحة الإعدادات ===== */
.settings-page {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.settings-nav {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-md);
}

.settings-nav-list {
    display: flex;
    gap: var(--spacing-sm);
    list-style: none;
    margin: 0;
    padding: 0;
}

.settings-nav-item {
    flex: 1;
}

.settings-nav-link {
    display: block;
    padding: var(--spacing-md);
    text-align: center;
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.settings-nav-link:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

.settings-nav-link.active {
    background: var(--primary-color);
    color: var(--text-light);
}

.settings-content {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
}

.settings-section {
    margin-bottom: var(--spacing-xl);
}

.settings-section:last-child {
    margin-bottom: 0;
}

.settings-section-title {
    font-size: var(--font-lg);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-color);
}

.settings-section-description {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
}

.settings-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md) 0;
    border-bottom: 1px solid var(--border-light);
}

.settings-row:last-child {
    border-bottom: none;
}

.settings-row-info {
    flex: 1;
}

.settings-row-title {
    font-weight: var(--font-medium);
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.settings-row-description {
    font-size: var(--font-sm);
    color: var(--text-secondary);
}

.settings-row-control {
    margin-right: var(--spacing-md);
}

/* ===== الاستجابة للصفحات ===== */
@media (max-width: 1024px) {
    .dashboard-charts {
        grid-template-columns: 1fr;
    }
    
    .dashboard-tables {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .dashboard-stats {
        grid-template-columns: 1fr;
    }
    
    .vehicles-grid {
        grid-template-columns: 1fr;
    }
    
    .vehicles-filters-row {
        grid-template-columns: 1fr;
    }
    
    .driver-details {
        grid-template-columns: 1fr;
    }
    
    .reports-grid {
        grid-template-columns: 1fr;
    }
    
    .settings-nav-list {
        flex-direction: column;
    }
    
    .settings-row {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-md);
    }
    
    .settings-row-control {
        margin-right: 0;
        width: 100%;
    }
}

@media (max-width: 576px) {
    .quick-actions {
        grid-template-columns: 1fr;
    }
    
    .vehicle-card-info {
        grid-template-columns: 1fr;
    }
    
    .vehicle-card-actions {
        flex-direction: column;
    }
    
    .maintenance-timeline {
        padding-right: var(--spacing-md);
    }
    
    .maintenance-timeline-content {
        margin-right: var(--spacing-md);
    }
    
    .login-page {
        padding: var(--spacing-md);
    }
}

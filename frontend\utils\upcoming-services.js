/**
 * Upcoming Services - حساب الخدمات القادمة
 * يوفر دوال لحساب وإدارة الخدمات القادمة للمركبات
 */

import { SERVICE_TYPES, SERVICE_TYPE_LABELS, ALERT_THRESHOLDS, SERVICE_PRIORITY } from './constants.js';
import { getBranchName, formatDate, getDaysDifference } from './utility.js';

/**
 * الحصول على الخدمات القادمة لجميع المركبات
 */
export function getUpcomingServices(vehicles, selectedBranch = 'all') {
    const services = [];
    
    // تصفية المركبات حسب الفرع
    const filteredVehicles = selectedBranch === 'all' 
        ? vehicles 
        : vehicles.filter(v => getBranchName(v).toLowerCase() === selectedBranch.toLowerCase());
    
    // معالجة كل مركبة للحصول على خدماتها القادمة
    filteredVehicles.forEach(vehicle => {
        // إضافة خدمة الصيانة
        const maintenanceService = getMaintenanceService(vehicle);
        if (maintenanceService) {
            services.push(maintenanceService);
        }
        
        // إضافة خدمة تغيير الإطارات
        const tireService = getTireService(vehicle);
        if (tireService) {
            services.push(tireService);
        }
        
        // إضافة خدمة تجديد الرخصة
        const licenseService = getLicenseService(vehicle);
        if (licenseService) {
            services.push(licenseService);
        }
    });
    
    // ترتيب الخدمات حسب الأولوية والوقت المتبقي
    return sortServices(services);
}

/**
 * الحصول على خدمة الصيانة للمركبة
 */
function getMaintenanceService(vehicle) {
    const kmToMaintenance = parseInt(String(vehicle['Km to next maintenance'] || '0').replace(/,/g, '')) || 0;
    
    if (isNaN(kmToMaintenance)) return null;
    
    const statusInfo = getServiceStatus(kmToMaintenance, SERVICE_TYPES.MAINTENANCE);
    
    // إظهار الخدمة فقط إذا كانت مطلوبة أو قادمة
    if (statusInfo.priority === SERVICE_PRIORITY.LOW) return null;
    
    return {
        vehicle: {
            id: vehicle['Vehicle ID'] || vehicle.id,
            plate: vehicle['License Plate'],
            branch: getBranchName(vehicle)
        },
        type: SERVICE_TYPES.MAINTENANCE,
        remaining: kmToMaintenance,
        unit: 'km',
        statusInfo: statusInfo,
        expectedDate: calculateExpectedDate(vehicle, kmToMaintenance, SERVICE_TYPES.MAINTENANCE)
    };
}

/**
 * الحصول على خدمة تغيير الإطارات للمركبة
 */
function getTireService(vehicle) {
    const kmToTires = parseInt(String(vehicle['Km left for tire change'] || '0').replace(/,/g, '')) || 0;
    
    if (isNaN(kmToTires)) return null;
    
    const statusInfo = getServiceStatus(kmToTires, SERVICE_TYPES.TIRE_CHANGE);
    
    // إظهار الخدمة فقط إذا كانت مطلوبة أو قادمة
    if (statusInfo.priority === SERVICE_PRIORITY.LOW) return null;
    
    return {
        vehicle: {
            id: vehicle['Vehicle ID'] || vehicle.id,
            plate: vehicle['License Plate'],
            branch: getBranchName(vehicle)
        },
        type: SERVICE_TYPES.TIRE_CHANGE,
        remaining: kmToTires,
        unit: 'km',
        statusInfo: statusInfo,
        expectedDate: calculateExpectedDate(vehicle, kmToTires, SERVICE_TYPES.TIRE_CHANGE)
    };
}

/**
 * الحصول على خدمة تجديد الرخصة للمركبة
 */
function getLicenseService(vehicle) {
    const daysToLicense = parseInt(vehicle['Days to renew license'] || '0');
    
    if (isNaN(daysToLicense)) return null;
    
    const statusInfo = getServiceStatus(daysToLicense, SERVICE_TYPES.LICENSE_RENEWAL);
    
    // إظهار الخدمة فقط إذا كانت مطلوبة أو قادمة
    if (statusInfo.priority === SERVICE_PRIORITY.LOW) return null;
    
    return {
        vehicle: {
            id: vehicle['Vehicle ID'] || vehicle.id,
            plate: vehicle['License Plate'],
            branch: getBranchName(vehicle)
        },
        type: SERVICE_TYPES.LICENSE_RENEWAL,
        remaining: daysToLicense,
        unit: 'days',
        statusInfo: statusInfo,
        expectedDate: vehicle['License Renewal Date'] || calculateExpectedDate(vehicle, daysToLicense, SERVICE_TYPES.LICENSE_RENEWAL)
    };
}

/**
 * الحصول على حالة الخدمة وأولويتها
 */
function getServiceStatus(remaining, serviceType) {
    const statusInfo = {
        priority: SERVICE_PRIORITY.LOW,
        class: 'good',
        icon: 'check-circle',
        text: 'جيد',
        tooltip: 'الخدمة في حالة جيدة',
        color: '#22c55e'
    };
    
    // تحديد الحدود حسب نوع الخدمة
    let criticalThreshold, warningThreshold;
    
    if (serviceType === SERVICE_TYPES.LICENSE_RENEWAL) {
        criticalThreshold = ALERT_THRESHOLDS.CRITICAL_DAYS;
        warningThreshold = ALERT_THRESHOLDS.LICENSE_RENEWAL_DAYS;
    } else {
        criticalThreshold = ALERT_THRESHOLDS.CRITICAL_KM;
        warningThreshold = serviceType === SERVICE_TYPES.MAINTENANCE 
            ? ALERT_THRESHOLDS.MAINTENANCE_KM 
            : ALERT_THRESHOLDS.TIRE_CHANGE_KM;
    }
    
    // تحديد الحالة والأولوية
    if (remaining <= 0) {
        statusInfo.priority = SERVICE_PRIORITY.CRITICAL;
        statusInfo.class = 'critical';
        statusInfo.icon = 'exclamation-triangle';
        statusInfo.text = 'متأخر';
        statusInfo.tooltip = 'الخدمة متأخرة ومطلوبة فوراً';
        statusInfo.color = '#dc3545';
    } else if (remaining <= criticalThreshold) {
        statusInfo.priority = SERVICE_PRIORITY.CRITICAL;
        statusInfo.class = 'critical';
        statusInfo.icon = 'exclamation-circle';
        statusInfo.text = 'مطلوب فوراً';
        statusInfo.tooltip = 'الخدمة مطلوبة بشكل عاجل';
        statusInfo.color = '#dc3545';
    } else if (remaining <= warningThreshold) {
        statusInfo.priority = SERVICE_PRIORITY.HIGH;
        statusInfo.class = 'warning';
        statusInfo.icon = 'exclamation';
        statusInfo.text = 'قادم';
        statusInfo.tooltip = 'الخدمة مطلوبة قريباً';
        statusInfo.color = '#ffc107';
    }
    
    return statusInfo;
}

/**
 * ترتيب الخدمات حسب الأولوية والوقت المتبقي
 */
function sortServices(services) {
    return services.sort((a, b) => {
        // ترتيب حسب الأولوية أولاً
        const priorityOrder = {
            [SERVICE_PRIORITY.CRITICAL]: 0,
            [SERVICE_PRIORITY.HIGH]: 1,
            [SERVICE_PRIORITY.MEDIUM]: 2,
            [SERVICE_PRIORITY.LOW]: 3
        };
        
        if (priorityOrder[a.statusInfo.priority] !== priorityOrder[b.statusInfo.priority]) {
            return priorityOrder[a.statusInfo.priority] - priorityOrder[b.statusInfo.priority];
        }
        
        // ثم حسب الوقت المتبقي
        return a.remaining - b.remaining;
    });
}

/**
 * تصفية الخدمات حسب النوع أو الحالة
 */
export function filterServices(services, filter) {
    switch (filter) {
        case 'all':
            return services;
        case 'critical':
            return services.filter(service => service.statusInfo.priority === SERVICE_PRIORITY.CRITICAL);
        case 'upcoming':
            return services.filter(service => service.statusInfo.priority === SERVICE_PRIORITY.HIGH);
        case 'maintenance':
            return services.filter(service => service.type === SERVICE_TYPES.MAINTENANCE);
        case 'tires':
            return services.filter(service => service.type === SERVICE_TYPES.TIRE_CHANGE);
        case 'license':
            return services.filter(service => service.type === SERVICE_TYPES.LICENSE_RENEWAL);
        default:
            return services;
    }
}

/**
 * تنسيق الوقت/المسافة المتبقية للخدمة
 */
export function formatServiceRemaining(service) {
    const remaining = service.remaining;
    
    if (remaining === undefined || remaining === null) {
        return 'غير محدد';
    }
    
    // التعامل مع القيم السالبة (متأخر)
    if (remaining < 0) {
        const absRemaining = Math.abs(remaining);
        if (service.unit === 'days') {
            return `<span class="overdue-value">متأخر ${absRemaining} يوم</span>`;
        } else {
            return `<span class="overdue-value">متأخر ${absRemaining.toLocaleString('ar-SA')} كم</span>`;
        }
    }
    
    // القيم الموجبة
    if (service.unit === 'days') {
        return `${remaining} يوم`;
    } else {
        return `${remaining.toLocaleString('ar-SA')} كم`;
    }
}

/**
 * حساب التاريخ المتوقع للخدمة
 */
function calculateExpectedDate(vehicle, remaining, serviceType) {
    const currentDate = new Date();
    
    if (serviceType === SERVICE_TYPES.LICENSE_RENEWAL) {
        // للرخصة، استخدام الأيام مباشرة
        const expectedDate = new Date(currentDate);
        expectedDate.setDate(currentDate.getDate() + remaining);
        return formatDate(expectedDate);
    } else {
        // للصيانة والإطارات، حساب الأيام بناءً على الاستخدام اليومي
        const dailyUsage = 50; // الاستخدام اليومي الافتراضي بالكيلومتر
        const daysRemaining = Math.max(0, remaining) / dailyUsage;
        const expectedDate = new Date(currentDate);
        expectedDate.setDate(currentDate.getDate() + Math.round(daysRemaining));
        return formatDate(expectedDate);
    }
}

/**
 * الحصول على تسمية نوع الخدمة
 */
export function getServiceTypeLabel(type) {
    return SERVICE_TYPE_LABELS[type] || type;
}

/**
 * الحصول على إحصائيات الخدمات القادمة
 */
export function getServicesStats(services) {
    const stats = {
        total: services.length,
        critical: 0,
        upcoming: 0,
        maintenance: 0,
        tires: 0,
        license: 0
    };
    
    services.forEach(service => {
        // إحصائيات الأولوية
        if (service.statusInfo.priority === SERVICE_PRIORITY.CRITICAL) {
            stats.critical++;
        } else if (service.statusInfo.priority === SERVICE_PRIORITY.HIGH) {
            stats.upcoming++;
        }
        
        // إحصائيات النوع
        if (service.type === SERVICE_TYPES.MAINTENANCE) {
            stats.maintenance++;
        } else if (service.type === SERVICE_TYPES.TIRE_CHANGE) {
            stats.tires++;
        } else if (service.type === SERVICE_TYPES.LICENSE_RENEWAL) {
            stats.license++;
        }
    });
    
    return stats;
}

/**
 * البحث في الخدمات القادمة
 */
export function searchServices(services, query) {
    if (!query) return services;
    
    const searchTerm = query.toLowerCase();
    
    return services.filter(service => 
        service.vehicle.plate?.toLowerCase().includes(searchTerm) ||
        service.vehicle.branch?.toLowerCase().includes(searchTerm) ||
        getServiceTypeLabel(service.type).toLowerCase().includes(searchTerm)
    );
}

export default {
    getUpcomingServices,
    filterServices,
    formatServiceRemaining,
    getServiceTypeLabel,
    getServicesStats,
    searchServices
};

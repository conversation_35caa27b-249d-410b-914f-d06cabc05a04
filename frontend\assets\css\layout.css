/**
 * Layout CSS - تخطيط الصفحة
 * يحتوي على تنسيقات التخطيط العام للتطبيق
 */

/* App Container - حاوية التطبيق */
.app-container {
    display: flex;
    min-height: 100vh;
    background-color: #fafafa;
}

/* Login Page Layout - تخطيط صفحة تسجيل الدخول */
body.login-page {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    padding: 1rem;
}

.login-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    max-width: 400px;
    animation: fadeIn 0.6s ease-out;
}

.login-box {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    width: 100%;
    margin-top: 1rem;
}

.login-box h2 {
    text-align: center;
    margin-bottom: 1.5rem;
    color: var(--text-color-dark);
    font-weight: 600;
}

.input-container {
    position: relative;
    margin-bottom: 1rem;
}

.input-container .icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-color-light);
    z-index: 1;
}

.input-container input {
    width: 100%;
    padding: 1rem 1rem 1rem 3rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: all var(--transition-speed);
    background: rgba(255, 255, 255, 0.9);
}

.input-container input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.1);
    background: white;
}

.error-message {
    color: var(--danger-color);
    font-size: 0.875rem;
    margin-top: 0.5rem;
    text-align: center;
    padding: 0.5rem;
    background: rgba(var(--danger-color-rgb), 0.1);
    border-radius: var(--border-radius);
}

/* Header - الهيدر */
.header {
    text-align: center;
    margin-bottom: 2rem;
    color: white;
}

.header img {
    width: 120px;
    height: auto;
    margin-bottom: 1rem;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
}

.header h1 {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Sidebar - الشريط الجانبي */
.sidebar {
    width: var(--sidebar-width);
    background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
    color: white;
    display: flex;
    flex-direction: column;
    position: fixed;
    height: 100vh;
    left: 0;
    top: 0;
    z-index: 100;
    transition: transform var(--transition-speed);
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

.sidebar.collapsed {
    transform: translateX(-100%);
}

.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    text-align: center;
}

.sidebar-logo img {
    width: 80px;
    height: auto;
    margin-bottom: 1rem;
    filter: brightness(0) invert(1);
}

.user-info {
    font-size: 0.875rem;
    opacity: 0.8;
}

.sidebar-nav {
    flex: 1;
    padding: 1rem 0;
}

.sidebar-nav ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sidebar-nav li {
    margin-bottom: 0.25rem;
}

.sidebar-nav a {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1.5rem;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all var(--transition-speed);
    border-left: 3px solid transparent;
}

.sidebar-nav a:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border-left-color: var(--primary-color);
}

.sidebar-nav a.active {
    background: rgba(var(--primary-color-rgb), 0.2);
    color: white;
    border-left-color: var(--primary-color);
}

.sidebar-nav i {
    width: 1.25rem;
    text-align: center;
    font-size: 1rem;
}

.sidebar-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Main Content - المحتوى الرئيسي */
.main-content {
    flex: 1;
    margin-left: var(--sidebar-width);
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    transition: margin-left var(--transition-speed);
}

.main-content.sidebar-collapsed {
    margin-left: 0;
}

/* Main Header - هيدر المحتوى الرئيسي */
.main-header {
    height: var(--header-height);
    background: white;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    position: sticky;
    top: 0;
    z-index: 50;
}

.toggle-sidebar {
    background: none;
    border: none;
    font-size: 1.25rem;
    cursor: pointer;
    color: var(--text-color);
    padding: 0.5rem;
    border-radius: var(--border-radius);
    transition: all var(--transition-speed);
}

.toggle-sidebar:hover {
    background: var(--hover-color);
    color: var(--primary-color);
}

.main-header h2 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-color-dark);
}

.user-menu {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-size: 0.875rem;
    color: var(--text-color-light);
}

.dark-mode-toggle {
    background: none;
    border: none;
    font-size: 1.125rem;
    cursor: pointer;
    color: var(--text-color);
    padding: 0.5rem;
    border-radius: var(--border-radius);
    transition: all var(--transition-speed);
}

.dark-mode-toggle:hover {
    background: var(--hover-color);
    color: var(--primary-color);
}

/* Page Content - محتوى الصفحة */
.page-content {
    flex: 1;
    padding: 1.5rem;
    background: #fafafa;
    position: relative;
}

.page {
    display: none;
    animation: fadeIn 0.3s ease-out;
}

.page.active {
    display: block;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    gap: 1rem;
}

.page-title {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-color-dark);
}

.page-actions {
    display: flex;
    gap: 0.75rem;
    align-items: center;
}

/* Section Styling - تنسيق الأقسام */
.section {
    background: var(--card-bg-color);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-bottom: 1.5rem;
    overflow: hidden;
}

.section-header {
    padding: 1rem 1.5rem;
    background: #f8f9fa;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.section-title {
    margin: 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-color-dark);
}

.section-body {
    padding: 1.5rem;
}

/* Table Responsive - جداول متجاوبة */
.table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    margin: -1px;
}

.table-responsive .table {
    margin-bottom: 0;
    min-width: 600px;
}

/* Filter Bar - شريط التصفية */
.filter-bar {
    background: var(--card-bg-color);
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-bottom: 1.5rem;
    box-shadow: var(--box-shadow);
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    min-width: 150px;
}

.filter-group label {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-color-dark);
}

.filter-group select,
.filter-group input {
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 0.875rem;
}

/* Status Indicators - مؤشرات الحالة */
.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.875rem;
    font-weight: 500;
}

.status-indicator.active {
    background: rgba(var(--success-color-rgb), 0.1);
    color: var(--success-color);
}

.status-indicator.inactive {
    background: rgba(var(--danger-color-rgb), 0.1);
    color: var(--danger-color);
}

.status-indicator.maintenance {
    background: rgba(var(--warning-color-rgb), 0.1);
    color: var(--warning-color);
}

.status-indicator::before {
    content: '';
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: currentColor;
}

/* Loading States - حالات التحميل */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

/* Empty States - حالات فارغة */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--text-color-light);
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state h3 {
    margin: 0 0 0.5rem 0;
    font-weight: 500;
}

.empty-state p {
    margin: 0;
    font-size: 0.875rem;
}

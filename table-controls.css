/* Table Controls Styles */

/* Main container for table controls */
.table-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    flex-wrap: wrap;
    gap: 1rem;
    background-color: white;
    border-radius: 8px;
    padding: 1rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 87, 255, 0.08);
}

/* Service type filter container */
.service-type-filter {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    flex: 1;
}

/* Control buttons */
.control-btn {
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    background-color: #f5f7fa;
    color: #64748b;
    border: 1px solid #e2e8f0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    height: 36px; /* Fixed height for all buttons */
    box-sizing: border-box;
}

.control-btn:hover {
    background-color: #e2e8f0;
    color: #334155;
}

.control-btn.active {
    background-color: rgba(0, 87, 255, 0.1);
    color: #0057ff;
    border-color: rgba(0, 87, 255, 0.2);
    font-weight: 600;
}

/* Export button */
.export-btn {
    background-color: #00c853;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    box-shadow: none;
    margin-right: 0.5rem;
    font-size: 0.9rem;
    height: 36px;
    box-sizing: border-box;
}

.export-btn:hover {
    background-color: #00b248;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 200, 83, 0.3);
    color: white;
}

.export-btn i {
    font-size: 0.9rem;
}

/* When export button is inside service-type-filter */
.service-type-filter .export-btn {
    margin-left: 0.5rem;
    margin-right: 0;
    height: 36px; /* Same fixed height as other buttons */
    line-height: normal;
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
    font-weight: 500;
    box-shadow: none;
    width: auto;
    min-height: auto;
}

/* Responsive styles */
@media (max-width: 768px) {
    .table-controls {
        flex-direction: column;
        align-items: flex-start;
    }

    .service-type-filter {
        width: 100%;
        overflow-x: auto;
        padding-bottom: 0.5rem;
        flex-wrap: nowrap;
    }

    .service-type-filter::-webkit-scrollbar {
        height: 4px;
    }

    .service-type-filter::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 4px;
    }

    .service-type-filter::-webkit-scrollbar-thumb {
        background: #cbd5e1;
        border-radius: 4px;
    }

    .export-btn {
        align-self: flex-end;
    }
}

/* Dark mode support */
body.dark-mode .table-controls {
    background: linear-gradient(145deg, #111b30, #162341);
    border: 1px solid rgba(0, 87, 255, 0.1);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 5px 15px rgba(0, 0, 0, 0.1);
}

body.dark-mode .control-btn {
    background-color: #1e293b;
    color: #94a3b8;
    border-color: #334155;
}

body.dark-mode .control-btn:hover {
    background-color: #334155;
    color: #e2e8f0;
}

body.dark-mode .control-btn.active {
    background-color: rgba(0, 87, 255, 0.15);
    color: #4d84ff;
    border-color: rgba(77, 132, 255, 0.3);
}

body.dark-mode .export-btn {
    background-color: #00a047;
    box-shadow: 0 2px 4px rgba(0, 160, 71, 0.3);
}

body.dark-mode .export-btn:hover {
    background-color: #00b248;
    box-shadow: 0 4px 8px rgba(0, 160, 71, 0.4);
}

/* Maintenance controls styles */
.maintenance-controls-wrapper {
    width: 100%;
}

.maintenance-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 1rem;
    width: 100%;
}

.control-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.control-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
    color: #64748b;
    font-size: 0.9rem;
}

.control-label i {
    color: #0057ff;
}

.search-group {
    flex: 2;
}

.filter-group {
    flex: 1;
}

.export-group {
    flex: 0 0 auto;
    align-self: flex-end;
}

.search-box {
    display: flex;
    width: 100%;
}

.search-box input {
    flex: 1;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-right: none;
}

.search-box button {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

.filter-box {
    width: 100%;
}

.active-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    align-items: center;
    margin-bottom: 1rem;
}

.filter-label {
    font-size: 0.9rem;
    color: #64748b;
    font-weight: 500;
}

.filter-tag {
    display: inline-flex;
    align-items: center;
    background-color: rgba(0, 87, 255, 0.1);
    color: #0057ff;
    padding: 0.25rem 0.75rem;
    border-radius: 50px;
    font-size: 0.85rem;
    gap: 0.5rem;
}

.filter-tag .filter-value {
    font-weight: 600;
}

.clear-filter {
    background: none;
    border: none;
    color: #0057ff;
    cursor: pointer;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.clear-filter:hover {
    background-color: rgba(0, 87, 255, 0.2);
}

/* Dark mode for maintenance controls */
body.dark-mode .control-label {
    color: #94a3b8;
}

body.dark-mode .filter-label {
    color: #94a3b8;
}

body.dark-mode .filter-tag {
    background-color: rgba(77, 132, 255, 0.15);
    color: #4d84ff;
}

body.dark-mode .clear-filter {
    color: #4d84ff;
}

body.dark-mode .clear-filter:hover {
    background-color: rgba(77, 132, 255, 0.25);
}

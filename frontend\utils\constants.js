/**
 * Constants - الثوابت العامة للتطبيق
 * يحتوي على جميع الثوابت المستخدمة في التطبيق
 */

// إعدادات API
export const API_CONFIG = {
    BASE_URL: 'https://script.google.com/macros/s/AKfycbyvgby4dtYeqahohK7loTDqU4ek3yYBi_8J_J5c5CBF4aBF0tWflyiKaC0AJK1xy-oW/exec',
    TIMEOUT: 30000,
    RETRY_ATTEMPTS: 3,
    RETRY_DELAY: 1000
};

// أسماء الأعمدة في لوحة التحكم
export const DASHBOARD_COLUMNS = {
    VEHICLE_ID: 'Vehicle ID',
    LICENSE_PLATE: 'License Plate',
    SERVICE_TYPE: 'Service Type',
    VEHICLE_TYPE: 'Vehicle Type',
    MODEL: 'Model',
    COLOR: 'Color',
    VIN_NUMBER: 'VIN Number',
    FUEL_TYPE: 'Fuel Type',
    CURRENT_KM: 'Current Km',
    VEHICLE_STATUS: 'Vehicle Status',
    INACTIVE: 'Inactive',
    LAST_MAINTENANCE_KM: 'Last Maintenance Km',
    LAST_MAINTENANCE_DATE: 'Last Maintenance Date',
    NEXT_MAINTENANCE_KM: 'Next Maintenance Km',
    KM_TO_MAINTENANCE: 'Km to next maintenance',
    LAST_TIRE_CHANGE_KM: 'Last tire change Km',
    LAST_TIRE_CHANGE_DATE: 'Last tire change Data',
    NEXT_TIRE_CHANGE_KM: 'Next Tire Change Km',
    KM_TO_TIRE_CHANGE: 'Km left for tire change',
    LICENSE_RENEWAL_DATE: 'License Renewal Date',
    DAYS_TO_LICENSE: 'Days to renew license',
    INSURANCE_EXPIRY: 'Insurance Expiry Date',
    BRANCH: 'Branch',
    CURRENT_LOCATION: 'Current Location',
    DRIVER_NAME: 'Driver Name',
    DRIVER_CONTACT: 'Driver Contact',
    NOTES: 'Notes'
};

// حالات المركبات
export const VEHICLE_STATUS = {
    ACTIVE: 'active',
    INACTIVE: 'inactive',
    MAINTENANCE: 'maintenance',
    REPAIR: 'repair',
    SOLD: 'sold'
};

// ترجمة حالات المركبات
export const VEHICLE_STATUS_LABELS = {
    [VEHICLE_STATUS.ACTIVE]: 'نشط',
    [VEHICLE_STATUS.INACTIVE]: 'متوقف',
    [VEHICLE_STATUS.MAINTENANCE]: 'في الصيانة',
    [VEHICLE_STATUS.REPAIR]: 'في الإصلاح',
    [VEHICLE_STATUS.SOLD]: 'مباع'
};

// أنواع الخدمات
export const SERVICE_TYPES = {
    MAINTENANCE: 'maintenance',
    TIRE_CHANGE: 'tire_change',
    OIL_CHANGE: 'oil_change',
    BRAKE_SERVICE: 'brake_service',
    ENGINE_SERVICE: 'engine_service',
    TRANSMISSION_SERVICE: 'transmission_service',
    ELECTRICAL_SERVICE: 'electrical_service',
    AC_SERVICE: 'ac_service',
    BODY_WORK: 'body_work',
    LICENSE_RENEWAL: 'license_renewal',
    INSURANCE_RENEWAL: 'insurance_renewal'
};

// ترجمة أنواع الخدمات
export const SERVICE_TYPE_LABELS = {
    [SERVICE_TYPES.MAINTENANCE]: 'صيانة دورية',
    [SERVICE_TYPES.TIRE_CHANGE]: 'تغيير إطارات',
    [SERVICE_TYPES.OIL_CHANGE]: 'تغيير زيت',
    [SERVICE_TYPES.BRAKE_SERVICE]: 'خدمة الفرامل',
    [SERVICE_TYPES.ENGINE_SERVICE]: 'خدمة المحرك',
    [SERVICE_TYPES.TRANSMISSION_SERVICE]: 'خدمة ناقل الحركة',
    [SERVICE_TYPES.ELECTRICAL_SERVICE]: 'خدمة كهربائية',
    [SERVICE_TYPES.AC_SERVICE]: 'خدمة التكييف',
    [SERVICE_TYPES.BODY_WORK]: 'أعمال الهيكل',
    [SERVICE_TYPES.LICENSE_RENEWAL]: 'تجديد الرخصة',
    [SERVICE_TYPES.INSURANCE_RENEWAL]: 'تجديد التأمين'
};

// أنواع الوقود
export const FUEL_TYPES = {
    GASOLINE: 'gasoline',
    DIESEL: 'diesel',
    HYBRID: 'hybrid',
    ELECTRIC: 'electric',
    CNG: 'cng',
    LPG: 'lpg'
};

// ترجمة أنواع الوقود
export const FUEL_TYPE_LABELS = {
    [FUEL_TYPES.GASOLINE]: 'بنزين',
    [FUEL_TYPES.DIESEL]: 'ديزل',
    [FUEL_TYPES.HYBRID]: 'هجين',
    [FUEL_TYPES.ELECTRIC]: 'كهربائي',
    [FUEL_TYPES.CNG]: 'غاز طبيعي مضغوط',
    [FUEL_TYPES.LPG]: 'غاز البترول المسال'
};

// أنواع المركبات
export const VEHICLE_TYPES = {
    CAR: 'car',
    TRUCK: 'truck',
    VAN: 'van',
    BUS: 'bus',
    MOTORCYCLE: 'motorcycle',
    TRAILER: 'trailer',
    HEAVY_EQUIPMENT: 'heavy_equipment'
};

// ترجمة أنواع المركبات
export const VEHICLE_TYPE_LABELS = {
    [VEHICLE_TYPES.CAR]: 'سيارة',
    [VEHICLE_TYPES.TRUCK]: 'شاحنة',
    [VEHICLE_TYPES.VAN]: 'فان',
    [VEHICLE_TYPES.BUS]: 'حافلة',
    [VEHICLE_TYPES.MOTORCYCLE]: 'دراجة نارية',
    [VEHICLE_TYPES.TRAILER]: 'مقطورة',
    [VEHICLE_TYPES.HEAVY_EQUIPMENT]: 'معدات ثقيلة'
};

// الفروع
export const BRANCHES = {
    RIYADH: 'riyadh',
    JEDDAH: 'jeddah',
    DAMMAM: 'dammam',
    MECCA: 'mecca',
    MEDINA: 'medina',
    TAIF: 'taif',
    KHOBAR: 'khobar',
    JUBAIL: 'jubail'
};

// ترجمة الفروع
export const BRANCH_LABELS = {
    [BRANCHES.RIYADH]: 'الرياض',
    [BRANCHES.JEDDAH]: 'جدة',
    [BRANCHES.DAMMAM]: 'الدمام',
    [BRANCHES.MECCA]: 'مكة المكرمة',
    [BRANCHES.MEDINA]: 'المدينة المنورة',
    [BRANCHES.TAIF]: 'الطائف',
    [BRANCHES.KHOBAR]: 'الخبر',
    [BRANCHES.JUBAIL]: 'الجبيل'
};

// أولويات الخدمة
export const SERVICE_PRIORITY = {
    LOW: 'low',
    MEDIUM: 'medium',
    HIGH: 'high',
    CRITICAL: 'critical'
};

// ترجمة أولويات الخدمة
export const SERVICE_PRIORITY_LABELS = {
    [SERVICE_PRIORITY.LOW]: 'منخفضة',
    [SERVICE_PRIORITY.MEDIUM]: 'متوسطة',
    [SERVICE_PRIORITY.HIGH]: 'عالية',
    [SERVICE_PRIORITY.CRITICAL]: 'حرجة'
};

// ألوان الأولويات
export const SERVICE_PRIORITY_COLORS = {
    [SERVICE_PRIORITY.LOW]: '#28a745',
    [SERVICE_PRIORITY.MEDIUM]: '#ffc107',
    [SERVICE_PRIORITY.HIGH]: '#fd7e14',
    [SERVICE_PRIORITY.CRITICAL]: '#dc3545'
};

// حدود التنبيهات (بالكيلومتر أو الأيام)
export const ALERT_THRESHOLDS = {
    MAINTENANCE_KM: 1000,
    TIRE_CHANGE_KM: 500,
    LICENSE_RENEWAL_DAYS: 30,
    INSURANCE_EXPIRY_DAYS: 30,
    CRITICAL_KM: 100,
    CRITICAL_DAYS: 7
};

// إعدادات الصفحات
export const PAGINATION = {
    DEFAULT_PAGE_SIZE: 10,
    PAGE_SIZE_OPTIONS: [10, 25, 50, 100],
    MAX_VISIBLE_PAGES: 5
};

// إعدادات التاريخ والوقت
export const DATE_FORMATS = {
    DISPLAY: 'DD/MM/YYYY',
    API: 'YYYY-MM-DD',
    DATETIME: 'DD/MM/YYYY HH:mm',
    TIME: 'HH:mm'
};

// إعدادات التصدير
export const EXPORT_FORMATS = {
    PDF: 'pdf',
    EXCEL: 'excel',
    CSV: 'csv',
    HTML: 'html'
};

// أنواع الإشعارات
export const NOTIFICATION_TYPES = {
    SUCCESS: 'success',
    ERROR: 'error',
    WARNING: 'warning',
    INFO: 'info'
};

// مدة عرض الإشعارات (بالميلي ثانية)
export const NOTIFICATION_DURATION = {
    [NOTIFICATION_TYPES.SUCCESS]: 3000,
    [NOTIFICATION_TYPES.ERROR]: 5000,
    [NOTIFICATION_TYPES.WARNING]: 4000,
    [NOTIFICATION_TYPES.INFO]: 3000
};

// إعدادات التخزين المؤقت
export const CACHE_DURATION = {
    SHORT: 5 * 60 * 1000,      // 5 دقائق
    MEDIUM: 15 * 60 * 1000,    // 15 دقيقة
    LONG: 60 * 60 * 1000,      // ساعة واحدة
    VERY_LONG: 24 * 60 * 60 * 1000  // 24 ساعة
};

// مفاتيح التخزين المحلي
export const STORAGE_KEYS = {
    USER_SESSION: 'fleet_user_session',
    AUTH_TOKEN: 'fleet_auth_token',
    UI_SETTINGS: 'fleet_ui_settings',
    THEME: 'fleet_theme',
    LANGUAGE: 'fleet_language',
    COLUMN_SETTINGS: 'fleet_column_settings'
};

// الألوان الافتراضية للرسوم البيانية
export const CHART_COLORS = [
    '#0057ff',
    '#28a745',
    '#fd7e14',
    '#dc3545',
    '#6f42c1',
    '#17a2b8',
    '#ffc107',
    '#6c757d'
];

// إعدادات الملفات
export const FILE_SETTINGS = {
    MAX_SIZE: 5 * 1024 * 1024, // 5 ميجابايت
    ALLOWED_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'],
    ALLOWED_EXTENSIONS: ['.jpg', '.jpeg', '.png', '.gif', '.pdf']
};

export default {
    API_CONFIG,
    DASHBOARD_COLUMNS,
    VEHICLE_STATUS,
    VEHICLE_STATUS_LABELS,
    SERVICE_TYPES,
    SERVICE_TYPE_LABELS,
    FUEL_TYPES,
    FUEL_TYPE_LABELS,
    VEHICLE_TYPES,
    VEHICLE_TYPE_LABELS,
    BRANCHES,
    BRANCH_LABELS,
    SERVICE_PRIORITY,
    SERVICE_PRIORITY_LABELS,
    SERVICE_PRIORITY_COLORS,
    ALERT_THRESHOLDS,
    PAGINATION,
    DATE_FORMATS,
    EXPORT_FORMATS,
    NOTIFICATION_TYPES,
    NOTIFICATION_DURATION,
    CACHE_DURATION,
    STORAGE_KEYS,
    CHART_COLORS,
    FILE_SETTINGS
};

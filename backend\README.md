# Backend Directory

هذا المجلد مخصص للانتقال المستقبلي من Google Apps Script إلى Backend منفصل.

## الخطة المستقبلية:
- إنشاء API منفصل باستخدام Node.js/Express أو Python/FastAPI
- نقل منطق الأعمال من Google Apps Script
- إعداد قاعدة بيانات منفصلة
- تطبيق نظام المصادقة والتفويض

## الحالة الحالية:
- المجلد فارغ حالياً
- النظام يعتمد على Google Apps Script كـ Backend
- جميع العمليات تتم عبر API_URL المحدد في constants.js

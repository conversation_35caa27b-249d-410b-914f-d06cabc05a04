// Loading Spinner Functions

/**
 * Show the global loading spinner
 * @param {string} message - Optional message to display with the spinner
 */
export function showSpinner(message = 'Loading...') {
    // Check if spinner already exists
    let spinner = document.getElementById('global-spinner');

    if (!spinner) {
        // Create spinner container
        spinner = document.createElement('div');
        spinner.id = 'global-spinner';
        spinner.className = 'spinner-container';

        // Create spinner HTML
        spinner.innerHTML = `
            <div class="spinner">
                <div class="spinner-circle spinner-circle-primary"></div>
                <div class="spinner-circle spinner-circle-secondary"></div>
                <div class="spinner-circle spinner-circle-success"></div>
                <div class="spinner-logo"></div>
                <div class="spinner-text">${message}</div>
            </div>
        `;

        // Add to document
        document.body.appendChild(spinner);
    } else {
        // Update message if spinner already exists
        const textElement = spinner.querySelector('.spinner-text');
        if (textElement) {
            textElement.textContent = message;
        }

        // Make sure spinner is visible
        spinner.style.display = 'flex';
    }

    // Prevent scrolling while spinner is active
    document.body.style.overflow = 'hidden';
}

/**
 * Hide the global loading spinner
 */
export function hideSpinner() {
    const spinner = document.getElementById('global-spinner');

    if (spinner) {
        // Add fade-out effect
        spinner.style.opacity = '0';

        // Remove spinner after animation completes
        setTimeout(() => {
            spinner.style.display = 'none';
            spinner.style.opacity = '1';
            document.body.style.overflow = '';
        }, 300);
    }
}

/**
 * Create a section spinner
 * @param {HTMLElement} container - Container element to append the spinner to
 * @param {string} message - Optional message to display with the spinner
 * @returns {HTMLElement} - The created spinner element
 */
export function createSectionSpinner(container, message = 'Loading...') {
    // Create spinner wrapper
    const spinnerWrapper = document.createElement('div');
    spinnerWrapper.className = 'section-spinner';

    // Create spinner HTML
    spinnerWrapper.innerHTML = `
        <div class="spinner">
            <div class="spinner-circle spinner-circle-primary"></div>
            <div class="spinner-circle spinner-circle-secondary"></div>
            <div class="spinner-circle spinner-circle-success"></div>
            <div class="spinner-logo"></div>
            <div class="spinner-text">${message}</div>
        </div>
    `;

    // Add to container
    container.innerHTML = '';
    container.appendChild(spinnerWrapper);

    return spinnerWrapper;
}

/**
 * Create a table spinner
 * @param {HTMLElement} tableContainer - Table container element to append the spinner to
 * @param {string} message - Optional message to display with the spinner
 * @returns {HTMLElement} - The created spinner element
 */
export function createTableSpinner(tableContainer, message = 'Loading data...') {
    // Check if tableContainer exists
    if (!tableContainer) {
        console.error('Table container not found for spinner');
        return null;
    }

    // Create spinner wrapper
    const spinnerWrapper = document.createElement('div');
    spinnerWrapper.className = 'table-spinner';

    // Create spinner HTML
    spinnerWrapper.innerHTML = `
        <div class="spinner">
            <div class="spinner-circle spinner-circle-primary"></div>
            <div class="spinner-circle spinner-circle-secondary"></div>
            <div class="spinner-circle spinner-circle-success"></div>
            <div class="spinner-text">${message}</div>
        </div>
    `;

    // Add to container
    try {
        tableContainer.innerHTML = '';
        tableContainer.appendChild(spinnerWrapper);
    } catch (error) {
        console.error('Error adding spinner to container:', error);
        return null;
    }

    return spinnerWrapper;
}

/**
 * Add a spinner to a button
 * @param {HTMLButtonElement} button - Button element to add the spinner to
 * @param {boolean} isLoading - Whether the button is in loading state
 * @param {string} originalText - Original button text
 */
export function toggleButtonSpinner(button, isLoading, originalText = null) {
    if (!button) return;

    if (isLoading) {
        // Store original text if not provided
        if (!originalText) {
            button.setAttribute('data-original-text', button.innerHTML);
        }

        // Create spinner HTML
        const spinnerHTML = `
            <span class="btn-spinner">
                <div class="spinner-circle spinner-circle-primary"></div>
                <div class="spinner-circle spinner-circle-secondary"></div>
            </span>
        `;

        // Set button to loading state
        button.innerHTML = spinnerHTML + (originalText || 'Loading...');
        button.disabled = true;
    } else {
        // Restore original text
        const originalContent = originalText || button.getAttribute('data-original-text') || 'Done';
        button.innerHTML = originalContent;
        button.disabled = false;

        // Clean up data attribute
        button.removeAttribute('data-original-text');
    }
}

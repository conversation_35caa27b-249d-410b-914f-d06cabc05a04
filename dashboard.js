// Import required variables and functions
import { vehicles, drivers, maintenanceRecords, charts, openModal, showNotification, API_URL } from './script.js';
import { getServiceTypeLabel, formatServiceRemaining, filterServices, getUpcomingServices } from './upcoming-services.js';
import { initializeMaintenanceStats, calculateMaintenanceStats } from './maintenance-stats.js';
import { renderUpcomingServices } from './maintenance.js';
import { getBranchName } from './utility.js';

// Create branch filter component
function createBranchFilter() {
    // Validate vehicles data
    if (!Array.isArray(vehicles) || vehicles.length === 0) {
        console.error('No vehicles data available');
        return;
    }

    // Get unique branches from vehicles data
    const branches = [...new Set(vehicles.map(getBranchName))].sort();
    
    if (branches.length === 0) {
        console.error('No branches found in vehicles data');
        return;
    }
    
    // Create filter container
    const filterContainer = document.createElement('div');
    filterContainer.className = 'branch-filter';
    filterContainer.style.cssText = `
        margin: 20px 0;
        padding: 15px;
        background: var(--card-bg);
        border-radius: 8px;
        box-shadow: var(--card-shadow);
    `;

    // Create filter content with Arabic label
    filterContainer.innerHTML = `
        <div style="display: flex; align-items: center; gap: 10px; justify-content: flex-start;">
            <label for="branch-select" style="font-weight: 500; display: flex; gap: 8px;">
                <span>Branch Filter:</span>
            </label>
            <select id="branch-select" style="
                padding: 8px 12px;
                border-radius: 6px;
                border: 1px solid var(--border-color);
                min-width: 200px;
                background: var(--input-bg);
                color: var(--text-color);
                font-size: 14px;
                transition: all 0.2s ease;
            ">
                <option value="all">All Branches</option>
                ${branches.map(branch => `<option value="${branch}">${branch}</option>`).join('')}
            </select>
        </div>
    `;

    // Insert filter before dashboard stats
    const dashboardStats = document.querySelector('.dashboard-stats');
    if (dashboardStats) {
        dashboardStats.parentNode.insertBefore(filterContainer, dashboardStats);
    }

    // Add change event listener
    const branchSelect = filterContainer.querySelector('#branch-select');
    branchSelect.addEventListener('change', () => {
        updateDashboard(branchSelect.value);
    });
}

// Update the dashboard (using data from Vehicles sheet)
export function updateDashboard(selectedBranch = 'all') {
    try {
        // Validate vehicles data
        if (!Array.isArray(vehicles) || vehicles.length === 0) {
            console.error('Invalid or empty vehicles data');
            return;
        }

        // Log data for debugging
        console.log('Vehicles count:', vehicles.length);
        console.log('Selected branch:', selectedBranch);
        
        // Create branch filter if it doesn't exist
        if (!document.querySelector('.branch-filter')) {
            createBranchFilter();
        }

        // Filter vehicles based on selected branch
        const filteredVehicles = selectedBranch === 'all' 
            ? vehicles 
            : vehicles.filter(v => {
                const branchName = getBranchName(v);
                const matches = branchName.toLowerCase() === selectedBranch.toLowerCase();
                if (!matches && selectedBranch !== 'all') {
                    console.debug('Branch mismatch:', branchName, 'vs', selectedBranch, 'for vehicle:', v['Vehicle ID']);
                }
                return matches;
            });

        // Log filtering results
        console.log('Filtered vehicles count:', filteredVehicles.length);
        
        if (selectedBranch !== 'all' && filteredVehicles.length === 0) {
            showNotification('No vehicles found for selected branch', 'warning');
            console.warn(`No vehicles found for branch: ${selectedBranch}`);
        }

        // Update window.filteredVehicles for other functions that might use it
        window.filteredVehicles = filteredVehicles;

        // Update all dashboard components with filtered data
        updateStatistics(filteredVehicles);
        createCharts(filteredVehicles);

        // Update upcoming services in the dashboard with branch filter
        renderUpcomingServices('all', selectedBranch);

        // Update all vehicle-related charts
        renderVehicleStatusChart(filteredVehicles);
        renderVehicleTypesChart(filteredVehicles);
        renderVehicleAgeChart(filteredVehicles);
        renderFleetCompositionChart(filteredVehicles);

        // Update service statistics with branch filter
        const services = getUpcomingServices(selectedBranch);
        updateServiceStats(services);

        // Initialize and update maintenance statistics
        if (window.maintenanceRecords && Array.isArray(window.maintenanceRecords)) {
            console.log('Filtering maintenance records for branch:', selectedBranch);
            
            // Filter maintenance records by branch if needed
            const filteredMaintenance = selectedBranch === 'all' 
                ? window.maintenanceRecords 
                : window.maintenanceRecords.filter(r => {
                    const vehicle = vehicles.find(v => v['Vehicle ID'] === r['Vehicle ID']);
                    if (!vehicle) {
                        console.debug('Vehicle not found for maintenance record:', r['Vehicle ID']);
                        return false;
                    }
                    const branchName = getBranchName(vehicle);
                    return branchName.toLowerCase() === selectedBranch.toLowerCase();
                });

            console.log('Filtered maintenance records:', filteredMaintenance.length);
            calculateMaintenanceStats(filteredMaintenance);
            
        } else {
            console.log('No maintenance records available, loading from server...');
            loadMaintenanceDataForDashboard(selectedBranch);
        }

    } catch (error) {
        console.error('Error updating dashboard:', error);
        showNotification('Error updating dashboard', 'error');
    }
}

// Update statistics based on filtered vehicles
function updateStatistics(filteredVehicles) {
    try {
        if (!Array.isArray(filteredVehicles)) {
            console.error('Vehicles data is not an array:', filteredVehicles);
            resetStatistics();
            return;
        }

        // Calculate vehicle counts
        const totalVehicles = filteredVehicles.length;
        const statusCounts = filteredVehicles.reduce((counts, vehicle) => {
            const status = normalizeStatus(vehicle['Vehicle Status']);
            counts[status]++;
            return counts;
        }, { active: 0, maintenance: 0, inactive: 0 });

        // Calculate percentages
        const activePercent = Math.round((statusCounts.active / totalVehicles) * 100);
        const maintenancePercent = Math.round((statusCounts.maintenance / totalVehicles) * 100);
        const inactivePercent = Math.round((statusCounts.inactive / totalVehicles) * 100);

        // Update UI
        setElementText('total-vehicles', `${formatNumber(totalVehicles)} <span class="stat-percentage">100%</span>`);
        setElementText('active-vehicles', `${formatNumber(statusCounts.active)} <span class="stat-percentage">${activePercent}%</span>`);
        setElementText('in-maintenance', `${formatNumber(statusCounts.maintenance)} <span class="stat-percentage">${maintenancePercent}%</span>`);
        setElementText('inactive-vehicles', `${formatNumber(statusCounts.inactive)} <span class="stat-percentage">${inactivePercent}%</span>`);

    } catch (error) {
        console.error('Error updating statistics:', error);
        resetStatistics();
    }
}

// Update service statistics
export function updateServiceStats(services) {
    try {
        const stats = {
            maintenance: { count: 0, critical: 0 },
            tires: { count: 0, critical: 0 },
            license: { count: 0, critical: 0 },
            total: 0,
            critical: 0
        };

        // Get active vehicles count from filtered vehicles
        const activeVehicles = window.filteredVehicles.filter(v => v['Vehicle Status']?.toLowerCase() === 'active').length;
        const totalVehicles = activeVehicles || window.filteredVehicles.length;

        // Filter services to only include those that need attention
        const urgentServices = services.filter(service =>
            service.statusInfo.category === 'required' || service.statusInfo.category === 'upcoming'
        );

        urgentServices.forEach(service => {
            // Only count services for active vehicles
            const vehicle = window.filteredVehicles.find(v => v['Vehicle ID'] === service.vehicle.id);
            if (!vehicle || vehicle['Vehicle Status']?.toLowerCase() !== 'active') return;

            stats[service.type].count++;
            stats.total++;

            if (service.statusInfo.category === 'required') {
                stats[service.type].critical++;
                stats.critical++;
            }
        });

        // Update UI
        document.getElementById('maintenance-count').textContent = stats.maintenance.count;
        document.getElementById('tires-count').textContent = stats.tires.count;
        document.getElementById('license-count').textContent = stats.license.count;
        document.getElementById('critical-count').textContent = stats.critical;

        // Update percentages
        if (totalVehicles > 0) {
            document.getElementById('maintenance-percentage').textContent =
                Math.round((stats.maintenance.critical / totalVehicles) * 100) + '%';
            document.getElementById('tires-percentage').textContent =
                Math.round((stats.tires.critical / totalVehicles) + '%');
            document.getElementById('license-percentage').textContent =
                Math.round((stats.license.critical / totalVehicles) * 100) + '%';
            document.getElementById('critical-percentage').textContent =
                Math.round((stats.critical / totalVehicles) * 100) + '%';
        }
    } catch (error) {
        console.error('Error updating service stats:', error);
    }
}

// Helper functions
function setElementText(elementId, value) {
    const element = document.getElementById(elementId);
    if (element) element.innerHTML = value;
}

function formatNumber(num) {
    return String(num || 0);
}

function resetStatistics() {
    setElementText('total-vehicles', '0 <span class="stat-percentage">0%</span>');
    setElementText('active-vehicles', '0 <span class="stat-percentage">0%</span>');
    setElementText('in-maintenance', '0 <span class="stat-percentage">0%</span>');
    setElementText('inactive-vehicles', '0 <span class="stat-percentage">0%</span>');
}

function normalizeStatus(status) {
    if (!status) return 'inactive';
    status = String(status).toLowerCase().trim();
    if (status === 'active') return 'active';
    if (status === 'maintenance' || status === 'under maintenance') return 'maintenance';
    if (status === 'inactive') return 'inactive';
    if (status.includes('active')) return 'active';
    if (status.includes('maintenance')) return 'maintenance';
    if (status.includes('inactive')) return 'inactive';
    return 'inactive';
}

// Column visibility management
export function getVisibleColumns() {
    const defaultColumns = {
        vehicle: true,
        serviceType: true,
        expectedDate: true,
        status: true,
        remaining: true
    };

    try {
        const savedColumns = localStorage.getItem('upcomingServicesColumns');
        return savedColumns ? JSON.parse(savedColumns) : defaultColumns;
    } catch (e) {
        console.warn('Error getting visible columns:', e);
        return defaultColumns;
    }
}

export function toggleColumnVisibility(columnName, isVisible) {
    try {
        console.log(`Toggling visibility of column '${columnName}' to ${isVisible}`);
        const columns = getVisibleColumns();
        columns[columnName] = isVisible;
        localStorage.setItem('upcomingServicesColumns', JSON.stringify(columns));
        console.log('Updated columns configuration:', columns);
        return columns;
    } catch (error) {
        console.error('Error toggling column visibility:', error);
        return null;
    }
}

// Load maintenance data for dashboard
async function loadMaintenanceDataForDashboard(selectedBranch = 'all') {
    try {
        console.log('Loading maintenance data for dashboard');
        const params = new URLSearchParams({
            action: 'getMaintenance',
            timestamp: new Date().getTime()
        });

        if (selectedBranch !== 'all') {
            params.append('branch', selectedBranch);
        }

        const response = await fetch(`${API_URL}?${params}`);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        if (result.status === 'success' && result.data) {
            window.maintenanceRecords = result.data;
            console.log('Maintenance records loaded for dashboard:', window.maintenanceRecords.length);

            // Initialize and update maintenance statistics
            initializeMaintenanceStats();
            calculateMaintenanceStats(window.maintenanceRecords);
        } else {
            console.warn('No maintenance records found or error in response');
            window.maintenanceRecords = [];
        }
    } catch (error) {
        console.error('Error loading maintenance data:', error);
        window.maintenanceRecords = [];
        showNotification('Failed to load maintenance data', 'error');
    }
}

// Chart creation
function createCharts(filteredVehicles) {
    console.log('Creating dashboard charts...');
    const isDarkMode = document.body.classList.contains('dark-mode');

    Chart.defaults.color = isDarkMode ? '#e0e0e0' : '#666';
    Chart.defaults.borderColor = isDarkMode ? '#444' : '#ddd';

    const chartContainers = document.querySelectorAll('.chart-container');
    chartContainers.forEach(container => {
        container.style.height = '90vh';
        container.style.width = '98%';
        container.style.margin = '15px auto';
        container.style.position = 'relative';
        container.style.padding = '20px';
        container.style.boxSizing = 'border-box';
        container.style.minHeight = '500px';
    });

    createLocationChart(filteredVehicles);
    createServiceTypeChart(filteredVehicles);
    createMaintenanceKmChart(filteredVehicles);
    createTireChangeChart(filteredVehicles);
    createLicenseRenewalChart(filteredVehicles);
    createInactiveDistributionChart(filteredVehicles);
    createFleetHealthChart(filteredVehicles);
}

function getChartOptions(showScales = false) {
    const isDarkMode = document.body.classList.contains('dark-mode');
    const fontColor = isDarkMode ? '#e0e0e0' : '#666';
    const gridColor = isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)';

    const options = {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom',
                labels: {
                    font: { family: 'Arial' },
                    color: fontColor
                }
            },
            tooltip: {
                backgroundColor: isDarkMode ? 'rgba(0, 0, 0, 0.7)' : 'rgba(255, 255, 255, 0.7)',
                titleColor: isDarkMode ? '#fff' : '#000',
                bodyColor: isDarkMode ? '#fff' : '#000',
                borderColor: isDarkMode ? '#444' : '#ddd',
                borderWidth: 1,
                callbacks: {
                    label: function(context) {
                        const label = context.dataset.label || '';
                        const value = context.raw;
                        return `${label}: ${value.toLocaleString()}`;
                    }
                }
            }
        }
    };

    if (showScales) {
        options.scales = {
            y: {
                beginAtZero: true,
                ticks: {
                    font: { family: 'Arial' },
                    color: fontColor
                },
                grid: {
                    color: gridColor
                }
            },
            x: {
                ticks: {
                    font: { family: 'Arial' },
                    color: fontColor
                },
                grid: {
                    color: gridColor
                }
            }
        };
    }
    return options;
}

function getChartContext(canvasId) {
    const canvas = document.getElementById(canvasId);
    return canvas ? canvas.getContext('2d') : null;
}

function destroyChart(chartName) {
    if (charts[chartName]) {
        charts[chartName].destroy();
        charts[chartName] = null;
    }
}

function createLocationChart(filteredVehicles) {
    const ctx = getChartContext('location-chart');
    if (!ctx) return;

    const locationCounts = filteredVehicles.reduce((counts, vehicle) => {
        const location = vehicle['Current Location'] || 'Not Specified';
        counts[location] = (counts[location] || 0) + 1;
        return counts;
    }, {});

    const labels = Object.keys(locationCounts);
    const data = Object.values(locationCounts);
    const total = data.reduce((sum, val) => sum + val, 0);

    destroyChart('location');
    charts.location = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels.map((label, index) => 
                `${label}: ${data[index]} (${Math.round(data[index] / total * 100)}%)`
            ),
            datasets: [{
                label: 'Vehicle Distribution by Location',
                data: data,
                backgroundColor: ['#3498db', '#2ecc71', '#e74c3c', '#f39c12', '#9b59b6']
            }]
        },
        options: getChartOptions(true)
    });
}

function createServiceTypeChart(filteredVehicles) {
    const ctx = getChartContext('service-type-chart');
    if (!ctx) return;

    const typeCounts = filteredVehicles.reduce((counts, vehicle) => {
        const type = vehicle['Service Type'] || 'Not Specified';
        counts[type] = (counts[type] || 0) + 1;
        return counts;
    }, {});

    const labels = Object.keys(typeCounts);
    const data = Object.values(typeCounts);

    destroyChart('serviceType');
    charts.serviceType = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels.map((label, index) => `${label}: ${data[index]}`),
            datasets: [{
                label: 'Vehicle Service Types',
                data: data,
                backgroundColor: ['#3498db', '#2ecc71', '#e74c3c', '#f39c12']
            }]
        },
        options: getChartOptions(true)
    });
}

function createMaintenanceKmChart(filteredVehicles) {
    const ctx = getChartContext('maintenance-km-chart');
    if (!ctx) return;

    const kmData = filteredVehicles
        .filter(v => v['Km to next maintenance'])
        .map(v => ({
            id: v['Vehicle ID'],
            plate: v['License Plate'],
            km: parseInt(String(v['Km to next maintenance']).replace(/,/g, '')) || 0
        }))
        .sort((a, b) => a.km - b.km);

    destroyChart('maintenanceKm');
    charts.maintenanceKm = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: kmData.map(v => v.plate || v.id),
            datasets: [{
                label: 'KM to Maintenance',
                data: kmData.map(v => v.km),
                backgroundColor: kmData.map(v => {
                    const km = v.km;
                    return km < 1000 ? '#e74c3c' :
                           km < 5000 ? '#f39c12' : '#2ecc71';
                })
            }]
        },
        options: getChartOptions(true)
    });
}

function createTireChangeChart(filteredVehicles) {
    const ctx = getChartContext('tire-change-chart');
    if (!ctx) return;

    const tireData = filteredVehicles
        .filter(v => v['Km left for tire change'])
        .map(v => ({
            id: v['Vehicle ID'],
            plate: v['License Plate'],
            km: parseInt(String(v['Km left for tire change']).replace(/,/g, '')) || 0
        }))
        .sort((a, b) => a.km - b.km);

    destroyChart('tireChange');
    charts.tireChange = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: tireData.map(v => v.plate || v.id),
            datasets: [{
                label: 'KM to Tire Change',
                data: tireData.map(v => v.km),
                backgroundColor: tireData.map(v =>
                    v.km < 1000 ? '#e74c3c' :
                    v.km < 5000 ? '#f39c12' : '#2ecc71'
                )
            }]
        },
        options: getChartOptions(true)
    });
}

function createLicenseRenewalChart(filteredVehicles) {
    const ctx = getChartContext('license-renewal-chart');
    if (!ctx) return;

    const licenseData = filteredVehicles
        .filter(v => v['Days to renew license'])
        .map(v => ({
            id: v['Vehicle ID'],
            plate: v['License Plate'],
            days: parseInt(v['Days to renew license']) || 0
        }))
        .sort((a, b) => a.days - b.days);

    destroyChart('licenseRenewal');
    charts.licenseRenewal = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: licenseData.map(v => v.plate || v.id),
            datasets: [{
                label: 'Days to License Renewal',
                data: licenseData.map(v => v.days),
                backgroundColor: licenseData.map(v => {
                    const days = v.days;
                    return days < 7 ? '#e74c3c' :
                           days < 30 ? '#f39c12' : '#2ecc71';
                })
            }]
        },
        options: getChartOptions(true)
    });
}

function createInactiveDistributionChart(filteredVehicles) {
    const ctx = getChartContext('inactive-distribution-chart');
    if (!ctx) return;

    const inactiveVehicles = filteredVehicles.filter(v => 
        normalizeStatus(v['Vehicle Status']) === 'inactive'
    );

    const typeCounts = inactiveVehicles.reduce((counts, vehicle) => {
        const type = vehicle['Vehicle Type'] || 'Not Specified';
        counts[type] = (counts[type] || 0) + 1;
        return counts;
    }, {});

    const labels = Object.keys(typeCounts);
    const data = Object.values(typeCounts);
    const total = data.reduce((sum, val) => sum + val, 0);

    destroyChart('inactiveDistribution');
    charts.inactiveDistribution = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels.map((label, index) => 
                `${label}: ${data[index]} (${Math.round(data[index] / total * 100)}%)`
            ),
            datasets: [{
                label: 'Inactive Vehicles by Type',
                data: data,
                backgroundColor: ['#ef4444', '#f59e0b', '#fbbf24', '#64748b']
            }]
        },
        options: getChartOptions(true)
    });
}

function renderVehicleStatusChart(filteredVehicles) {
    const ctx = getChartContext('vehicle-status-chart');
    if (!ctx) return;

    const statusCounts = filteredVehicles.reduce((counts, vehicle) => {
        const status = normalizeStatus(vehicle['Vehicle Status']);
        counts[status]++;
        return counts;
    }, { active: 0, maintenance: 0, inactive: 0 });

    const labels = ['Active', 'In Maintenance', 'Inactive'];
    const data = [statusCounts.active, statusCounts.maintenance, statusCounts.inactive];
    const total = data.reduce((sum, val) => sum + val, 0);

    destroyChart('vehicleStatus');
    charts.vehicleStatus = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels.map((label, index) => 
                `${label}: ${data[index]} (${Math.round(data[index] / total * 100)}%)`
            ),
            datasets: [{
                label: 'Vehicle Status',
                data: data,
                backgroundColor: ['#2ecc71', '#f39c12', '#e74c3c']
            }]
        },
        options: getChartOptions(true)
    });
}

function renderVehicleTypesChart(filteredVehicles) {
    const ctx = getChartContext('vehicle-types-chart');
    if (!ctx) return;

    const typeCounts = filteredVehicles.reduce((counts, vehicle) => {
        const type = vehicle['Vehicle Type'] || 'Not Specified';
        counts[type] = (counts[type] || 0) + 1;
        return counts;
    }, {});

    const labels = Object.keys(typeCounts);
    const data = Object.values(typeCounts);
    const total = data.reduce((sum, val) => sum + val, 0);

    destroyChart('vehicleTypes');
    charts.vehicleTypes = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: labels.map((label, index) => 
                `${label}: ${data[index]} (${Math.round(data[index] / total * 100)}%)`
            ),
            datasets: [{
                label: 'Vehicle Types',
                data: data,
                backgroundColor: ['#3498db', '#2ecc71', '#e74c3c', '#f39c12', '#9b59b6']
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: true,
                    position: 'bottom'
                }
            }
        }
    });
}

function renderVehicleAgeChart(filteredVehicles) {
    const ctx = getChartContext('vehicle-age-chart');
    if (!ctx) return;

    const currentYear = new Date().getFullYear();
    const ageCounts = filteredVehicles.reduce((counts, vehicle) => {
        const year = parseInt(vehicle['Model']) || currentYear;
        const age = currentYear - year;
        const ageGroup =
            age < 2 ? '0-2 years' :
            age < 5 ? '2-5 years' :
            age < 8 ? '5-8 years' :
            '8+ years';
        counts[ageGroup] = (counts[ageGroup] || 0) + 1;
        return counts;
    }, {});

    const labels = ['0-2 years', '2-5 years', '5-8 years', '8+ years'];
    const data = labels.map(label => ageCounts[label] || 0);
    const total = data.reduce((sum, val) => sum + val, 0);

    destroyChart('vehicleAge');
    charts.vehicleAge = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: labels.map((label, index) => 
                `${label}: ${data[index]} (${Math.round(data[index] / total * 100)}%)`
            ),
            datasets: [{
                label: 'Vehicle Age Distribution',
                data: data,
                backgroundColor: ['#2ecc71', '#3498db', '#f39c12', '#e74c3c']
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: true,
                    position: 'bottom'
                }
            }
        }
    });
}

function renderFleetCompositionChart(filteredVehicles) {
    const ctx = getChartContext('fleet-composition-chart');
    if (!ctx) return;

    // Group vehicles by service type and vehicle type
    const serviceTypes = {};
    const vehicleTypes = {};
    const composition = {};

    // First identify all unique types
    filteredVehicles.forEach(vehicle => {
        const serviceType = vehicle['Service Type'] || 'Unknown';
        const vehicleType = vehicle['Vehicle Type'] || 'Unknown';

        serviceTypes[serviceType] = true;
        vehicleTypes[vehicleType] = true;

        const key = `${serviceType}:${vehicleType}`;
        composition[key] = (composition[key] || 0) + 1;
    });

    const serviceTypeLabels = Object.keys(serviceTypes);
    const vehicleTypeLabels = Object.keys(vehicleTypes);

    const datasets = vehicleTypeLabels.map((vehicleType, index) => {
        const hue = 120 - (index * (120 / Math.max(1, vehicleTypeLabels.length - 1)));
        return {
            label: `${vehicleType} (${serviceTypeLabels.reduce((sum, serviceType) => 
                sum + (composition[`${serviceType}:${vehicleType}`] || 0), 0)})`,
            data: serviceTypeLabels.map(serviceType => composition[`${serviceType}:${vehicleType}`] || 0),
            backgroundColor: `hsl(${hue}, 70%, 50%)`,
            borderColor: `hsl(${hue}, 80%, 40%)`,
            borderWidth: 1
        };
    });

    destroyChart('fleetComposition');
    charts.fleetComposition = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: serviceTypeLabels,
            datasets: datasets
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                x: {
                    stacked: true,
                    title: {
                        display: true,
                        text: 'Service Type'
                    }
                },
                y: {
                    stacked: true,
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Number of Vehicles'
                    }
                }
            },
            plugins: {
                legend: {
                    position: 'bottom'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const vehicleType = context.dataset.label.split(' (')[0];
                            return `${vehicleType}: ${context.raw} vehicles`;
                        },
                        footer: function(tooltipItems) {
                            const sum = tooltipItems.reduce((total, item) => total + item.parsed.y, 0);
                            return `Total: ${sum} vehicles`;
                        }
                    }
                }
            }
        }
    });
}

// Export to Excel functionality
export function exportToExcel() {
    try {
        console.log('Exporting to Excel');

        // Verify that we have the XLSX library
        if (typeof XLSX === 'undefined') {
            console.warn('XLSX library not found, loading from CDN...');

            // Load the SheetJS library if not already loaded
            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js';
            script.onload = () => {
                console.log('XLSX library loaded, proceeding with export');
                performExport();
            };
            script.onerror = () => {
                console.error('Failed to load XLSX library');
                showNotification('Failed to load Excel export library', 'error');
            };
            document.head.appendChild(script);
        } else {
            performExport();
        }
    } catch (error) {
        console.error('Excel export error:', error);
        showNotification('Export failed: ' + error.message, 'error');
    }
}

function performExport() {
    try {
        // Get services data to export
        const services = getUpcomingServices();
        if (!services || services.length === 0) {
            showNotification('No services data to export', 'warning');
            return;
        }

        // Get visible columns
        const visibleColumns = getVisibleColumns();

        // Convert to worksheet format
        const worksheet = XLSX.utils.json_to_sheet(services.map(service => {
            const row = {};

            if (visibleColumns.vehicle) {
                row['Vehicle'] = service.vehicle.plate || service.vehicle.id || 'Unknown';
            }

            if (visibleColumns.serviceType) {
                row['Service Type'] = getServiceTypeLabel(service.type);
            }

            if (visibleColumns.expectedDate) {
                row['Expected Date'] = service.expectedDate || 'N/A';
            }

            if (visibleColumns.status) {
                row['Status'] = service.statusInfo.text || 'Unknown';
            }

            if (visibleColumns.remaining) {
                if (service.type === 'license') {
                    row['Remaining'] = service.remaining + ' Days';
                } else {
                    row['Remaining'] = service.remaining + ' Km';
                }
            }

            return row;
        }));

        // Create workbook and add the worksheet
        const workbook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(workbook, worksheet, 'Upcoming Services');

        // Generate filename with current date
        const date = new Date();
        const filename = `upcoming_services_${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}.xlsx`;

        // Write and download
        XLSX.writeFile(workbook, filename);

        // Show success notification
        showNotification('Successfully exported to Excel', 'success');
    } catch (error) {
        console.error('Error during Excel export:', error);
        showNotification('Export failed: ' + error.message, 'error');
    }
}

function createFleetHealthChart(filteredVehicles) {
    const ctx = getChartContext('fleet-health-chart');
    if (!ctx) return;

    const healthCategories = {
        excellent: { count: 0, color: '#2ecc71', label: 'Excellent' },
        good: { count: 0, color: '#3498db', label: 'Good' },
        attention: { count: 0, color: '#f1c40f', label: 'Needs Attention' },
        critical: { count: 0, color: '#e74c3c', label: 'Critical' }
    };

    filteredVehicles.forEach(vehicle => {
        const kmToMaintenance = parseInt(String(vehicle['Km to next maintenance'] || '0').replace(/,/g, '')) || 0;
        const kmToTires = parseInt(String(vehicle['Km left for tire change'] || '0').replace(/,/g, '')) || 0;
        const daysToLicense = parseInt(String(vehicle['Days to renew license'] || '0')) || 0;

        let healthScore = 100;

        if (kmToMaintenance <= 0) healthScore -= 40;
        else if (kmToMaintenance < 5000) healthScore -= 20;
        else if (kmToMaintenance < 10000) healthScore -= 10;

        if (kmToTires <= 0) healthScore -= 30;
        else if (kmToTires < 2000) healthScore -= 15;
        else if (kmToTires < 5000) healthScore -= 7;

        if (daysToLicense <= 0) healthScore -= 30;
        else if (daysToLicense < 30) healthScore -= 15;
        else if (daysToLicense < 60) healthScore -= 7;

        if (healthScore >= 90) healthCategories.excellent.count++;
        else if (healthScore >= 70) healthCategories.good.count++;
        else if (healthScore >= 50) healthCategories.attention.count++;
        else healthCategories.critical.count++;
    });

    destroyChart('fleetHealth');
    charts.fleetHealth = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: Object.values(healthCategories).map(cat => {
                const percentage = Math.round((cat.count / filteredVehicles.length) * 100);
                return `${cat.label}: ${cat.count} (${percentage}%)`;
            }),
            datasets: [{
                data: Object.values(healthCategories).map(cat => cat.count),
                backgroundColor: Object.values(healthCategories).map(cat => cat.color),
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        font: { family: 'Arial' },
                        padding: 20
                    }
                }
            }
        }
    });
}

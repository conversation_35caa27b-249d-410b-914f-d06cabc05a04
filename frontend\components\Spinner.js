/**
 * Spinner Component - مؤشر التحميل
 * يوفر مؤشرات تحميل مختلفة للعمليات غير المتزامنة
 */

export class Spinner {
    constructor(containerId, options = {}) {
        this.containerId = containerId;
        this.container = containerId ? document.getElementById(containerId) : document.body;
        this.options = {
            type: 'default', // default, dots, pulse, bars
            size: 'medium', // small, medium, large
            color: '#0057ff',
            overlay: true,
            text: 'جاري التحميل...',
            ...options
        };
        
        this.spinnerId = `spinner-${Date.now()}`;
        this.isVisible = false;
    }

    show() {
        if (this.isVisible) return;
        
        this.create();
        this.isVisible = true;
    }

    hide() {
        if (!this.isVisible) return;
        
        const spinner = document.getElementById(this.spinnerId);
        if (spinner) {
            spinner.remove();
        }
        this.isVisible = false;
    }

    create() {
        const spinnerHTML = this.getSpinnerHTML();
        
        if (this.container) {
            this.container.insertAdjacentHTML('beforeend', spinnerHTML);
        }
    }

    getSpinnerHTML() {
        const sizeClass = `spinner-${this.options.size}`;
        const overlayClass = this.options.overlay ? 'spinner-overlay' : '';
        
        return `
            <div id="${this.spinnerId}" class="spinner-container ${overlayClass}">
                <div class="spinner-content">
                    ${this.getSpinnerByType()}
                    ${this.options.text ? `<div class="spinner-text">${this.options.text}</div>` : ''}
                </div>
            </div>
        `;
    }

    getSpinnerByType() {
        switch (this.options.type) {
            case 'dots':
                return `
                    <div class="spinner-dots ${this.getSizeClass()}">
                        <div class="dot"></div>
                        <div class="dot"></div>
                        <div class="dot"></div>
                    </div>
                `;
            
            case 'pulse':
                return `
                    <div class="spinner-pulse ${this.getSizeClass()}">
                        <div class="pulse-ring"></div>
                    </div>
                `;
            
            case 'bars':
                return `
                    <div class="spinner-bars ${this.getSizeClass()}">
                        <div class="bar"></div>
                        <div class="bar"></div>
                        <div class="bar"></div>
                        <div class="bar"></div>
                    </div>
                `;
            
            default:
                return `
                    <div class="spinner-default ${this.getSizeClass()}">
                        <div class="spinner-circle"></div>
                    </div>
                `;
        }
    }

    getSizeClass() {
        return `spinner-${this.options.size}`;
    }

    updateText(text) {
        const textElement = document.querySelector(`#${this.spinnerId} .spinner-text`);
        if (textElement) {
            textElement.textContent = text;
        }
    }
}

// دوال مساعدة للاستخدام السريع
let globalSpinner = null;

export function showSpinner(text = 'جاري التحميل...', options = {}) {
    if (globalSpinner) {
        hideSpinner();
    }
    
    globalSpinner = new Spinner(null, { text, overlay: true, ...options });
    globalSpinner.show();
}

export function hideSpinner() {
    if (globalSpinner) {
        globalSpinner.hide();
        globalSpinner = null;
    }
}

export function showPageSpinner(containerId, text = 'جاري التحميل...') {
    const spinner = new Spinner(containerId, { text, overlay: false });
    spinner.show();
    return spinner;
}

// إنشاء CSS للمؤشرات إذا لم يكن موجوداً
export function injectSpinnerCSS() {
    if (document.getElementById('spinner-styles')) return;
    
    const css = `
        <style id="spinner-styles">
            .spinner-container {
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 9999;
            }
            
            .spinner-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
            }
            
            .spinner-content {
                text-align: center;
                color: white;
            }
            
            .spinner-text {
                margin-top: 1rem;
                font-size: 14px;
            }
            
            /* Default Spinner */
            .spinner-default .spinner-circle {
                border: 3px solid rgba(255, 255, 255, 0.3);
                border-top: 3px solid #0057ff;
                border-radius: 50%;
                animation: spin 1s linear infinite;
            }
            
            .spinner-small .spinner-circle { width: 20px; height: 20px; }
            .spinner-medium .spinner-circle { width: 40px; height: 40px; }
            .spinner-large .spinner-circle { width: 60px; height: 60px; }
            
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
            
            /* Dots Spinner */
            .spinner-dots {
                display: flex;
                gap: 5px;
            }
            
            .spinner-dots .dot {
                background: #0057ff;
                border-radius: 50%;
                animation: bounce 1.4s ease-in-out infinite both;
            }
            
            .spinner-small .dot { width: 8px; height: 8px; }
            .spinner-medium .dot { width: 12px; height: 12px; }
            .spinner-large .dot { width: 16px; height: 16px; }
            
            .spinner-dots .dot:nth-child(1) { animation-delay: -0.32s; }
            .spinner-dots .dot:nth-child(2) { animation-delay: -0.16s; }
            
            @keyframes bounce {
                0%, 80%, 100% { transform: scale(0); }
                40% { transform: scale(1); }
            }
        </style>
    `;
    
    document.head.insertAdjacentHTML('beforeend', css);
}

// تحميل CSS تلقائياً
injectSpinnerCSS();

export default Spinner;

// Import DASHBOARD_COLUMNS from constants
import { DASHBOARD_COLUMNS } from './constants.js';
import { hasPermission, PERMISSIONS, ROLES } from './permissions.js';

// Global variables
export let currentUser = null;
export let vehicles = [];
export let drivers = [];
export let maintenanceRecords = [];
export let fuelRecords = [];
export let users = [];
export let charts = {};
export const API_URL = 'https://script.google.com/macros/s/AKfycbyvgby4dtYeqahohK7loTDqU4ek3yYBi_8J_J5c5CBF4aBF0tWflyiKaC0AJK1xy-oW/exec';

// Re-export DASHBOARD_COLUMNS
export { DASHBOARD_COLUMNS };

// printVehicleDetails function is exported at its definition

// Import modules
import { setInitializeUICallback, checkLoginStatus } from './auth.js';
import { setupEventListeners } from './events.js';
import { updateDashboard } from './dashboard.js';
import { renderVehiclesTable } from './vehicles.js';
import { initializeMaintenancePage, renderMaintenanceTable } from './maintenance.js';  // Add renderMaintenanceTable to imports
import { renderFuelTable } from './fuel.js';
import { renderDriversTable } from './drivers.js';
import { renderUsersTable } from './users.js';
import { showSpinner, hideSpinner } from './spinner.js';

// System ready after page load
document.addEventListener('DOMContentLoaded', initializeApp);

// Add code to ensure Chart.js is loaded

// Function to load external scripts
function loadScript(src) {
    return new Promise((resolve, reject) => {
        const script = document.createElement('script');
        script.src = src;
        script.onload = resolve;
        script.onerror = reject;
        document.head.appendChild(script);
    });
}

// Load required scripts for charts if not already available
async function loadDependencies() {
    try {
        if (typeof Chart === 'undefined') {
            console.log('Chart.js not found, loading from CDN...');
            await loadScript('https://cdn.jsdelivr.net/npm/chart.js');
            console.log('Chart.js loaded successfully');
        }

        // You can add additional libraries here if needed
    } catch (error) {
        console.error('Error loading dependencies:', error);
    }
}

// Call this function when loading the application
document.addEventListener('DOMContentLoaded', () => {
    loadDependencies();
    // Continue with other initialization
    // ...existing code...
});

// Export function for other modules to use
export { loadDependencies };

// Initialize the application
function initializeApp() {
    // Add event listeners for users page
    document.addEventListener('click', (event) => {
        // Use event delegation for the load users button
        if (event.target.closest('#load-users-btn')) {
            console.log('Load users button clicked');
            loadUsersData();
        }

        // Use event delegation for the add user button
        if (event.target.closest('#add-user-btn')) {
            console.log('Add user button clicked');
            import('./users.js').then(module => {
                if (module && typeof module.openUserModal === 'function') {
                    module.openUserModal();
                }
            });
        }
    });
    // Set user interface initialization function
    setInitializeUICallback(initializeUserInterface);

    // Setup event listeners
    setupEventListeners();

    // Check login status
    checkLoginStatus();

    // Create dark mode toggle button
    createDarkModeToggle();

    // Check user's dark mode preference
    checkDarkModePreference();

    // Create notification system
    createNotificationSystem();

    // Add functions to window object for global access
    window.openVehicleDetailsModal = openVehicleDetailsModal;
    window.printVehicleDetails = printVehicleDetails;
    window.closeModal = closeModal;

    // Ensure openVehicleDetailsModal is available globally
    if (!window.openVehicleDetailsModal) {
        window.openVehicleDetailsModal = openVehicleDetailsModal;
    }

    // Add global event delegation for profile buttons and print buttons
    document.addEventListener('click', function(e) {
        const profileBtn = e.target.closest('.profile-btn');
        const printBtn = e.target.closest('.print-vehicle');

        if (profileBtn) {
            const vehicleId = profileBtn.dataset.vehicleId;
            if (vehicleId) {
                openVehicleDetailsModal(vehicleId);
            }
        } else if (printBtn) {
            const vehicleId = printBtn.dataset.vehicleId;
            if (vehicleId) {
                printVehicleDetails(vehicleId);
            }
        }
    });
}

// Initialize user interface based on user role
function initializeUserInterface(userData) {
    // Update current user
    currentUser = userData;

    // Get main elements
    const loginContainer = document.getElementById('login-container');
    const mainContainer = document.getElementById('main-container');
    const sidebar = document.getElementById('sidebar');
    const userName = document.getElementById('user-name');
    const userRole = document.getElementById('user-role');
    const headerUserName = document.getElementById('header-user-name');

    // Hide login screen and show main interface
    loginContainer.style.display = 'none';
    mainContainer.style.display = 'flex';
    sidebar.style.display = 'block';

    // Update user information in the interface
    if (userName) userName.textContent = currentUser.name;
    userRole.textContent = getRoleTranslation(currentUser.role);
    if (headerUserName) headerUserName.textContent = `Welcome, ${currentUser.name}`;

    // Set user roles
    document.body.className = currentUser.role;

    // Disable unauthorized functions based on user role
    disableActionsForEmployee();

    // منع عرض جدول الخدمات القادمة عند أول تحميل إذا لم يكن هناك تفضيل محفوظ
    if (localStorage.getItem('showUpcomingServices') === null) {
        localStorage.setItem('showUpcomingServices', 'false');
    }

    // Load data
    loadData();

    // Show dashboard
    showPage('dashboard');

    // Get visible columns function for column management
    import('./dashboard.js').then(module => {
        if (module && typeof module.getVisibleColumns === 'function') {
            window.getVisibleColumns = module.getVisibleColumns;
            console.log('getVisibleColumns function loaded globally');
        } else {
            console.warn('getVisibleColumns function not found in dashboard.js module');
            // Create fallback function
            window.getVisibleColumns = function() {
                const defaultColumns = {
                    vehicle: true,
                    serviceType: true,
                    expectedDate: true,
                    status: true,
                    remaining: true
                };

                try {
                    const savedColumns = localStorage.getItem('upcomingServicesColumns');
                    return savedColumns ? JSON.parse(savedColumns) : defaultColumns;
                } catch (e) {
                    console.warn('Error getting visible columns:', e);
                    return defaultColumns;
                }
            };
        }
    }).catch(error => {
        console.error('Error importing dashboard.js for getVisibleColumns:', error);
        // Create fallback function
        window.getVisibleColumns = function() {
            const defaultColumns = {
                vehicle: true,
                serviceType: true,
                expectedDate: true,
                status: true,
                remaining: true
            };

            try {
                const savedColumns = localStorage.getItem('upcomingServicesColumns');
                return savedColumns ? JSON.parse(savedColumns) : defaultColumns;
            } catch (e) {
                console.warn('Error getting visible columns:', e);
                return defaultColumns;
            }
        };
    });

    // Export Excel function for global access
    window.exportToExcel = function() {
        console.log('Global exportToExcel called');
        try {
            // First try to import and use the module version
            import('./dashboard.js')
                .then(module => {
                    if (module && typeof module.exportToExcel === 'function') {
                        console.log('Calling exportToExcel from dashboard.js module');
                        module.exportToExcel();
                    } else {
                        console.error('exportToExcel function not found in dashboard.js module');
                        showNotification('Export function not available', 'error');
                    }
                })
                .catch(error => {
                    console.error('Error importing dashboard.js:', error);
                    showNotification('Error exporting to Excel', 'error');
                });
        } catch (e) {
            console.error('Excel export failed:', e);
            showNotification('Excel export failed: ' + e.message, 'error');
        }
    };

    // Setup column reset button
    setupColumnResetButton();
}

// Setup column reset button
function setupColumnResetButton() {
    const resetButton = document.getElementById('reset-columns');
    if (resetButton) {
        resetButton.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation(); // Prevent dropdown from closing

            // Reset to default column settings
            const defaultColumns = {
                vehicle: true,
                serviceType: true,
                expectedDate: true,
                status: true,
                remaining: true
            };

            // Save to localStorage
            localStorage.setItem('upcomingServicesColumns', JSON.stringify(defaultColumns));

            // Update checkbox UI
            document.querySelectorAll('.column-option input[type="checkbox"]').forEach(checkbox => {
                const columnName = checkbox.dataset.column;
                if (columnName && defaultColumns[columnName] !== undefined) {
                    checkbox.checked = defaultColumns[columnName];
                }
            });

            // Update table
            import('./dashboard.js').then(module => {
                if (module && typeof module.updateUpcomingServices === 'function') {
                    const activeFilterBtn = document.querySelector('.services-filter .filter-btn.active');
                    const filterType = activeFilterBtn ? activeFilterBtn.getAttribute('data-filter') : 'all';
                    module.updateUpcomingServices(filterType);

                    showNotification('Column settings have been reset to default', 'success');
                }
            }).catch(error => {
                console.error('Error importing dashboard.js:', error);
            });
        });
    }
}

// Get user role translation
function getRoleTranslation(role) {
    // Return the role directly as we're now using the display names from ROLES
    return role || 'Unknown';
}

// Disable unauthorized actions based on user role
function disableActionsForEmployee() {
    console.log('Current user role in disableActionsForEmployee:', currentUser?.role);
    // Hide add vehicle button if user doesn't have permission to add vehicles
    // Only Super Admin, General Manager, Operations Manager, and Fleet Manager can add vehicles
    const allowedRolesToAddVehicles = [
        ROLES.SUPER_ADMIN,
        ROLES.GENERAL_MANAGER,
        ROLES.OPERATIONS_MANAGER,
        ROLES.FLEET_MANAGER
    ];

    if (!allowedRolesToAddVehicles.includes(currentUser?.role)) {
        const addVehicleBtn = document.getElementById('add-vehicle-btn');
        if (addVehicleBtn) addVehicleBtn.style.display = 'none';
    }

    // Hide add driver button if user doesn't have permission to manage vehicles
    if (!hasPermission(currentUser?.role, PERMISSIONS.MANAGE_VEHICLES_ALL_BRANCHES) &&
        !hasPermission(currentUser?.role, PERMISSIONS.MANAGE_VEHICLES_BRANCH)) {
        const addDriverBtn = document.getElementById('add-driver-btn');
        if (addDriverBtn) addDriverBtn.style.display = 'none';
    }

    // Hide add maintenance button if user doesn't have permission to register maintenance
    if (!hasPermission(currentUser?.role, PERMISSIONS.REGISTER_MAINTENANCE)) {
        const addMaintenanceBtn = document.getElementById('add-maintenance-btn');
        if (addMaintenanceBtn) addMaintenanceBtn.style.display = 'none';
    }

    // Hide add fuel button if user doesn't have permission to update mileage
    if (!hasPermission(currentUser?.role, PERMISSIONS.UPDATE_MILEAGE_ALL_BRANCHES) &&
        !hasPermission(currentUser?.role, PERMISSIONS.UPDATE_MILEAGE_BRANCH) &&
        !hasPermission(currentUser?.role, PERMISSIONS.UPDATE_MILEAGE_ASSIGNED)) {
        const addFuelBtn = document.getElementById('add-fuel-btn');
        if (addFuelBtn) addFuelBtn.style.display = 'none';
    }

    // Hide users page link if user doesn't have permission to view users
    // Only Super Admin, General Manager, Operations Manager, Fleet Manager, and Fleet Supervisor can view users page
    const allowedRolesToViewUsers = [
        ROLES.SUPER_ADMIN,
        ROLES.GENERAL_MANAGER,
        ROLES.OPERATIONS_MANAGER,
        ROLES.FLEET_MANAGER,
        ROLES.FLEET_SUPERVISOR
    ];

    if (!allowedRolesToViewUsers.includes(currentUser?.role)) {
        const usersNavItem = document.getElementById('users-nav-item');
        if (usersNavItem) {
            console.log('Hiding users nav item for role:', currentUser?.role);
            usersNavItem.style.display = 'none';
        } else {
            console.warn('Users nav item not found');
        }
    } else {
        const usersNavItem = document.getElementById('users-nav-item');
        if (usersNavItem) {
            console.log('Showing users nav item for role:', currentUser?.role);
            usersNavItem.style.display = 'block';
        }
    }

    // Hide reports page link if user doesn't have permission to view reports
    if (!hasPermission(currentUser?.role, PERMISSIONS.VIEW_REPORTS)) {
        const reportsNavLink = document.querySelector('.sidebar-nav a[data-page="reports"]');
        if (reportsNavLink) reportsNavLink.parentElement.style.display = 'none';
    }
}

// Show requested page
export function showPage(page) {
    // Hide all pages
    const pages = document.querySelectorAll('.page');
    pages.forEach(p => p.classList.remove('active'));

    // Show the requested page
    const targetPage = document.getElementById(`${page}-page`);
    if (targetPage) {
        targetPage.classList.add('active');
        document.getElementById('current-page-title').textContent = getPageTitle(page);
        updatePageContent(page);
    }
}

// Update page content
function updatePageContent(page) {
    console.log('updatePageContent called for page:', page);
    switch(page) {
        case 'dashboard':
            updateDashboard();
            break;
        case 'vehicles':
            renderVehiclesTable();
            break;
        case 'maintenance':
            // Check if user has permission to register maintenance or receive alerts
            if (hasPermission(currentUser?.role, PERMISSIONS.REGISTER_MAINTENANCE) ||
                hasPermission(currentUser?.role, PERMISSIONS.RECEIVE_MAINTENANCE_ALERTS)) {
                initializeMaintenancePage();
            }
            break;
        case 'fuel':
            renderFuelTable();
            break;
        case 'drivers':
            renderDriversTable();
            break;
        case 'users':
            // Check if user has permission to view users page
            const allowedRolesToViewUsers = [
                ROLES.SUPER_ADMIN,
                ROLES.GENERAL_MANAGER,
                ROLES.OPERATIONS_MANAGER,
                ROLES.FLEET_MANAGER,
                ROLES.FLEET_SUPERVISOR
            ];

            console.log('Showing users page, current user role:', currentUser?.role);
            console.log('Allowed roles:', allowedRolesToViewUsers);
            console.log('Is allowed:', allowedRolesToViewUsers.includes(currentUser?.role));

            if (allowedRolesToViewUsers.includes(currentUser?.role)) {
                // Get the users page element
                const usersPage = document.getElementById('users-page');

                // Check if the users page is empty
                if (!usersPage.innerHTML.trim()) {
                    console.log('Users page is empty, initializing...');
                    // Create a button to initialize the users page
                    usersPage.innerHTML = `
                        <div style="text-align: center; padding: 2rem;">
                            <h3>Users Management</h3>
                            <p>Click the button below to load users data</p>
                            <button id="init-users-page" class="btn btn-primary">
                                <i class="fas fa-sync"></i> Load Users Data
                            </button>
                        </div>
                    `;

                    // Add event listener to the button
                    document.getElementById('init-users-page').addEventListener('click', () => {
                        import('./users.js').then(module => {
                            if (module && typeof module.renderUsersTable === 'function') {
                                module.renderUsersTable();
                            }
                        });
                    });
                } else {
                    // Import and call renderUsersTable from users.js
                    import('./users.js').then(module => {
                        if (module && typeof module.renderUsersTable === 'function') {
                            // We're already on the users page, so just update the table
                            // without calling showPage again to avoid recursion
                            module.renderUsersTable();
                        }
                    }).catch(error => {
                        console.error('Error importing users.js:', error);
                    });
                }
            } else {
                showPage('dashboard'); // Redirect to dashboard if no permission
                showNotification('You do not have permission to view users', 'error');
            }
            break;
        case 'reports':
            // Check if user has permission to view reports
            if (!hasPermission(currentUser?.role, PERMISSIONS.VIEW_REPORTS)) {
                showPage('dashboard'); // Redirect to dashboard if no permission
                showNotification('You do not have permission to view reports', 'error');
            } else {
                // Initialize reports page
                initializeReportsPage();
            }
            break;
    }
}

// Initialize reports page
function initializeReportsPage() {
    console.log('Initializing reports page...');

    // Add event listeners to report buttons
    const generateReportBtn = document.getElementById('generate-report-btn');
    if (generateReportBtn) {
        generateReportBtn.addEventListener('click', () => {
            import('./reports.js').then(module => {
                if (module && typeof module.generateReport === 'function') {
                    module.generateReport();
                } else {
                    console.error('generateReport function not found');
                    showNotification('Error generating report', 'error');
                }
            }).catch(error => {
                console.error('Error importing reports.js:', error);
                showNotification('Error loading reports module', 'error');
            });
        });
    }

    const exportReportBtn = document.getElementById('export-report-btn');
    if (exportReportBtn) {
        exportReportBtn.addEventListener('click', () => {
            import('./reports.js').then(module => {
                if (module && typeof module.exportReport === 'function') {
                    module.exportReport();
                } else {
                    console.error('exportReport function not found');
                    showNotification('Error exporting report', 'error');
                }
            }).catch(error => {
                console.error('Error importing reports.js:', error);
                showNotification('Error loading reports module', 'error');
            });
        });
    }

    // Set default dates (last 30 days)
    const today = new Date();
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(today.getDate() - 30);

    const dateFromInput = document.getElementById('date-from');
    const dateToInput = document.getElementById('date-to');

    if (dateFromInput) {
        dateFromInput.valueAsDate = thirtyDaysAgo;
    }

    if (dateToInput) {
        dateToInput.valueAsDate = today;
    }

    // Show success notification
    showNotification('Reports page initialized successfully', 'success');
}

// Get page title
function getPageTitle(page) {
    const titles = {
        'dashboard': 'Dashboard',
        'vehicles': 'Vehicles Management',
        'maintenance': 'Maintenance Management',
        'fuel': 'Fuel Management',
        'drivers': 'Drivers Management',
        'reports': 'Reports',
        'users': 'Users Management'
    };
    return titles[page] || page;
}

// Load data
async function loadData() {
    try {
        // Show loading spinner
        showSpinner('Loading system data...');
        console.log('Starting to load application data...');

        // Load vehicles data first as it's required by other components
        console.log('Loading vehicles data...');
        const vehiclesResult = await loadVehiclesData();
        if (!vehiclesResult || !Array.isArray(vehiclesResult) || vehiclesResult.length === 0) {
            console.warn('No vehicles data loaded, initializing with empty array');
            vehicles = [];
        }

        // Load remaining data in parallel
        console.log('Loading other data...');
        const [driversResult, maintenanceResult, fuelResult, usersResult] = await Promise.all([
            loadDriversData(),
            loadMaintenanceData(),
            loadFuelData(),
            // Load users data only for authorized roles
            [
                ROLES.SUPER_ADMIN,
                ROLES.GENERAL_MANAGER,
                ROLES.OPERATIONS_MANAGER,
                ROLES.FLEET_MANAGER,
                ROLES.FLEET_SUPERVISOR
            ].includes(currentUser?.role) ? loadUsersData() : Promise.resolve([])
        ]);

        // Log the results
        console.log('Data loading complete:', {
            vehiclesCount: vehicles.length,
            driversCount: drivers.length,
            maintenanceCount: maintenanceRecords.length,
            fuelCount: fuelRecords.length,
            usersCount: users.length
        });

        // Hide loading spinner
        hideSpinner();

        // Show success notification only if we have some data
        if (vehicles.length > 0) {
            showNotification('Data loaded successfully', 'success');
        } else {
            showNotification('System initialized with no vehicle data', 'warning');
        }

        // Update UI
        updateDashboard();
    } catch (error) {
        console.error('Error loading data:', error);
        showNotification('Error loading data. Please try again.', 'error');
        hideSpinner();
    }
}

// Note: The old loading indicator functions have been replaced by the new spinner.js module

// Modify data loading functions to always fetch fresh data
async function loadVehiclesData() {
    try {
        console.log('Starting to load vehicles data...');
        const response = await fetchData('getDashboard');
        console.log('Dashboard API response:', response);

        if (response.status === 'success' && Array.isArray(response.data)) {
            // Log the raw data first
            console.log('Raw vehicles data:', response.data);

            // Process the vehicle data
            vehicles = response.data.map(vehicle => {
                // Ensure all required fields exist with default values
                const processedVehicle = {
                    'Vehicle ID': vehicle['Vehicle ID'] || vehicle.id || generateTempId('VEH'),
                    'License Plate': vehicle['License Plate'] || 'Unknown',
                    'Vehicle Status': vehicle['Vehicle Status'] || vehicle.vehicleStatus || vehicle.status || 'Active',
                    'Km to next maintenance': vehicle['Km to next maintenance'] || '0',
                    'Km left for tire change': vehicle['Km left for tire change'] || '0',
                    'Days to renew license': vehicle['Days to renew license'] || '0',
                    'License Renewal Date': vehicle['License Renewal Date'] || null,
                    'Branch': vehicle['Branch'] || vehicle.branch || 'Main Branch',
                    'Vehicle Type': vehicle['Vehicle Type'] || vehicle.type || 'Standard',
                    'Model': vehicle['Model'] || vehicle.model || new Date().getFullYear().toString(),
                    'Driver Name': vehicle['Driver Name'] || vehicle.driver || '',
                    ...vehicle // Keep any other existing fields
                };

                // Log the processed vehicle
                console.log('Processed vehicle:', processedVehicle);

                return processedVehicle;
            });

            console.log(`Successfully loaded and processed ${vehicles.length} vehicles`);

            // Sort vehicles by status (Active first)
            vehicles.sort((a, b) => {
                const statusA = (a['Vehicle Status'] || '').toLowerCase();
                const statusB = (b['Vehicle Status'] || '').toLowerCase();
                if (statusA === 'active' && statusB !== 'active') return -1;
                if (statusA !== 'active' && statusB === 'active') return 1;
                return 0;
            });

            // Update UI
            if (typeof updateDashboard === 'function') {
                console.log('Updating dashboard with vehicle data...');
                updateDashboard();
            }
            if (typeof renderVehiclesTable === 'function') {
                console.log('Rendering vehicles table...');
                renderVehiclesTable();
            }

            return vehicles;
        } else {
            // If no valid data, initialize with empty array but log the issue
            console.warn('No valid vehicles data in response:', response);
            vehicles = [];
            showNotification('No vehicle data available', 'warning');
            return [];
        }
    } catch (error) {
        console.error('Error in loadVehiclesData:', error);
        showNotification('Error loading vehicle data: ' + error.message, 'error');
        return [];
    }
}

async function loadDriversData() {
    try {
        const data = await fetchData('getDrivers');
        if (data.status === 'success') {
            drivers = data.data;
            renderDriversTable();
            return drivers;
        }
        return [];
    } catch (error) {
        console.error('Error loading drivers data:', error);
        showNotification('Error loading drivers data', 'error');
        return [];
    }
}

async function loadMaintenanceData() {
    try {
        const data = await fetchData('getMaintenance');
        if (data.status === 'success') {
            maintenanceRecords = data.data;
            renderMaintenanceTable();
            return maintenanceRecords;
        }
        return [];
    } catch (error) {
        console.error('Error loading maintenance data:', error);
        showNotification('Error loading maintenance data', 'error');
        return [];
    }
}

async function loadFuelData() {
    try {
        const data = await fetchData('getFuel');
        if (data.status === 'success' && data.data && data.data.length > 0) {
            console.log('Successfully loaded fuel records from API:', data.data.length);
            processFuelRecords(data.data);
            return fuelRecords;
        } else {
            console.warn('No fuel records returned from API, using sample data');
            loadSampleFuelData();
            return fuelRecords;
        }
    } catch (error) {
        console.error('Error loading fuel data from API:', error);
        loadSampleFuelData();
        return fuelRecords;
    }
}

// Process fuel records to ensure consistent format
function processFuelRecords(records) {
    try {
        // Map to ensure consistent structure
        fuelRecords = records.map(record => {
            const processedRecord = {
                ...record,
                id: record.id || generateTempId('fuel'),
                // Make sure vehicle property exists and matches Vehicle ID
                vehicle: record.vehicle || record['Vehicle ID'] || '',
                'Vehicle ID': record['Vehicle ID'] || record.vehicle || '',
                'License Plate': record['License Plate'] || '',
                'Fuel Type': record['Fuel Type'] || record.type || '92',
                Date: record.Date || record.date || new Date().toISOString().split('T')[0],
                Amount: record.Amount || record.amount || '£0',
                Quantity: parseFloat(record.Quantity || record.quantity || 0),
                Distance: parseFloat(record.Distance || record.distance || 0),
                'Consumption Rate': record['Consumption Rate'] ||
                    (parseFloat(record.Quantity || 0) > 0 ?
                        (parseFloat(record.Distance || 0) / parseFloat(record.Quantity || 1)).toFixed(1) :
                        '0')
            };

            return processedRecord;
        });

        console.log('Processed fuel records:', fuelRecords.length);
        renderFuelTable();
    } catch (error) {
        console.error('Error processing fuel records:', error);
        fuelRecords = [];
        renderFuelTable();
    }
}

// Generate temporary ID
function generateTempId(prefix) {
    return `${prefix}-${Math.random().toString(36).substring(2, 10)}`;
}

// Load sample fuel data when API fails or returns empty results
function loadSampleFuelData() {
    console.log('Loading sample fuel data');

    // Sample fuel data based on the provided structure - enhanced with driver information
    const sampleFuelData = [
        {
            "id": "fuel-1",
            "Vehicle ID": "**********",
            "License Plate": "546 د ى ج",
            "Vehicle Type": "Comfort",
            "Vehicle Status": "Active",
            "Vehicle VIN/SN": "**********",
            "Date": "2023-01-28",
            "Time": "11:35:01 PM",
            "Driver Name": "Ahmed Abdel Aziz",
            "Driver Name (AR)": "احمد عبدالعزيز الصغير",
            "Driver Name (EN)": "Ahmed Abdel Aziz",
            "Fuel Type": "92",
            "Amount": "£420.00",
            "Quantity": 27.54,
            "Odometer": 144047,
            "Distance": 340,
            "Consumption Rate": "12.3",
            "Cost per Meter": "£1.24",
            "Branch": "Gouna",
            "Vehicle Group": "London Cab",
            "Vehicle Model": 2023
        },
        {
            "id": "fuel-2",
            "Vehicle ID": "PJ535346",
            "License Plate": "199 د ي ج",
            "Vehicle Type": "SUV",
            "Vehicle Status": "Active",
            "Vehicle VIN/SN": "PJ535346",
            "Date": "2023-02-15",
            "Time": "09:20:45 AM",
            "Driver Name": "Mohamed Ahmed Ali",
            "Driver Name (AR)": "محمد أحمد علي",
            "Driver Name (EN)": "Mohamed Ahmed Ali",
            "Fuel Type": "95",
            "Amount": "£350.00",
            "Quantity": 22.5,
            "Odometer": 102750,
            "Distance": 290,
            "Consumption Rate": "12.9",
            "Cost per Meter": "£1.21",
            "Branch": "Cairo",
            "Vehicle Group": "SUVs",
            "Vehicle Model": 2022
        },
        {
            "id": "fuel-3",
            "Vehicle ID": "A008451",
            "License Plate": "782 د ي د",
            "Vehicle Type": "Sedan",
            "Vehicle Status": "Active",
            "Vehicle VIN/SN": "A008451",
            "Date": "2023-03-10",
            "Time": "02:15:30 PM",
            "Driver Name": "Khaled Mohamed Abdullah",
            "Driver Name (AR)": "خالد محمد عبدالله",
            "Driver Name (EN)": "Khaled Mohamed Abdullah",
            "Fuel Type": "Diesel",
            "Amount": "£280.00",
            "Quantity": 18.5,
            "Odometer": 89750,
            "Distance": 310,
            "Consumption Rate": "16.8",
            "Cost per Meter": "£0.90",
            "Branch": "Alexandria",
            "Vehicle Group": "Economy",
            "Vehicle Model": 2021
        },
        {
            "id": "fuel-4",
            "Vehicle ID": "PR047612",
            "License Plate": "923 د ي هـ",
            "Vehicle Type": "Sedan",
            "Vehicle Status": "Active",
            "Vehicle VIN/SN": "PR047612",
            "Date": "2023-03-12",
            "Time": "10:30:00 AM",
            "Driver Name": "Omar Hassan",
            "Driver Name (AR)": "عمر حسن",
            "Driver Name (EN)": "Omar Hassan",
            "Fuel Type": "92",
            "Amount": "£310.00",
            "Quantity": 20.5,
            "Odometer": 67500,
            "Distance": 285,
            "Consumption Rate": "13.9",
            "Cost per Meter": "£1.09",
            "Branch": "Cairo",
            "Vehicle Group": "Economy",
            "Vehicle Model": 2022
        },
        {
            "id": "fuel-5",
            "Vehicle ID": "A008629",
            "License Plate": "546 د ى ج",
            "Vehicle Type": "SUV",
            "Vehicle Status": "Active",
            "Vehicle VIN/SN": "A008629",
            "Date": "2023-03-15",
            "Time": "03:45:00 PM",
            "Driver Name": "Ahmed Abdel Aziz",
            "Driver Name (AR)": "احمد عبدالعزيز الصغير",
            "Driver Name (EN)": "Ahmed Abdel Aziz",
            "Fuel Type": "95",
            "Amount": "£390.00",
            "Quantity": 25.0,
            "Odometer": 145200,
            "Distance": 325,
            "Consumption Rate": "13.0",
            "Cost per Meter": "£1.20",
            "Branch": "Gouna",
            "Vehicle Group": "SUVs",
            "Vehicle Model": 2021
        },
        {
            "id": "fuel-6",
            "Vehicle ID": "PR047602",
            "License Plate": "381 د ي و",
            "Vehicle Type": "Comfort",
            "Vehicle Status": "Active",
            "Vehicle VIN/SN": "PR047602",
            "Date": "2023-03-18",
            "Time": "08:15:00 AM",
            "Driver Name": "Mustafa Ibrahim",
            "Driver Name (AR)": "مصطفى ابراهيم",
            "Driver Name (EN)": "Mustafa Ibrahim",
            "Fuel Type": "Diesel",
            "Amount": "£340.00",
            "Quantity": 22.0,
            "Odometer": 91200,
            "Distance": 405,
            "Consumption Rate": "18.4",
            "Cost per Meter": "£0.84",
            "Branch": "Alexandria",
            "Vehicle Group": "London Cab",
            "Vehicle Model": 2021
        },
        {
            "id": "fuel-7",
            "Vehicle ID": "PR047620",
            "License Plate": "752 د ي ز",
            "Vehicle Type": "Sedan",
            "Vehicle Status": "Active",
            "Vehicle VIN/SN": "PR047620",
            "Date": "2023-03-20",
            "Time": "05:30:00 PM",
            "Driver Name": "Mahmoud Ali",
            "Driver Name (AR)": "محمود علي",
            "Driver Name (EN)": "Mahmoud Ali",
            "Fuel Type": "92",
            "Amount": "£280.00",
            "Quantity": 18.5,
            "Odometer": 124600,
            "Distance": 210,
            "Consumption Rate": "11.4",
            "Cost per Meter": "£1.33",
            "Branch": "Cairo",
            "Vehicle Group": "Economy",
            "Vehicle Model": 2022
        },
        {
            "id": "fuel-8",
            "Vehicle ID": "PJ535346",
            "License Plate": "199 د ي ج",
            "Vehicle Type": "SUV",
            "Vehicle Status": "Active",
            "Vehicle VIN/SN": "PJ535346",
            "Date": "2023-03-22",
            "Time": "02:00:00 PM",
            "Driver Name": "Mohamed Ahmed Ali",
            "Driver Name (AR)": "محمد أحمد علي",
            "Driver Name (EN)": "Mohamed Ahmed Ali",
            "Fuel Type": "95",
            "Amount": "£365.00",
            "Quantity": 23.5,
            "Odometer": 103040,
            "Distance": 290,
            "Consumption Rate": "12.3",
            "Cost per Meter": "£1.26",
            "Branch": "Cairo",
            "Vehicle Group": "SUVs",
            "Vehicle Model": 2022
        }
    ];

    processFuelRecords(sampleFuelData);
}

export async function loadUsersData() {
    try {
        console.log('loadUsersData called, current user role:', currentUser?.role);

        // Pass the current user role to check permissions
        const data = await fetchData('getUsers', { currentUserRole: currentUser?.role });
        console.log('Users data response:', data);

        if (data.status === 'success') {
            users = data.data;
            console.log('Users data loaded successfully:', users.length, 'users');

            // Import and call renderUsersTable from users.js
            try {
                await import('./users.js').then(module => {
                    if (module && typeof module.renderUsersTable === 'function') {
                        module.renderUsersTable();
                    } else {
                        console.error('renderUsersTable function not found');
                    }
                });
            } catch (error) {
                console.error('Error importing users.js:', error);
            }

            return users;
        } else if (data.status === 'error') {
            console.error('Error loading users data:', data.message);
            showNotification(data.message, 'error');
        }
        return [];
    } catch (error) {
        console.error('Error loading users data:', error);
        showNotification('Error loading users data', 'error');
        return [];
    }
}

// Helper function to fetch data with improved error handling
async function fetchData(action, additionalParams = {}) {
    try {
        console.log(`Fetching data for action: ${action}`);
        const params = new URLSearchParams();
        params.append('action', action);

        // Add manager ID for roles that should only see their branch data
        if (currentUser?.role === 'manager' ||
            currentUser?.role === 'Fleet Manager' ||
            currentUser?.role === 'Fleet Supervisor') {
            params.append('managerId', currentUser.id);
        }

        // Add any additional parameters
        for (const [key, value] of Object.entries(additionalParams)) {
            if (value !== undefined && value !== null) {
                params.append(key, value);
            }
        }

        // Add timestamp to prevent caching
        params.append('timestamp', new Date().getTime());

        // Log the full URL for debugging
        const url = `${API_URL}?${params.toString()}`;
        console.log('Fetching from URL:', url);

        // Make the request
        const response = await fetch(url);
        
        // Check if response is ok
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        // Parse the response
        const data = await response.json();
        
        // Log the response for debugging
        console.log(`Response for ${action}:`, data);

        // Return empty array if no data
        if (!data || (data.status === 'success' && !data.data)) {
            console.warn(`No data returned for ${action}`);
            return { status: 'success', data: [] };
        }

        return data;
    } catch (error) {
        console.error(`Error fetching ${action}:`, error);
        // Return a structured error response
        return { 
            status: 'error', 
            message: error.message,
            data: [] // Always include an empty data array to prevent undefined
        };
    }
}

// Open and close modals
export function openModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) modal.classList.add('active');
}

export function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) modal.classList.remove('active');
}

// تصدير دالة فتح نافذة تفاصيل المركبة حتى يمكن استخدامها من وحدات أخرى
export function openVehicleDetailsModal(vehicleId) {
    console.log('Opening vehicle details modal for ID:', vehicleId);

    // Get the modal element first
    const modal = document.getElementById('vehicle-details-modal');
    if (!modal) {
        console.error('Vehicle details modal element not found');
        return;
    }

    // Get the details content element
    const detailsContent = modal.querySelector('.modal-body');
    if (!detailsContent) {
        console.error('Modal body element not found');
        return;
    }

    if (!vehicleId) {
        console.error('No vehicle ID provided to openVehicleDetailsModal');
        showNotification('Error: Vehicle ID is missing', 'error');
        return;
    }

    // Try to find the vehicle by ID - more comprehensive search
    const vehicle = vehicles.find(v =>
        v['Vehicle ID'] === vehicleId ||
        v.id === vehicleId ||
        v['Vehicle VIN/SN'] === vehicleId
    );

    if (!vehicle) {
        console.error('Vehicle not found with ID:', vehicleId);
        showNotification(`Vehicle not found with ID: ${vehicleId}`, 'error');
        return;
    }

    console.log('Found vehicle:', vehicle);

    // Rest of the function remains the same
    // تحديث رأس النافذة المنبثقة
    const modalHeader = modal.querySelector('.modal-header');
    if (modalHeader) {
        modalHeader.innerHTML = `
            <div>
                <h3>Vehicle Details</h3>
                <div class="license-number">
                    <i class="fas fa-id-card"></i> ${vehicle['License Plate'] || 'N/A'}
                </div>
                <div id="vehicle-details-tabs">
                    <button class="tab-btn active" data-tab="info"><i class="fas fa-info-circle"></i> Basic Info</button>
                    <button class="tab-btn" data-tab="maintenance"><i class="fas fa-tools"></i> Maintenance</button>
                    <button class="tab-btn" data-tab="service"><i class="fas fa-cog"></i> Service</button>
                    <button class="tab-btn" data-tab="driver"><i class="fas fa-user"></i> Driver</button>
                </div>
            </div>
            <button id="close-vehicle-details-modal" class="close-btn">
                <i class="fas fa-times"></i>
            </button>
        `;
    }

    // تحديد حالة المركبة
    const vehicleStatus = vehicle['Vehicle Status'] || 'inactive';
    const statusClass = vehicleStatus.toLowerCase().includes('active') ? 'active' :
                       vehicleStatus.toLowerCase().includes('maintenance') ? 'maintenance' : 'inactive';

    // حساب تحذيرات الصيانة
    const kmToMaintenance = parseFloat(String(vehicle['Km to next maintenance'] || '0').replace(/,/g, '')) || 0;
    const kmToTires = parseFloat(String(vehicle['Km left for tire change'] || '0').replace(/,/g, '')) || 0;
    const daysToLicense = parseInt(String(vehicle['Days to renew license'] || '0')) || 0;

    // تعيين أيقونات البيانات
    const icons = {
        'License Plate': 'fas fa-id-card',
        'VIN Number': 'fas fa-barcode',
        'Service Type': 'fas fa-tag',
        'Vehicle Type': 'fas fa-truck',
        'Model': 'fas fa-car-side',
        'Color': 'fas fa-palette',
        'Current Location': 'fas fa-map-marker-alt',
        'Current Km': 'fas fa-tachometer-alt',
        'Last Maintenance Km': 'fas fa-wrench',
        'Next Maintenance Km': 'fas fa-calendar-plus',
        'Km to Next Maintenance': 'fas fa-road',
        'Last Maintenance Date': 'fas fa-calendar-check',
        'Last Tire Change Km': 'fas fa-circle-notch',
        'Next Tire Change Km': 'fas fa-cog',
        'Km Left for Tire Change': 'fas fa-tire',
        'Last Tire Change Date': 'fas fa-calendar-day',
        'License Renewal Date': 'fas fa-id-badge',
        'Days to License Renewal': 'fas fa-hourglass-half',
        'Driver Name': 'fas fa-user',
        'Driver Contact': 'fas fa-phone'
    };

    // قيم العرض - تحسين معالجة البيانات
    const currentKm = parseFloat(String(vehicle['Current Km'] || vehicle['Current Kilometers'] || '0').replace(/,/g, '')) || 0;
    const lastMaintenanceKm = parseFloat(String(vehicle['Last Maintenance Km'] || vehicle['Last Maintenance Kilometers'] || '0').replace(/,/g, '')) || 0;
    const nextMaintenanceKm = parseFloat(String(vehicle['Next Maintenance Km'] || vehicle['Next Maintenance Kilometers'] || '0').replace(/,/g, '')) || 0;
    const lastTireKm = parseFloat(String(vehicle['Last tire change Km'] || vehicle['Last Tire Change Kilometers'] || '0').replace(/,/g, '')) || 0;
    const nextTireChangeKm = parseFloat(String(vehicle['Next Tire Change Km'] || vehicle['Next Tire Change Kilometers'] || '0').replace(/,/g, '')) || 0;

    // محتوى التبويبات بالتصميم المحسن
    const infoTabContent = `
        <div class="status-badge ${statusClass}">
            <i class="fas fa-${statusClass === 'active' ? 'check-circle' : statusClass === 'maintenance' ? 'tools' : 'times-circle'}"></i>
            ${vehicle['Vehicle Status'] || 'Unknown Status'}
        </div>
        <div class="preview-details">
            ${createPreviewItem('License Plate', vehicle['License Plate'] || vehicle['Vehicle License'], icons['License Plate'])}
            ${createPreviewItem('Service Type', vehicle['Service Type'] || vehicle['Vehicle Service'], icons['Service Type'])}
            ${createPreviewItem('Vehicle Type', vehicle['Vehicle Type'], icons['Vehicle Type'])}
            ${createPreviewItem('Model', vehicle['Model'] || vehicle['Vehicle Model'], icons['Model'])}
            ${createPreviewItem('Color', vehicle['Color'] || vehicle['Vehicle Color'], icons['Color'])}
            ${createPreviewItem('VIN Number', vehicle['VIN Number'] || vehicle['Vehicle VIN/SN'], icons['VIN Number'])}
            ${createPreviewItem('Current Location', vehicle['Current Location'] || vehicle['Location'], icons['Current Location'])}
        </div>
    `;

    const maintenanceTabContent = `
        <div class="section-header">
            <h4>Maintenance Information</h4>
            <span class="section-subtitle">Last updated: ${vehicle['Last Maintenance Date'] || 'N/A'}</span>
        </div>
        <div class="preview-details">
            ${createPreviewItem('Current Km', formatNumber(currentKm) + ' Km', icons['Current Km'])}
            ${createPreviewItem('Last Maintenance Km', vehicle['Last Maintenance Km'], icons['Last Maintenance Km'])}
            ${createPreviewItem('Next Maintenance Km', vehicle['Next Maintenance Km'], icons['Next Maintenance Km'])}
            ${createPreviewItem('Km to Next Maintenance', formatNumber(kmToMaintenance) + ' Km', icons['Km to Next Maintenance'], kmToMaintenance < 1000 ? 'danger' : kmToMaintenance < 5000 ? 'warning' : '')}
            ${createPreviewItem('Last Maintenance Date', vehicle['Last Maintenance Date'], icons['Last Maintenance Date'])}
        </div>
        ${kmToMaintenance < 5000 ? `
        <div class="progress-wrapper">
            <div class="progress-info">
                <span>Maintenance Progress</span>
                <span>${Math.round((currentKm - lastMaintenanceKm) / (nextMaintenanceKm - lastMaintenanceKm) * 100)}%</span>
            </div>
            <div class="progress">
                <div class="progress-bar ${kmToMaintenance < 1000 ? 'progress-danger' : kmToMaintenance < 5000 ? 'progress-warning' : 'progress-good'}"
                     style="width: ${Math.min(((currentKm - lastMaintenanceKm) / (nextMaintenanceKm - lastMaintenanceKm)) * 100, 100)}%"></div>
            </div>
        </div>
        ` : ''}
    `;

    const serviceTabContent = `
        <div class="section-header">
            <h4>Service Information</h4>
            <span class="section-subtitle">Last updated: ${vehicle['Last tire change Data'] || 'N/A'}</span>
        </div>
        <div class="preview-details">
            ${createPreviewItem('Last Tire Change Km', vehicle['Last tire change Km'] || vehicle['Last Tire Change Kilometers'], icons['Last Tire Change Km'])}
            ${createPreviewItem('Next Tire Change Km', vehicle['Next Tire Change Km'] || vehicle['Next Tire Change Kilometers'], icons['Next Tire Change Km'])}
            ${createPreviewItem('Km Left for Tire Change', formatNumber(kmToTires) + ' Km', icons['Km Left for Tire Change'], kmToTires < 1000 ? 'danger' : kmToTires < 5000 ? 'warning' : '')}
            ${createPreviewItem('Last Tire Change Date', vehicle['Last tire change Date'] || vehicle['Last Tire Change Date'], icons['Last Tire Change Date'])}
            ${createPreviewItem('License Renewal Date', vehicle['License Renewal Date'], icons['License Renewal Date'], daysToLicense < 7 ? 'danger' : daysToLicense < 30 ? 'warning' : '')}
            ${createPreviewItem('Days to License Renewal', daysToLicense + ' Days', icons['Days to License Renewal'], daysToLicense < 7 ? 'danger' : daysToLicense < 30 ? 'warning' : '')}
        </div>
        ${kmToTires < 5000 ? `
        <div class="progress-wrapper">
            <div class="progress-info">
                <span>Tire Change Progress</span>
                <span>${Math.round((currentKm - lastTireKm) / (nextTireChangeKm - lastTireKm) * 100)}%</span>
            </div>
            <div class="progress">
                <div class="progress-bar ${kmToTires < 1000 ? 'progress-danger' : kmToTires < 5000 ? 'progress-warning' : 'progress-good'}"
                     style="width: ${Math.min(((currentKm - lastTireKm) / (nextTireChangeKm - lastTireKm)) * 100, 100)}%"></div>
            </div>
        </div>
        ` : ''}
    `;

    const driverTabContent = `
        <div class="section-header">
            <h4>Driver Information</h4>
        </div>
        <div class="preview-details">
            ${createPreviewItem('Driver Name', vehicle['Driver Name'] || vehicle['Driver Name (EN)'] || vehicle['Driver Name (AR)'], icons['Driver Name'])}
            ${createPreviewItem('Driver Contact', vehicle['Driver Contact'] || vehicle['Driver Phone'] || vehicle['Phone'], icons['Driver Contact'])}
        </div>
        ${vehicle['Driver Name'] ? `
        <div style="text-align: center; padding: 2rem 1.5rem; margin-top: 1rem;">
            <div style="width: 100px; height: 100px; border-radius: 50%; background-color: var(--primary-color); color: white; display: flex; align-items: center; justify-content: center; margin: 0 auto;">
                <i class="fas fa-user" style="font-size: 3rem;"></i>
            </div>
            <h4 style="margin-top: 1.5rem; color: var(--text-color-dark); font-size: 1.3rem;">${vehicle['Driver Name']}</h4>
            <p style="color: var(--text-color-light); font-size: 1.1rem; margin-top: 0.5rem;">${vehicle['Driver Contact'] || 'No contact information'}</p>
        </div>
        ` : `
        <div style="text-align: center; padding: 3rem 1.5rem; color: var(--text-color-light);">
            <i class="fas fa-user-slash" style="font-size: 4rem; opacity: 0.5; margin-bottom: 1.5rem;"></i>
            <p style="font-size: 1.1rem;">No driver assigned to this vehicle.</p>
        </div>
        `}
    `;

    // تنسيق محتوى النافذة المنبثقة مع معرفات محددة وفريدة
    detailsContent.innerHTML = `
        <!-- قسم المعلومات الأساسية -->
        <div id="vehicle-tab-info" class="vehicle-details-section active">
            <h3><i class="fas fa-info-circle"></i> Vehicle Information</h3>
            <div class="section-wrapper">
                ${infoTabContent}
            </div>
        </div>

        <!-- قسم الصيانة -->
        <div id="vehicle-tab-maintenance" class="vehicle-details-section">
            <h3><i class="fas fa-tools"></i> Maintenance Status</h3>
            <div class="section-wrapper">
                ${maintenanceTabContent}
            </div>
        </div>

        <!-- قسم الخدمات -->
        <div id="vehicle-tab-service" class="vehicle-details-section">
            <h3><i class="fas fa-cog"></i> Service Information</h3>
            <div class="section-wrapper">
                ${serviceTabContent}
            </div>
        </div>

        <!-- قسم السائق -->
        <div id="vehicle-tab-driver" class="vehicle-details-section">
            <h3><i class="fas fa-user"></i> Driver Information</h3>
            <div class="section-wrapper">
                ${driverTabContent}
            </div>
        </div>

        <!-- قسم الإجراءات -->
        <div id="vehicle-details-actions">
            <button class="btn btn-primary print-vehicle" data-vehicle-id="${vehicleId}">
                <i class="fas fa-print"></i> Print Details
            </button>
            <button class="btn btn-secondary close-vehicle" onclick="closeModal('vehicle-details-modal')">
                <i class="fas fa-times"></i> Close
            </button>
        </div>
    `;

    // تحديث عرض البيانات في النافذة المنبثقة
    updateVehicleDetailsDisplay(vehicle);

    // إضافة مستمعات الأحداث لأزرار التبويب
    const tabButtons = modal.querySelectorAll('.tab-btn');
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            console.log('Tab button clicked:', this.getAttribute('data-tab')); // تسجيل للتشخيص

            // إزالة الفئة النشطة من جميع الأزرار
            tabButtons.forEach(btn => btn.classList.remove('active'));

            // إضافة الفئة النشطة للزر المحدد
            this.classList.add('active');

            // إخفاء جميع الأقسام
            const sections = detailsContent.querySelectorAll('.vehicle-details-section');
            sections.forEach(section => section.classList.remove('active'));

            // إظهار القسم المحدد
            const tabId = this.getAttribute('data-tab');
            const activeSection = document.getElementById(`vehicle-tab-${tabId}`);
            console.log('Looking for tab section:', `vehicle-tab-${tabId}`);

            if (activeSection) {
                console.log('Found section, activating:', activeSection.id);
                activeSection.classList.add('active');
            } else {
                console.warn('Section not found for tab:', tabId);
            }
        });
    });

    // إضافة مستمع حدث لزر الإغلاق
    const closeBtn = modal.querySelector('#close-vehicle-details-modal');
    if (closeBtn) {
        closeBtn.addEventListener('click', () => closeModal('vehicle-details-modal'));
    }

    // فتح النافذة المنبثقة
    openModal('vehicle-details-modal');

    // التأكد من أن التبويب الأول نشط
    const firstTab = modal.querySelector('.tab-btn[data-tab="info"]');
    if (firstTab) {
        firstTab.click();
    }
}

// دالة مساعدة لإنشاء عنصر معلومات في نمط Preview
function createPreviewItem(label, value, iconClass, alertClass = '') {
    let itemClass = 'preview-item';
    if (alertClass === 'danger') {
        itemClass += 'alert-danger';
    } else if (alertClass === 'warning') {
        itemClass += 'alert-warning';
    }

    return `
        <div class="${itemClass}">
            <i class="${iconClass || 'fas fa-info-circle'}"></i>
            <div>
                <span class="preview-label">${label}</span>
                <span class="preview-value">${formatValue(value, label)}</span>
            </div>
        </div>
    `;
}

// دالة لتنسيق القيم قبل عرضها
function formatValue(value, label) {
    if (value === undefined || value === null || value === '') {
        return 'N/A';
    }

    // تجنب تنسيق الأرقام لحقل Model
    if (label !== 'Model' && (typeof value === 'number' || !isNaN(value))) {
        return formatNumber(value);
    }

    // تنسيق التواريخ
    if (typeof value === 'string' && value.includes('-') && !isNaN(Date.parse(value.replace(/-/g, '/')))) {
        return new Date(value.replace(/-/g, '/')).toLocaleDateString('ar-EG');
    }

    return value;
}

// دالة لطباعة تفاصيل المركبة بالكامل
export function printVehicleDetails(vehicleId) {
    // البحث عن المركبة بواسطة المعرف
    const vehicle = vehicles.find(v =>
        v['Vehicle ID'] === vehicleId ||
        v.id === vehicleId ||
        v['Vehicle VIN/SN'] === vehicleId
    );

    if (!vehicle) {
        console.error('Vehicle not found with ID:', vehicleId);
        showNotification('Error: Vehicle not found', 'error');
        return;
    }

    // إنشاء نافذة طباعة جديدة
    const printWindow = window.open('', '_blank');
    if (!printWindow) {
        alert('Please allow pop-ups to print vehicle details');
        return;
    }

    // تحديد حالة المركبة
    const vehicleStatus = vehicle['Vehicle Status'] || 'inactive';
    const statusClass = vehicleStatus.toLowerCase().includes('active') ? 'active' :
                       vehicleStatus.toLowerCase().includes('maintenance') ? 'maintenance' : 'inactive';

    // حساب تحذيرات الصيانة
    const currentKm = parseFloat(String(vehicle['Current Km'] || '0').replace(/,/g, '')) || 0;
    const lastMaintenanceKm = parseFloat(String(vehicle['Last Maintenance Km'] || '0').replace(/,/g, '')) || 0;
    const nextMaintenanceKm = parseFloat(String(vehicle['Next Maintenance Km'] || '0').replace(/,/g, '')) || 0;
    const kmToMaintenance = parseFloat(String(vehicle['Km to next maintenance'] || '0').replace(/,/g, '')) || 0;

    const lastTireKm = parseFloat(String(vehicle['Last tire change Km'] || '0').replace(/,/g, '')) || 0;
    const nextTireKm = parseFloat(String(vehicle['Next Tire Change Km'] || '0').replace(/,/g, '')) || 0;
    const kmToTires = parseFloat(String(vehicle['Km left for tire change'] || '0').replace(/,/g, '')) || 0;
    const daysToLicense = parseInt(String(vehicle['Days to renew license'] || '0')) || 0;

    // الحصول على معلومات السائق
    const driverName = vehicle['Driver Name'] || vehicle['Driver Name (EN)'] || vehicle['Driver Name (AR)'];
    const driverContact = vehicle['Driver Contact'] || vehicle['Driver Phone'] || vehicle['Phone'];

    // إنشاء محتوى HTML للطباعة
    const printContent = `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Vehicle Details - ${vehicle['License Plate'] || vehicle.id}</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                line-height: 1.6;
                color: #333;
                margin: 0;
                padding: 20px;
            }
            .print-header {
                text-align: center;
                margin-bottom: 20px;
                padding-bottom: 10px;
                border-bottom: 2px solid #0057ff;
            }
            .print-header h1 {
                color: #0057ff;
                margin: 0;
                font-size: 24px;
            }
            .license-number {
                font-size: 18px;
                font-weight: bold;
                margin: 10px 0;
            }
            .print-section {
                margin-bottom: 30px;
                page-break-inside: avoid;
            }
            .print-section h2 {
                color: #0057ff;
                border-bottom: 1px solid #ddd;
                padding-bottom: 5px;
                margin-top: 0;
            }
            .details-grid {
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                gap: 15px;
            }
            .detail-item {
                margin-bottom: 10px;
            }
            .detail-label {
                font-weight: bold;
                display: block;
            }
            .detail-value {
                display: block;
            }
            .status-badge {
                display: inline-block;
                padding: 5px 10px;
                border-radius: 4px;
                font-weight: bold;
                margin-bottom: 15px;
            }
            .active {
                background-color: #e6f7ee;
                color: #00b478;
            }
            .maintenance {
                background-color: #fff8e6;
                color: #ffa500;
            }
            .inactive {
                background-color: #ffe6e6;
                color: #ff3c4c;
            }
            .warning-text {
                color: #ffa500;
            }
            .danger-text {
                color: #ff3c4c;
            }
            .footer {
                margin-top: 30px;
                text-align: center;
                font-size: 12px;
                color: #666;
                border-top: 1px solid #ddd;
                padding-top: 10px;
            }
            @media print {
                body {
                    padding: 0;
                    margin: 15px;
                }
                .print-section {
                    page-break-inside: avoid;
                }
            }
        </style>
    </head>
    <body>
        <div class="print-header">
            <h1>Vehicle Details Report</h1>
            <div class="license-number">
                <i class="fas fa-id-card"></i> ${vehicle['License Plate'] || 'N/A'}
            </div>
            <div>Report Date: ${new Date().toLocaleDateString()}</div>
        </div>

        <!-- Basic Information Section -->
        <div class="print-section">
            <h2>Basic Information</h2>
            <div class="status-badge ${statusClass}">
                ${vehicle['Vehicle Status'] || 'Unknown Status'}
            </div>
            <div class="details-grid">
                <div class="detail-item">
                    <span class="detail-label">License Plate:</span>
                    <span class="detail-value">${vehicle['License Plate'] || 'N/A'}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Service Type:</span>
                    <span class="detail-value">${vehicle['Service Type'] || 'N/A'}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Vehicle Type:</span>
                    <span class="detail-value">${vehicle['Vehicle Type'] || 'N/A'}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Model:</span>
                    <span class="detail-value">${vehicle['Model'] || 'N/A'}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Color:</span>
                    <span class="detail-value">${vehicle['Color'] || 'N/A'}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">VIN Number:</span>
                    <span class="detail-value">${vehicle['VIN Number'] || vehicle['Vehicle VIN/SN'] || 'N/A'}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Current Location:</span>
                    <span class="detail-value">${vehicle['Current Location'] || vehicle['current location'] || 'N/A'}</span>
                </div>
            </div>
        </div>

        <!-- Maintenance Information Section -->
        <div class="print-section">
            <h2>Maintenance Information</h2>
            <div class="details-grid">
                <div class="detail-item">
                    <span class="detail-label">Current Kilometers:</span>
                    <span class="detail-value">${formatNumber(currentKm)} Km</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Last Maintenance Km:</span>
                    <span class="detail-value">${formatNumber(lastMaintenanceKm)} Km</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Next Maintenance Km:</span>
                    <span class="detail-value">${formatNumber(nextMaintenanceKm)} Km</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Km to Next Maintenance:</span>
                    <span class="detail-value ${kmToMaintenance < 1000 ? 'danger-text' : kmToMaintenance < 5000 ? 'warning-text' : ''}">
                        ${formatNumber(kmToMaintenance)} Km
                    </span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Last Maintenance Date:</span>
                    <span class="detail-value">${vehicle['Last Maintenance Date'] || 'N/A'}</span>
                </div>
            </div>
        </div>

        <!-- Service Information Section -->
        <div class="print-section">
            <h2>Service Information</h2>
            <div class="details-grid">
                <div class="detail-item">
                    <span class="detail-label">Last Tire Change Km:</span>
                    <span class="detail-value">${formatNumber(lastTireKm)} Km</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Next Tire Change Km:</span>
                    <span class="detail-value">${formatNumber(nextTireKm)} Km</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Km Left for Tire Change:</span>
                    <span class="detail-value ${kmToTires < 1000 ? 'danger-text' : kmToTires < 5000 ? 'warning-text' : ''}">
                        ${formatNumber(kmToTires)} Km
                    </span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Last Tire Change Date:</span>
                    <span class="detail-value">${vehicle['Last tire change Date'] || vehicle['Last Tire Change Date'] || 'N/A'}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">License Renewal Date:</span>
                    <span class="detail-value">${vehicle['License Renewal Date'] || 'N/A'}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Days to License Renewal:</span>
                    <span class="detail-value ${daysToLicense < 7 ? 'danger-text' : daysToLicense < 30 ? 'warning-text' : ''}">
                        ${daysToLicense} Days
                    </span>
                </div>
            </div>
        </div>

        <!-- Driver Information Section -->
        <div class="print-section">
            <h2>Driver Information</h2>
            ${driverName ? `
            <div class="details-grid">
                <div class="detail-item">
                    <span class="detail-label">Driver Name:</span>
                    <span class="detail-value">${driverName}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Driver Contact:</span>
                    <span class="detail-value">${driverContact || 'N/A'}</span>
                </div>
            </div>
            ` : `
            <p>No driver assigned to this vehicle.</p>
            `}
        </div>

        <div class="footer">
            <p>This report was generated on ${new Date().toLocaleString()} by Fleet Management System</p>
        </div>
    </body>
    </html>
    `;

    // كتابة المحتوى إلى نافذة الطباعة
    printWindow.document.open();
    printWindow.document.write(printContent);
    printWindow.document.close();

    // الانتظار حتى يتم تحميل المحتوى ثم طباعته
    printWindow.onload = function() {
        printWindow.print();
        // لا نغلق النافذة بعد الطباعة لإتاحة الفرصة للمستخدم لمراجعة المحتوى
    };
}

// دالة لتحديث عرض بيانات المركبة
function updateVehicleDetailsDisplay(vehicle) {
    // تحديث معلومات المركبة
    const vehicleInfo = document.getElementById('vehicle-tab-info');
    if (vehicleInfo) {
        vehicleInfo.querySelector('.section-wrapper').innerHTML = `
            <div class="vehicle-status ${vehicle['Vehicle Status']?.toLowerCase() || 'unknown'}">
                ${vehicle['Vehicle Status'] || 'Unknown Status'}
            </div>
            <div class="preview-details">
                ${createPreviewItem('License Plate', vehicle['License Plate'], 'fas fa-id-card')}
                ${createPreviewItem('Service Type', vehicle['Service Type'], 'fas fa-cogs')}
                ${createPreviewItem('Vehicle Type', vehicle['Vehicle Type'], 'fas fa-car')}
                ${createPreviewItem('Model', vehicle['Model'], 'fas fa-calendar-alt')}
                ${createPreviewItem('Color', vehicle['Color'], 'fas fa-palette')}
                ${createPreviewItem('VIN Number', vehicle['VIN Number'], 'fas fa-barcode')}
                ${createPreviewItem('Current Location', vehicle['Current Location'], 'fas fa-map-marker-alt')}
            </div>
        `;
    }

    // تحديث معلومات الصيانة
    const maintenanceInfo = document.getElementById('vehicle-tab-maintenance');
    if (maintenanceInfo) {
        const currentKm = parseFloat(String(vehicle['Current Km'] || '0').replace(/,/g, '')) || 0;
        const lastMaintenanceKm = parseFloat(String(vehicle['Last Maintenance Km'] || '0').replace(/,/g, '')) || 0;
        const nextMaintenanceKm = parseFloat(String(vehicle['Next Maintenance Km'] || '0').replace(/,/g, '')) || 0;
        const kmToMaintenance = parseFloat(vehicle['Km to next maintenance'] || 0);

        maintenanceInfo.querySelector('.section-wrapper').innerHTML = `
            <div class="section-header">
                <h4>Maintenance Information</h4>
                <span class="section-subtitle">Last updated: ${formatValue(vehicle['Last Maintenance Date'])}</span>
            </div>
            <div class="preview-details">
                ${createPreviewItem('Current Km', currentKm + ' Km', 'fas fa-tachometer-alt')}
                ${createPreviewItem('Last Maintenance Km', lastMaintenanceKm + ' Km', 'fas fa-history')}
                ${createPreviewItem('Next Maintenance Km', nextMaintenanceKm + ' Km', 'fas fa-forward')}
                ${createPreviewItem('Km to Next Maintenance', kmToMaintenance + ' Km', 'fas fa-road',
                    kmToMaintenance < 1000 ? 'danger' : kmToMaintenance < 5000 ? 'warning' : '')}
            </div>
        `;
    }

    // تحديث معلومات الخدمة
    const serviceInfo = document.getElementById('vehicle-tab-service');
    if (serviceInfo) {
        const lastTireKm = parseFloat(String(vehicle['Last tire change Km'] || '0').replace(/,/g, '')) || 0;
        const nextTireKm = parseFloat(String(vehicle['Next Tire Change Km'] || '0').replace(/,/g, '')) || 0;
        const kmToTires = parseFloat(String(vehicle['Km left for tire change'] || '0').replace(/,/g, '')) || 0;
        const daysToLicense = parseInt(String(vehicle['Days to renew license'] || '0')) || 0;

        serviceInfo.querySelector('.section-wrapper').innerHTML = `
            <div class="section-header">
                <h4>Service Information</h4>
                <span class="section-subtitle">Last updated: ${formatValue(vehicle['Last tire change Data'])}</span>
            </div>
            <div class="preview-details">
                ${createPreviewItem('Last Tire Change Km', lastTireKm + ' Km', 'fas fa-tire')}
                ${createPreviewItem('Next Tire Change Km', nextTireKm + ' Km', 'fas fa-tire-flat')}
                ${createPreviewItem('Km Left for Tire Change', kmToTires + ' Km', 'fas fa-ruler-horizontal',
                    kmToTires < 1000 ? 'danger' : kmToTires < 5000 ? 'warning' : '')}
                ${createPreviewItem('License Renewal Date', vehicle['License Renewal Date'], 'fas fa-calendar-check',
                    daysToLicense < 7 ? 'danger' : daysToLicense < 30 ? 'warning' : '')}
                ${createPreviewItem('Days to License Renewal', daysToLicense + ' Days', 'fas fa-hourglass-half',
                    daysToLicense < 7 ? 'danger' : daysToLicense < 30 ? 'warning' : '')}
            </div>
        `;
    }

    // تحديث معلومات السائق
    const driverInfo = document.getElementById('vehicle-tab-driver');
    if (driverInfo) {
        const driverName = vehicle['Driver Name'] || vehicle['Driver Name (EN)'] || vehicle['Driver Name (AR)'];
        const driverContact = vehicle['Driver Contact'] || vehicle['Driver Phone'] || vehicle['Phone'];

        driverInfo.querySelector('.section-wrapper').innerHTML = `
            <div class="section-header">
                <h4>Driver Information</h4>
            </div>
            <div class="preview-details">
                ${createPreviewItem('Driver Name', driverName, 'fas fa-user')}
                ${createPreviewItem('Driver Contact', driverContact, 'fas fa-phone')}
            </div>
            ${driverName ? `
                <div class="driver-profile">
                    <div class="driver-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <h4 class="driver-name">${driverName}</h4>
                    <p class="driver-contact">${driverContact || 'No contact information'}</p>
                </div>
            ` : `
                <div class="no-driver">
                    <i class="fas fa-user-slash"></i>
                    <p>No driver assigned to this vehicle.</p>
                </div>
            `}
        `;
    }
}

// Helper function to format numbers with commas as thousands separators
function formatNumber(number) {
    return String(number || 0).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
}

// Create dark mode toggle button
function createDarkModeToggle() {
    const button = document.createElement('button');
    button.className = 'dark-mode-toggle';
    button.innerHTML = '<i class="fas fa-moon"></i>';
    button.title = 'Toggle Dark Mode';
    button.addEventListener('click', toggleDarkMode);
    document.body.appendChild(button);
}

// Toggle dark mode
function toggleDarkMode() {
    // إضافة تأثير انتقالي أسرع للتبديل
    document.body.style.transition = 'background 0.3s ease';

    const isDarkMode = document.body.classList.toggle('dark-mode');
    localStorage.setItem('darkMode', isDarkMode ? 'enabled' : 'disabled');

    // تحديث أيقونة الزر فوراً بدون تأخير
    const toggler = document.querySelector('.dark-mode-toggle');
    if (toggler) {
        toggler.innerHTML = isDarkMode ? '<i class="fas fa-sun"></i>' : '<i class="fas fa-moon"></i>';
    }

    // ضمان رؤية عنوان الصفحة الحالية
    const pageTitle = document.getElementById('current-page-title');
    if (pageTitle) {
        pageTitle.style.color = isDarkMode ? '#ffffff' : '';
    }

    // تأجيل تحديث الرسوم البيانية للتأكد من اكتمال التبديل أولاً
    setTimeout(() => {
        // تحديث الرسوم البيانية إذا كانت موجودة
        if (Object.keys(charts).length > 0) {
            updateChartsForDarkMode(isDarkMode);
        }

        // إزالة التأثير الانتقالي بعد الانتهاء
        document.body.style.transition = '';
    }, 50);

    // عرض إشعار بشكل اختياري (يمكن إزالته لتحسين الأداء)
    // showNotification(isDarkMode ? 'Dark mode enabled' : 'Light mode enabled', 'success');
}

// Update charts for dark mode
function updateChartsForDarkMode(isDarkMode) {
    // Update Chart.js default colors for dark mode
    Chart.defaults.color = isDarkMode ? '#e0e0e0' : '#666';
    Chart.defaults.borderColor = isDarkMode ? '#444' : '#ddd';

    // Refresh all charts
    Object.keys(charts).forEach(chartKey => {
        const chart = charts[chartKey];
        if (chart && typeof chart === 'object' && chart.update) {
            // Update the chart's options for dark mode
            if (chart.options.scales && chart.options.scales.y) {
                chart.options.scales.y.grid.color = isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)';
                if (chart.options.scales.y.title) {
                    chart.options.scales.y.title.color = isDarkMode ? '#e0e0e0' : '#666';
                }
                chart.options.scales.y.ticks.color = isDarkMode ? '#e0e0e0' : '#666';
            }
            if (chart.options.scales && chart.options.scales.x) {
                chart.options.scales.x.grid.color = isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)';
                if (chart.options.scales.x.title) {
                    chart.options.scales.x.title.color = isDarkMode ? '#e0e0e0' : '#666';
                }
                chart.options.scales.x.ticks.color = isDarkMode ? '#e0e0e0' : '#666';
            }

            // Update legend colors
            if (chart.options.plugins && chart.options.plugins.legend) {
                chart.options.plugins.legend.labels.color = isDarkMode ? '#e0e0e0' : '#666';
            }

            // Update the chart
            chart.update();
        }
    });

    // If we're on the dashboard page, refresh it
    if (document.getElementById('dashboard-page').classList.contains('active')) {
        setTimeout(() => updateDashboard(), 100);
    }
}

// Check user's dark mode preference
function checkDarkModePreference() {
    // Check localStorage preference
    const darkModePreference = localStorage.getItem('darkMode');

    // Set appropriate class to show correct logo and update styles
    if (darkModePreference === 'enabled') {
        document.body.classList.add('dark-mode');
        const toggler = document.querySelector('.dark-mode-toggle');
        if (toggler) {
            toggler.innerHTML = '<i class="fas fa-sun"></i>';
        }

        // Ensure current page title is visible in dark mode
        const pageTitle = document.getElementById('current-page-title');
        if (pageTitle) {
            pageTitle.style.color = '#ffffff';
        }

        // Update chart defaults for dark mode
        Chart.defaults.color = '#e0e0e0';
        Chart.defaults.borderColor = '#444';
    }

    // Also check for system preference if no localStorage setting
    if (darkModePreference === null &&
        window.matchMedia &&
        window.matchMedia('(prefers-color-scheme: dark)').matches) {
        document.body.classList.add('dark-mode');
        localStorage.setItem('darkMode', 'enabled');
        const toggler = document.querySelector('.dark-mode-toggle');
        if (toggler) {
            toggler.innerHTML = '<i class="fas fa-sun"></i>';
        }

        // Update chart defaults for dark mode
        Chart.defaults.color = '#e0e0e0';
        Chart.defaults.borderColor = '#444';
    }
}

// Create notification system
function createNotificationSystem() {
    // Create notification container if it doesn't exist
    if (!document.getElementById('notification-container')) {
        const container = document.createElement('div');
        container.id = 'notification-container';
        document.body.appendChild(container);
    }
}

// Show notification
export function showNotification(message, type = 'info', duration = 5000) {
    const container = document.getElementById('notification-container');
    if (!container) return;

    // Create notification
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;

    // Add appropriate icon based on type
    let icon = 'info-circle';
    switch(type) {
        case 'success': icon = 'check-circle'; break;
        case 'error': icon = 'exclamation-circle'; break;
        case 'warning': icon = 'exclamation-triangle'; break;
    }

    notification.innerHTML = `
        <div class="notification-icon">
            <i class="fas fa-${icon}"></i>
        </div>
        <div class="notification-content">
            <p>${message}</p>
        </div>
        <button class="notification-close">
            <i class="fas fa-times"></i>
        </button>
    `;

    // Add close button event
    const closeBtn = notification.querySelector('.notification-close');
    if (closeBtn) {
        closeBtn.addEventListener('click', () => {
            notification.classList.add('closing');
            setTimeout(() => {
                container.removeChild(notification);
            }, 300); // Animation time
        });
    }

    // Add to container
    container.appendChild(notification);

    // Automatically remove after duration
    setTimeout(() => {
        if (container.contains(notification)) {
            notification.classList.add('closing');
            setTimeout(() => {
                if (container.contains(notification)) {
                    container.removeChild(notification);
                }
            }, 300); // Animation time
        }
    }, duration);

    // Start animation
    setTimeout(() => {
        notification.classList.add('show');
    }, 10);
}

/**
 * Application Store - مخزن حالة التطبيق
 * يوفر نظام إدارة حالة مبسط مع subscribe/setState
 */

export class Store {
    constructor(initialState = {}) {
        this.state = { ...initialState };
        this.subscribers = new Map();
        this.middleware = [];
        this.history = [];
        this.maxHistorySize = 50;
    }

    /**
     * الحصول على الحالة الحالية
     */
    getState() {
        return { ...this.state };
    }

    /**
     * تحديث الحالة
     */
    setState(updates, actionType = 'UPDATE') {
        const prevState = { ...this.state };
        
        // تطبيق التحديثات
        if (typeof updates === 'function') {
            this.state = { ...this.state, ...updates(this.state) };
        } else {
            this.state = { ...this.state, ...updates };
        }

        // حفظ في التاريخ
        this.addToHistory(prevState, this.state, actionType);

        // تطبيق الـ middleware
        this.applyMiddleware(prevState, this.state, actionType);

        // إشعار المشتركين
        this.notifySubscribers(prevState, this.state, actionType);
    }

    /**
     * الاشتراك في تغييرات الحالة
     */
    subscribe(selector, callback) {
        const subscriberId = Date.now() + Math.random();
        
        this.subscribers.set(subscriberId, {
            selector: selector || ((state) => state),
            callback,
            lastValue: selector ? selector(this.state) : this.state
        });

        // إرجاع دالة لإلغاء الاشتراك
        return () => {
            this.subscribers.delete(subscriberId);
        };
    }

    /**
     * إشعار المشتركين
     */
    notifySubscribers(prevState, newState, actionType) {
        this.subscribers.forEach((subscriber, id) => {
            try {
                const newValue = subscriber.selector(newState);
                const hasChanged = JSON.stringify(newValue) !== JSON.stringify(subscriber.lastValue);
                
                if (hasChanged) {
                    subscriber.lastValue = newValue;
                    subscriber.callback(newValue, prevState, actionType);
                }
            } catch (error) {
                console.error(`Subscriber ${id} error:`, error);
            }
        });
    }

    /**
     * إضافة middleware
     */
    addMiddleware(middlewareFunction) {
        this.middleware.push(middlewareFunction);
    }

    /**
     * تطبيق middleware
     */
    applyMiddleware(prevState, newState, actionType) {
        this.middleware.forEach(middleware => {
            try {
                middleware(prevState, newState, actionType);
            } catch (error) {
                console.error('Middleware error:', error);
            }
        });
    }

    /**
     * إضافة إلى التاريخ
     */
    addToHistory(prevState, newState, actionType) {
        this.history.push({
            timestamp: Date.now(),
            actionType,
            prevState: { ...prevState },
            newState: { ...newState }
        });

        // الحفاظ على حجم التاريخ
        if (this.history.length > this.maxHistorySize) {
            this.history.shift();
        }
    }

    /**
     * الحصول على التاريخ
     */
    getHistory() {
        return [...this.history];
    }

    /**
     * مسح التاريخ
     */
    clearHistory() {
        this.history = [];
    }

    /**
     * إعادة تعيين الحالة
     */
    reset(newState = {}) {
        this.setState(newState, 'RESET');
    }
}

// الحالة الأولية للتطبيق
const initialState = {
    // بيانات المستخدم
    user: {
        currentUser: null,
        isAuthenticated: false,
        permissions: []
    },

    // بيانات التطبيق
    app: {
        currentPage: 'dashboard',
        isLoading: false,
        notifications: [],
        theme: 'light'
    },

    // بيانات الأسطول
    fleet: {
        vehicles: [],
        drivers: [],
        maintenanceRecords: [],
        fuelRecords: [],
        users: []
    },

    // إعدادات واجهة المستخدم
    ui: {
        sidebarCollapsed: false,
        selectedVehicle: null,
        filters: {},
        sortBy: null
    }
};

// إنشاء مخزن التطبيق الرئيسي
const appStore = new Store(initialState);

// إضافة middleware للتسجيل
appStore.addMiddleware((prevState, newState, actionType) => {
    if (process.env.NODE_ENV === 'development') {
        console.log(`Store Action: ${actionType}`, {
            prevState,
            newState,
            timestamp: new Date().toISOString()
        });
    }
});

// إضافة middleware لحفظ بعض البيانات في localStorage
appStore.addMiddleware((prevState, newState, actionType) => {
    try {
        // حفظ إعدادات المستخدم
        if (newState.ui !== prevState.ui) {
            localStorage.setItem('fleet_ui_settings', JSON.stringify(newState.ui));
        }

        // حفظ موضوع التطبيق
        if (newState.app.theme !== prevState.app.theme) {
            localStorage.setItem('fleet_theme', newState.app.theme);
            document.body.className = newState.app.theme === 'dark' ? 'dark-mode' : '';
        }
    } catch (error) {
        console.error('LocalStorage middleware error:', error);
    }
});

// استرجاع الإعدادات المحفوظة
try {
    const savedUISettings = localStorage.getItem('fleet_ui_settings');
    const savedTheme = localStorage.getItem('fleet_theme');
    
    if (savedUISettings) {
        appStore.setState({
            ui: { ...appStore.getState().ui, ...JSON.parse(savedUISettings) }
        }, 'RESTORE_UI_SETTINGS');
    }
    
    if (savedTheme) {
        appStore.setState({
            app: { ...appStore.getState().app, theme: savedTheme }
        }, 'RESTORE_THEME');
    }
} catch (error) {
    console.error('Failed to restore settings:', error);
}

// دوال مساعدة للتفاعل مع المخزن
export const storeActions = {
    // إجراءات المستخدم
    setUser(user) {
        appStore.setState({
            user: {
                currentUser: user,
                isAuthenticated: !!user,
                permissions: user?.permissions || []
            }
        }, 'SET_USER');
    },

    logout() {
        appStore.setState({
            user: {
                currentUser: null,
                isAuthenticated: false,
                permissions: []
            }
        }, 'LOGOUT');
    },

    // إجراءات التطبيق
    setCurrentPage(page) {
        appStore.setState({
            app: { ...appStore.getState().app, currentPage: page }
        }, 'SET_CURRENT_PAGE');
    },

    setLoading(isLoading) {
        appStore.setState({
            app: { ...appStore.getState().app, isLoading }
        }, 'SET_LOADING');
    },

    addNotification(notification) {
        const notifications = [...appStore.getState().app.notifications, {
            id: Date.now(),
            timestamp: new Date(),
            ...notification
        }];
        
        appStore.setState({
            app: { ...appStore.getState().app, notifications }
        }, 'ADD_NOTIFICATION');
    },

    removeNotification(notificationId) {
        const notifications = appStore.getState().app.notifications
            .filter(n => n.id !== notificationId);
        
        appStore.setState({
            app: { ...appStore.getState().app, notifications }
        }, 'REMOVE_NOTIFICATION');
    },

    // إجراءات البيانات
    setVehicles(vehicles) {
        appStore.setState({
            fleet: { ...appStore.getState().fleet, vehicles }
        }, 'SET_VEHICLES');
    },

    setDrivers(drivers) {
        appStore.setState({
            fleet: { ...appStore.getState().fleet, drivers }
        }, 'SET_DRIVERS');
    },

    setMaintenanceRecords(maintenanceRecords) {
        appStore.setState({
            fleet: { ...appStore.getState().fleet, maintenanceRecords }
        }, 'SET_MAINTENANCE_RECORDS');
    },

    // إجراءات واجهة المستخدم
    toggleSidebar() {
        appStore.setState({
            ui: { 
                ...appStore.getState().ui, 
                sidebarCollapsed: !appStore.getState().ui.sidebarCollapsed 
            }
        }, 'TOGGLE_SIDEBAR');
    },

    setSelectedVehicle(vehicle) {
        appStore.setState({
            ui: { ...appStore.getState().ui, selectedVehicle: vehicle }
        }, 'SET_SELECTED_VEHICLE');
    },

    setTheme(theme) {
        appStore.setState({
            app: { ...appStore.getState().app, theme }
        }, 'SET_THEME');
    }
};

// دوال مساعدة للاشتراك في أجزاء محددة من الحالة
export const storeSelectors = {
    getCurrentUser: (state) => state.user.currentUser,
    isAuthenticated: (state) => state.user.isAuthenticated,
    getCurrentPage: (state) => state.app.currentPage,
    isLoading: (state) => state.app.isLoading,
    getVehicles: (state) => state.fleet.vehicles,
    getDrivers: (state) => state.fleet.drivers,
    getNotifications: (state) => state.app.notifications,
    getTheme: (state) => state.app.theme
};

export default appStore;

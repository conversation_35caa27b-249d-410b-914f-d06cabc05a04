/**
 * Store Actions - إجراءات المخزن
 * يحتوي على جميع الإجراءات المتاحة لتحديث حالة التطبيق
 */

import appStore from './store.js';
import { showNotification } from '../utils/utility.js';

/**
 * إجراءات المستخدم والمصادقة
 */
export const userActions = {
    /**
     * تسجيل دخول المستخدم
     */
    login(user) {
        appStore.setState({
            user: {
                currentUser: user,
                isAuthenticated: true,
                permissions: user?.permissions || [],
                loginTime: new Date().toISOString()
            }
        }, 'USER_LOGIN');
        
        showNotification(`مرحباً ${user.name}`, 'success');
    },

    /**
     * تسجيل خروج المستخدم
     */
    logout() {
        appStore.setState({
            user: {
                currentUser: null,
                isAuthenticated: false,
                permissions: [],
                loginTime: null
            }
        }, 'USER_LOGOUT');
        
        showNotification('تم تسجيل الخروج بنجاح', 'info');
    },

    /**
     * تحديث بيانات المستخدم
     */
    updateUser(userData) {
        const currentState = appStore.getState();
        appStore.setState({
            user: {
                ...currentState.user,
                currentUser: {
                    ...currentState.user.currentUser,
                    ...userData
                }
            }
        }, 'USER_UPDATE');
    },

    /**
     * تحديث تفضيلات المستخدم
     */
    updatePreferences(preferences) {
        const currentState = appStore.getState();
        appStore.setState({
            user: {
                ...currentState.user,
                preferences: {
                    ...currentState.user.preferences,
                    ...preferences
                }
            }
        }, 'USER_PREFERENCES_UPDATE');
    }
};

/**
 * إجراءات التطبيق العامة
 */
export const appActions = {
    /**
     * تعيين الصفحة الحالية
     */
    setCurrentPage(page) {
        appStore.setState({
            app: {
                ...appStore.getState().app,
                currentPage: page
            }
        }, 'APP_SET_PAGE');
    },

    /**
     * تعيين حالة التحميل
     */
    setLoading(isLoading, message = '') {
        appStore.setState({
            app: {
                ...appStore.getState().app,
                isLoading,
                loadingMessage: message
            }
        }, 'APP_SET_LOADING');
    },

    /**
     * إضافة إشعار
     */
    addNotification(notification) {
        const currentState = appStore.getState();
        const newNotification = {
            id: Date.now() + Math.random(),
            timestamp: new Date().toISOString(),
            type: 'info',
            duration: 5000,
            ...notification
        };

        appStore.setState({
            app: {
                ...currentState.app,
                notifications: [...currentState.app.notifications, newNotification]
            }
        }, 'APP_ADD_NOTIFICATION');

        // إزالة تلقائية بعد المدة المحددة
        if (newNotification.duration > 0) {
            setTimeout(() => {
                this.removeNotification(newNotification.id);
            }, newNotification.duration);
        }
    },

    /**
     * إزالة إشعار
     */
    removeNotification(notificationId) {
        const currentState = appStore.getState();
        appStore.setState({
            app: {
                ...currentState.app,
                notifications: currentState.app.notifications.filter(n => n.id !== notificationId)
            }
        }, 'APP_REMOVE_NOTIFICATION');
    },

    /**
     * مسح جميع الإشعارات
     */
    clearNotifications() {
        appStore.setState({
            app: {
                ...appStore.getState().app,
                notifications: []
            }
        }, 'APP_CLEAR_NOTIFICATIONS');
    },

    /**
     * إضافة خطأ
     */
    addError(error) {
        const currentState = appStore.getState();
        const newError = {
            id: Date.now() + Math.random(),
            timestamp: new Date().toISOString(),
            message: error.message || error,
            stack: error.stack,
            ...error
        };

        appStore.setState({
            app: {
                ...currentState.app,
                errors: [...currentState.app.errors, newError]
            }
        }, 'APP_ADD_ERROR');
    },

    /**
     * مسح الأخطاء
     */
    clearErrors() {
        appStore.setState({
            app: {
                ...appStore.getState().app,
                errors: []
            }
        }, 'APP_CLEAR_ERRORS');
    },

    /**
     * تحديث حالة الاتصال
     */
    setOnlineStatus(isOnline) {
        appStore.setState({
            data: {
                ...appStore.getState().data,
                isOnline
            }
        }, 'APP_SET_ONLINE_STATUS');

        if (isOnline) {
            showNotification('تم استعادة الاتصال', 'success');
        } else {
            showNotification('انقطع الاتصال - سيتم العمل في وضع عدم الاتصال', 'warning');
        }
    }
};

/**
 * إجراءات البيانات
 */
export const dataActions = {
    /**
     * تحديث المركبات
     */
    setVehicles(vehicles) {
        appStore.setState({
            data: {
                ...appStore.getState().data,
                vehicles,
                lastSync: new Date().toISOString()
            }
        }, 'DATA_SET_VEHICLES');
    },

    /**
     * إضافة مركبة
     */
    addVehicle(vehicle) {
        const currentState = appStore.getState();
        appStore.setState({
            data: {
                ...currentState.data,
                vehicles: [...currentState.data.vehicles, vehicle]
            }
        }, 'DATA_ADD_VEHICLE');
    },

    /**
     * تحديث مركبة
     */
    updateVehicle(vehicleId, updates) {
        const currentState = appStore.getState();
        appStore.setState({
            data: {
                ...currentState.data,
                vehicles: currentState.data.vehicles.map(vehicle =>
                    vehicle.id === vehicleId ? { ...vehicle, ...updates } : vehicle
                )
            }
        }, 'DATA_UPDATE_VEHICLE');
    },

    /**
     * حذف مركبة
     */
    removeVehicle(vehicleId) {
        const currentState = appStore.getState();
        appStore.setState({
            data: {
                ...currentState.data,
                vehicles: currentState.data.vehicles.filter(vehicle => vehicle.id !== vehicleId)
            }
        }, 'DATA_REMOVE_VEHICLE');
    },

    /**
     * تحديث السائقين
     */
    setDrivers(drivers) {
        appStore.setState({
            data: {
                ...appStore.getState().data,
                drivers,
                lastSync: new Date().toISOString()
            }
        }, 'DATA_SET_DRIVERS');
    },

    /**
     * إضافة سائق
     */
    addDriver(driver) {
        const currentState = appStore.getState();
        appStore.setState({
            data: {
                ...currentState.data,
                drivers: [...currentState.data.drivers, driver]
            }
        }, 'DATA_ADD_DRIVER');
    },

    /**
     * تحديث سائق
     */
    updateDriver(driverId, updates) {
        const currentState = appStore.getState();
        appStore.setState({
            data: {
                ...currentState.data,
                drivers: currentState.data.drivers.map(driver =>
                    driver.id === driverId ? { ...driver, ...updates } : driver
                )
            }
        }, 'DATA_UPDATE_DRIVER');
    },

    /**
     * تحديث سجلات الصيانة
     */
    setMaintenanceRecords(maintenance) {
        appStore.setState({
            data: {
                ...appStore.getState().data,
                maintenance,
                lastSync: new Date().toISOString()
            }
        }, 'DATA_SET_MAINTENANCE');
    },

    /**
     * إضافة سجل صيانة
     */
    addMaintenanceRecord(record) {
        const currentState = appStore.getState();
        appStore.setState({
            data: {
                ...currentState.data,
                maintenance: [...currentState.data.maintenance, record]
            }
        }, 'DATA_ADD_MAINTENANCE');
    },

    /**
     * تحديث سجلات الوقود
     */
    setFuelRecords(fuel) {
        appStore.setState({
            data: {
                ...appStore.getState().data,
                fuel,
                lastSync: new Date().toISOString()
            }
        }, 'DATA_SET_FUEL');
    },

    /**
     * إضافة سجل وقود
     */
    addFuelRecord(record) {
        const currentState = appStore.getState();
        appStore.setState({
            data: {
                ...currentState.data,
                fuel: [...currentState.data.fuel, record]
            }
        }, 'DATA_ADD_FUEL');
    },

    /**
     * تحديث المستخدمين
     */
    setUsers(users) {
        appStore.setState({
            data: {
                ...appStore.getState().data,
                users,
                lastSync: new Date().toISOString()
            }
        }, 'DATA_SET_USERS');
    }
};

/**
 * إجراءات واجهة المستخدم
 */
export const uiActions = {
    /**
     * تبديل الشريط الجانبي
     */
    toggleSidebar() {
        const currentState = appStore.getState();
        appStore.setState({
            app: {
                ...currentState.app,
                sidebarOpen: !currentState.app.sidebarOpen
            }
        }, 'UI_TOGGLE_SIDEBAR');
    },

    /**
     * فتح/إغلاق نافذة منبثقة
     */
    setModal(modalId, isOpen, data = null) {
        const currentState = appStore.getState();
        appStore.setState({
            ui: {
                ...currentState.ui,
                modals: {
                    ...currentState.ui.modals,
                    [modalId]: { isOpen, data }
                }
            }
        }, 'UI_SET_MODAL');
    },

    /**
     * تحديث حالة النموذج
     */
    setFormState(formId, state) {
        const currentState = appStore.getState();
        appStore.setState({
            ui: {
                ...currentState.ui,
                forms: {
                    ...currentState.ui.forms,
                    [formId]: state
                }
            }
        }, 'UI_SET_FORM_STATE');
    },

    /**
     * تحديث إعدادات الجدول
     */
    setTableState(tableId, state) {
        const currentState = appStore.getState();
        appStore.setState({
            ui: {
                ...currentState.ui,
                tables: {
                    ...currentState.ui.tables,
                    [tableId]: state
                }
            }
        }, 'UI_SET_TABLE_STATE');
    },

    /**
     * تحديث الفلاتر
     */
    setFilters(section, filters) {
        const currentState = appStore.getState();
        appStore.setState({
            filters: {
                ...currentState.filters,
                [section]: {
                    ...currentState.filters[section],
                    ...filters
                }
            }
        }, 'UI_SET_FILTERS');
    },

    /**
     * مسح الفلاتر
     */
    clearFilters(section) {
        const currentState = appStore.getState();
        appStore.setState({
            filters: {
                ...currentState.filters,
                [section]: {}
            }
        }, 'UI_CLEAR_FILTERS');
    }
};

/**
 * تجميع جميع الإجراءات
 */
export const actions = {
    user: userActions,
    app: appActions,
    data: dataActions,
    ui: uiActions
};

// تصدير افتراضي
export default actions;

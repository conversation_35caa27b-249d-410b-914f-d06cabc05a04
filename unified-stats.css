/* Unified Statistics Card Styles */

/* Grid layouts for all stat cards */
.dashboard-stats,
.services-stats,
.maintenance-stats-grid {
    display: grid;
    grid-template-columns: repeat(1, 1fr);
    gap: 16px;
    margin-bottom: 24px;
}

/* Responsive grid layout */
@media (min-width: 640px) {
    .dashboard-stats,
    .services-stats,
    .maintenance-stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 768px) {
    .dashboard-stats,
    .services-stats,
    .maintenance-stats-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (min-width: 1024px) {
    .dashboard-stats,
    .services-stats,
    .maintenance-stats-grid {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .maintenance-stats-grid {
        grid-template-columns: repeat(5, 1fr);
    }
}

/* Unified stat card styles */
.stat-card,
.services-stat,
.maintenance-stat {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    padding: 16px;
    display: flex;
    align-items: center;
    position: relative;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: auto;
    min-height: 90px;
    border: 1px solid rgba(0, 87, 255, 0.08); /* Subtle border with #0057ff color */
}

.stat-card:hover,
.services-stat:hover,
.maintenance-stat:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    border-color: rgba(0, 87, 255, 0.2); /* More visible border on hover */
}

/* Icon styles */
.stat-card .stat-icon,
.services-stat .stat-icon,
.maintenance-stat .stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
    flex-shrink: 0;
    font-size: 1.5rem;
    color: white;
}

/* Icon colors */
.stat-icon.blue {
    background-color: #0057ff;
}

.stat-icon.green {
    background-color: #00c853;
}

.stat-icon.purple {
    background-color: #aa00ff;
}

.stat-icon.orange {
    background-color: #ffab00;
}

.stat-icon.indigo {
    background-color: #2979ff;
}

.stat-icon.red {
    background-color: #f44336;
}

.stat-icon.teal {
    background-color: #00bcd4;
}

.stat-icon.amber {
    background-color: #ff6d00;
}

.stat-icon.pink {
    background-color: #ec407a;
}

.stat-icon.cyan {
    background-color: #00bfa5;
}

/* Info styles */
.stat-card .stat-info,
.services-stat .stat-info,
.maintenance-stat .stat-info {
    flex: 1;
}

.stat-card .stat-info h3,
.services-stat .stat-info h3,
.maintenance-stat .stat-info h3 {
    font-size: 14px;
    color: #666;
    margin: 0 0 4px 0;
    font-weight: 500;
}

.stat-card .stat-info p,
.services-stat .stat-info p,
.maintenance-stat .stat-info p {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin: 0;
}

/* Percentage styles */
.stat-percentage {
    font-size: 0.85em;
    opacity: 0.85;
    margin-left: 8px;
    display: inline-block;
    vertical-align: baseline;
    color: #666;
}

/* Animation for updates */
@keyframes highlight {
    0% {
        background-color: rgba(0, 87, 255, 0.1);
    }
    50% {
        background-color: rgba(0, 87, 255, 0.2);
    }
    100% {
        background-color: transparent;
    }
}

.update-highlight {
    animation: highlight 1s ease;
}

/* Dark mode support */
body.dark-mode .stat-card,
body.dark-mode .services-stat,
body.dark-mode .maintenance-stat {
    background: linear-gradient(145deg, #111b30, #162341);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 5px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(0, 87, 255, 0.1); /* Subtle border in dark mode */
}

body.dark-mode .stat-card .stat-info h3,
body.dark-mode .services-stat .stat-info h3,
body.dark-mode .maintenance-stat .stat-info h3 {
    color: #94a3b8;
}

body.dark-mode .stat-card .stat-info p,
body.dark-mode .services-stat .stat-info p,
body.dark-mode .maintenance-stat .stat-info p {
    color: #f8fafc;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

body.dark-mode .stat-card:hover,
body.dark-mode .services-stat:hover,
body.dark-mode .maintenance-stat:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 15px rgba(0, 0, 0, 0.2);
    border-color: rgba(0, 87, 255, 0.25); /* More visible border on hover in dark mode */
}

body.dark-mode .stat-percentage {
    color: #94a3b8;
}

/* Mobile responsiveness */
@media (max-width: 480px) {
    .stat-card,
    .services-stat,
    .maintenance-stat {
        padding: 12px;
    }
    
    .stat-card .stat-icon,
    .services-stat .stat-icon,
    .maintenance-stat .stat-icon {
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
        margin-right: 12px;
    }
    
    .stat-card .stat-info h3,
    .services-stat .stat-info h3,
    .maintenance-stat .stat-info h3 {
        font-size: 12px;
    }
    
    .stat-card .stat-info p,
    .services-stat .stat-info p,
    .maintenance-stat .stat-info p {
        font-size: 20px;
    }
    
    .stat-percentage {
        font-size: 0.75em;
        margin-left: 4px;
    }
}

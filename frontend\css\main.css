/**
 * Main CSS - التنسيق الرئيسي للتطبيق
 * يحتوي على الأنماط الأساسية والمتغيرات والتخطيط العام
 */

/* ===== متغيرات CSS ===== */
:root {
    /* الألوان الأساسية */
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --primary-light: #3b82f6;
    --secondary-color: #64748b;
    --accent-color: #f59e0b;
    
    /* ألوان الحالة */
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --info-color: #3b82f6;
    
    /* ألوان الخلفية */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --bg-dark: #1e293b;
    --bg-darker: #0f172a;
    
    /* ألوان النص */
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;
    --text-light: #ffffff;
    
    /* ألوان الحدود */
    --border-color: #e2e8f0;
    --border-dark: #cbd5e1;
    --border-light: #f1f5f9;
    
    /* الظلال */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    
    /* المسافات */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    
    /* أحجام الخط */
    --font-xs: 0.75rem;
    --font-sm: 0.875rem;
    --font-base: 1rem;
    --font-lg: 1.125rem;
    --font-xl: 1.25rem;
    --font-2xl: 1.5rem;
    --font-3xl: 1.875rem;
    
    /* أوزان الخط */
    --font-light: 300;
    --font-normal: 400;
    --font-medium: 500;
    --font-semibold: 600;
    --font-bold: 700;
    
    /* نصف القطر */
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    
    /* الانتقالات */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 250ms ease-in-out;
    --transition-slow: 350ms ease-in-out;
    
    /* عرض الحاوية */
    --container-sm: 640px;
    --container-md: 768px;
    --container-lg: 1024px;
    --container-xl: 1280px;
    --container-2xl: 1536px;
    
    /* الشريط الجانبي */
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 80px;
    
    /* الرأس */
    --header-height: 64px;
}

/* ===== إعادة تعيين الأنماط ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: var(--font-base);
    font-weight: var(--font-normal);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-secondary);
    direction: rtl;
    text-align: right;
}

/* ===== الخطوط العربية ===== */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap');

body {
    font-family: 'Noto Sans Arabic', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* ===== التخطيط الأساسي ===== */
.app-container {
    display: flex;
    min-height: 100vh;
    background-color: var(--bg-secondary);
}

.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    margin-right: var(--sidebar-width);
    transition: margin-right var(--transition-normal);
}

.main-content.sidebar-collapsed {
    margin-right: var(--sidebar-collapsed-width);
}

.content-area {
    flex: 1;
    padding: var(--spacing-lg);
    margin-top: var(--header-height);
    overflow-y: auto;
}

/* ===== الحاويات ===== */
.container {
    width: 100%;
    max-width: var(--container-xl);
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

.container-fluid {
    width: 100%;
    padding: 0 var(--spacing-md);
}

/* ===== الشبكة ===== */
.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 calc(var(--spacing-sm) * -1);
}

.col {
    flex: 1;
    padding: 0 var(--spacing-sm);
}

.col-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
.col-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
.col-3 { flex: 0 0 25%; max-width: 25%; }
.col-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
.col-6 { flex: 0 0 50%; max-width: 50%; }
.col-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
.col-9 { flex: 0 0 75%; max-width: 75%; }
.col-12 { flex: 0 0 100%; max-width: 100%; }

/* ===== البطاقات ===== */
.card {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    transition: box-shadow var(--transition-normal);
}

.card:hover {
    box-shadow: var(--shadow-md);
}

.card-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-secondary);
}

.card-body {
    padding: var(--spacing-lg);
}

.card-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border-color);
    background: var(--bg-secondary);
}

.card-title {
    font-size: var(--font-lg);
    font-weight: var(--font-semibold);
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.card-subtitle {
    font-size: var(--font-sm);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
}

/* ===== الأزرار ===== */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-sm) var(--spacing-lg);
    font-size: var(--font-sm);
    font-weight: var(--font-medium);
    line-height: 1.5;
    text-align: center;
    text-decoration: none;
    border: 1px solid transparent;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
    user-select: none;
    white-space: nowrap;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    color: var(--text-light);
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover:not(:disabled) {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
}

.btn-secondary {
    color: var(--text-primary);
    background-color: var(--bg-secondary);
    border-color: var(--border-color);
}

.btn-secondary:hover:not(:disabled) {
    background-color: var(--bg-tertiary);
}

.btn-success {
    color: var(--text-light);
    background-color: var(--success-color);
    border-color: var(--success-color);
}

.btn-warning {
    color: var(--text-primary);
    background-color: var(--warning-color);
    border-color: var(--warning-color);
}

.btn-danger {
    color: var(--text-light);
    background-color: var(--error-color);
    border-color: var(--error-color);
}

.btn-outline-primary {
    color: var(--primary-color);
    background-color: transparent;
    border-color: var(--primary-color);
}

.btn-outline-primary:hover:not(:disabled) {
    color: var(--text-light);
    background-color: var(--primary-color);
}

/* أحجام الأزرار */
.btn-sm {
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: var(--font-xs);
}

.btn-lg {
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: var(--font-lg);
}

/* ===== النماذج ===== */
.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-weight: var(--font-medium);
    color: var(--text-primary);
}

.form-control {
    display: block;
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-base);
    line-height: 1.5;
    color: var(--text-primary);
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

.form-control.is-invalid {
    border-color: var(--error-color);
}

.form-control.is-invalid:focus {
    box-shadow: 0 0 0 3px rgb(239 68 68 / 0.1);
}

.invalid-feedback {
    display: block;
    width: 100%;
    margin-top: var(--spacing-xs);
    font-size: var(--font-sm);
    color: var(--error-color);
}

.form-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: left 0.75rem center;
    background-size: 16px 12px;
    padding-left: 2.25rem;
}

/* ===== الجداول ===== */
.table-responsive {
    overflow-x: auto;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
}

.table {
    width: 100%;
    margin-bottom: 0;
    background-color: var(--bg-primary);
    border-collapse: collapse;
}

.table th,
.table td {
    padding: var(--spacing-md);
    text-align: right;
    border-bottom: 1px solid var(--border-color);
}

.table th {
    font-weight: var(--font-semibold);
    background-color: var(--bg-secondary);
    color: var(--text-primary);
}

.table tbody tr:hover {
    background-color: var(--bg-tertiary);
}

/* ===== الأدوات المساعدة ===== */
.text-center { text-align: center !important; }
.text-left { text-align: left !important; }
.text-right { text-align: right !important; }

.text-primary { color: var(--primary-color) !important; }
.text-secondary { color: var(--text-secondary) !important; }
.text-success { color: var(--success-color) !important; }
.text-warning { color: var(--warning-color) !important; }
.text-danger { color: var(--error-color) !important; }
.text-muted { color: var(--text-muted) !important; }

.bg-primary { background-color: var(--primary-color) !important; }
.bg-secondary { background-color: var(--bg-secondary) !important; }
.bg-success { background-color: var(--success-color) !important; }
.bg-warning { background-color: var(--warning-color) !important; }
.bg-danger { background-color: var(--error-color) !important; }

.d-none { display: none !important; }
.d-block { display: block !important; }
.d-flex { display: flex !important; }
.d-inline-flex { display: inline-flex !important; }

.justify-content-start { justify-content: flex-start !important; }
.justify-content-center { justify-content: center !important; }
.justify-content-end { justify-content: flex-end !important; }
.justify-content-between { justify-content: space-between !important; }

.align-items-start { align-items: flex-start !important; }
.align-items-center { align-items: center !important; }
.align-items-end { align-items: flex-end !important; }

.flex-column { flex-direction: column !important; }
.flex-row { flex-direction: row !important; }

.w-100 { width: 100% !important; }
.h-100 { height: 100% !important; }

.m-0 { margin: 0 !important; }
.m-1 { margin: var(--spacing-xs) !important; }
.m-2 { margin: var(--spacing-sm) !important; }
.m-3 { margin: var(--spacing-md) !important; }
.m-4 { margin: var(--spacing-lg) !important; }
.m-5 { margin: var(--spacing-xl) !important; }

.p-0 { padding: 0 !important; }
.p-1 { padding: var(--spacing-xs) !important; }
.p-2 { padding: var(--spacing-sm) !important; }
.p-3 { padding: var(--spacing-md) !important; }
.p-4 { padding: var(--spacing-lg) !important; }
.p-5 { padding: var(--spacing-xl) !important; }

.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: var(--spacing-xs) !important; }
.mb-2 { margin-bottom: var(--spacing-sm) !important; }
.mb-3 { margin-bottom: var(--spacing-md) !important; }
.mb-4 { margin-bottom: var(--spacing-lg) !important; }
.mb-5 { margin-bottom: var(--spacing-xl) !important; }

.mt-0 { margin-top: 0 !important; }
.mt-1 { margin-top: var(--spacing-xs) !important; }
.mt-2 { margin-top: var(--spacing-sm) !important; }
.mt-3 { margin-top: var(--spacing-md) !important; }
.mt-4 { margin-top: var(--spacing-lg) !important; }
.mt-5 { margin-top: var(--spacing-xl) !important; }

/* ===== الاستجابة ===== */
@media (max-width: 768px) {
    .main-content {
        margin-right: 0;
    }
    
    .content-area {
        padding: var(--spacing-md);
    }
    
    .col-md-6 { flex: 0 0 50%; max-width: 50%; }
    .col-md-12 { flex: 0 0 100%; max-width: 100%; }
    
    .btn {
        width: 100%;
        margin-bottom: var(--spacing-sm);
    }
    
    .table-responsive {
        font-size: var(--font-sm);
    }
}

@media (max-width: 576px) {
    .col-sm-12 { flex: 0 0 100%; max-width: 100%; }
    
    .card-body {
        padding: var(--spacing-md);
    }
    
    .btn-group .btn {
        width: auto;
        margin-bottom: 0;
    }
}

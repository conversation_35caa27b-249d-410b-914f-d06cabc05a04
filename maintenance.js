// Import required modules/functions correctly
import { vehicles, drivers, openModal, closeModal, showNotification, API_URL, currentUser } from './script.js';
import { createTableSpinner, showSpinner, hideSpinner } from './spinner.js';
import { openMaintenanceModal, closeMaintenanceModal, handleMaintenanceSubmit, initializeMaintenanceModal, selectedVehicleId } from './maintenanceModal.js';
import { getServiceTypeLabel, formatServiceRemaining, filterServices, getStatusInfo, calculateExpectedDate, getUpcomingServices } from './upcoming-services.js';
import { updateServiceStats } from './dashboard.js';
import { hasPermission, PERMISSIONS } from './permissions.js';
import { initializeMaintenanceStats, calculateMaintenanceStats } from './maintenance-stats.js';

// Initialize maintenance records array
let maintenanceRecords = [];

// Initialize maintenance page
export function initializeMaintenancePage() {
    try {
        console.log('Initializing maintenance page');

        // Hide the upcoming services section in the maintenance page
        const upcomingServicesSection = document.querySelector('#maintenance-page .dashboard-section');
        if (upcomingServicesSection) {
            upcomingServicesSection.style.display = 'none';

            // We don't need to add filters since the section is hidden
            // addUpcomingServicesFilters(upcomingServicesSection);
        }

        // Remove existing table controls if any
        const existingControls = document.querySelectorAll('.table-controls');
        existingControls.forEach(control => control.remove());

        // Ensure the maintenance records section exists
        const maintenancePage = document.getElementById('maintenance-page');
        if (!maintenancePage) {
            console.error('Maintenance page not found');
            showNotification('Maintenance page not found', 'error');
            return;
        }

        // First, remove any existing duplicate sections
        const existingSections = maintenancePage.querySelectorAll('.maintenance-records-section');
        if (existingSections.length > 1) {
            console.log('Found multiple maintenance records sections, removing duplicates');
            // Keep only the first one
            for (let i = 1; i < existingSections.length; i++) {
                existingSections[i].remove();
            }
        }

        // Check if the maintenance records section exists
        let recordsSection = maintenancePage.querySelector('.maintenance-records-section');
        if (!recordsSection) {
            console.log('Creating maintenance records section');
            recordsSection = document.createElement('div');
            recordsSection.className = 'maintenance-records-section';
            // Create the title
            const title = document.createElement('h2');
            title.className = 'section-title';
            title.textContent = 'Maintenance Records';
            recordsSection.appendChild(title);

            // Create the table container
            const tableContainer = document.createElement('div');
            tableContainer.className = 'table-responsive';
            tableContainer.innerHTML = `
                <table id="maintenance-records-table" class="table">
                    <thead>
                        <tr>
                            <th>License Plate</th>
                            <th>Service Date</th>
                            <th>Service Type</th>
                            <th>Service Center</th>
                            <th>Odometer Reading</th>
                            <th>Next Service Date</th>
                            <th>Total Cost</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            `;
            recordsSection.appendChild(tableContainer);

            // Insert the section at the beginning of the page
            const pageHeader = maintenancePage.querySelector('.page-header');
            if (pageHeader) {
                maintenancePage.insertBefore(recordsSection, pageHeader.nextSibling);
            } else {
                maintenancePage.appendChild(recordsSection);
            }
        }

        // Verify that the table exists
        const maintenanceTable = document.getElementById('maintenance-records-table');
        if (!maintenanceTable) {
            console.error('Maintenance table not found after creation attempt');
            showNotification('Error initializing maintenance page', 'error');
            return;
        }

        // Check for duplicate titles and remove them
        const titles = recordsSection.querySelectorAll('.section-title');
        if (titles.length > 1) {
            console.log('Found multiple section titles, removing duplicates');
            // Keep only the first one
            for (let i = 1; i < titles.length; i++) {
                titles[i].remove();
            }
        }

        // Verify that the table body exists
        const tableBody = maintenanceTable.querySelector('tbody');
        if (!tableBody) {
            console.error('Maintenance table body not found after creation attempt');
            showNotification('Error initializing maintenance page', 'error');
            return;
        }

        console.log('Maintenance table structure verified');

        // إظهار مؤشر التحميل الجديد
        renderMaintenanceTable(true);

        // Fetch initial maintenance records
        fetch(`${API_URL}?action=getMaintenance`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(result => {
                if (result.status === 'success' && result.data) {
                    maintenanceRecords = result.data;
                    console.log('Maintenance records loaded:', maintenanceRecords.length);
                }
                // عرض الجدول بعد تحميل البيانات
                renderMaintenanceTable(false);

                // We still call renderUpcomingServices to update dashboard stats
                // but it won't render in maintenance page due to our check
                renderUpcomingServices('all');

                // Setup maintenance event listeners
                setupMaintenanceEventListeners();

                // Make functions available globally
                window.maintenanceRecords = maintenanceRecords;
                window.renderMaintenanceTable = renderMaintenanceTable;
                window.renderUpcomingServices = renderUpcomingServices;

                // Initialize maintenance statistics
                initializeMaintenanceStats();
                calculateMaintenanceStats(maintenanceRecords);

                // Hide spinner and show success notification
                hideSpinner();
                showNotification('Maintenance records loaded successfully', 'success');
            })
            .catch(error => {
                console.error('Error fetching initial maintenance records:', error);
                // عرض رسالة الخطأ في الجدول
                renderMaintenanceTable(false, error);
                setupMaintenanceEventListeners();
                hideSpinner();
                showNotification('Error loading maintenance records: ' + error.message, 'error');
            });
    } catch (error) {
        console.error('Error initializing maintenance page:', error);
    }
}



// Replace the missing updateUpcomingServices with a local implementation
export function renderUpcomingServices(filter = 'all', selectedBranch = 'all') {
    try {
        console.log(`Rendering upcoming services with filter: ${filter}, branch: ${selectedBranch}`);
        // Get the services data with branch filter
        const services = getUpcomingServices(selectedBranch);

        // Update the statistics for dashboard only
        updateServiceStats(services);

        // Get the current page
        const currentPage = document.querySelector('.page.active');
        const isDashboardPage = currentPage && currentPage.id === 'dashboard-page';
        const isMaintenancePage = currentPage && currentPage.id === 'maintenance-page';

        // Skip rendering the table in maintenance page
        if (isMaintenancePage) {
            console.log('Skipping upcoming services table rendering in maintenance page');
            return;
        }

        // Always check for dashboard section and add filters if needed
        const dashboardSection = document.querySelector('#dashboard-page .dashboard-section:has(#upcoming-services-table)');
        if (dashboardSection) {
            // Check if filters don't exist and add them
            if (!dashboardSection.querySelector('.service-type-filter')) {
                console.log('Adding service type filters to dashboard');
                addUpcomingServicesFilters(dashboardSection);
            }
        } else {
            console.log('Dashboard section with upcoming services table not found');
            return;
        }

        // Filter services based on the selected filter and branch
        const filteredServices = filterServices(services, filter);
        console.log(`Found ${filteredServices.length} services after filtering`);

        // Render the services table
        const tableBody = document.getElementById('upcoming-services-table').querySelector('tbody');
        if (!tableBody) {
            console.error('Upcoming services table body not found');
            return;
        }

        // Clear existing content
        tableBody.innerHTML = '';

        // If no services, show a message
        if (filteredServices.length === 0) {
            const row = document.createElement('tr');
            row.innerHTML = `<td colspan="6" style="text-align: center;">لا توجد خدمات قادمة${selectedBranch !== 'all' ? ` في فرع ${selectedBranch}` : ''}</td>`;
            tableBody.appendChild(row);
            return;
        }

        // Get the visible columns configuration
        const visibleColumns = window.getVisibleColumns ? window.getVisibleColumns() : {
            vehicle: true,
            serviceType: true,
            expectedDate: true,
            status: true,
            remaining: true,
            actions: true
        };

        // Render each service
        filteredServices.forEach(service => {
            const row = document.createElement('tr');
            row.className = `${service.statusInfo.class}-row ${service.type}-service`;
            row.setAttribute('data-branch', service.vehicle.branch || 'Unknown');

            let rowHtml = '';

            // Add columns based on visibility settings
            if (visibleColumns.vehicle) {
                rowHtml += `
                    <td>
                        ${service.vehicle.plate || service.vehicle.id || 'Unknown'}
                        <small class="text-muted d-block">${service.vehicle.branch || 'No Branch'}</small>
                    </td>
                `;
            }

            if (visibleColumns.serviceType) {
                rowHtml += `<td>${getServiceTypeLabel(service.type)}</td>`;
            }

            if (visibleColumns.expectedDate) {
                rowHtml += `<td>${service.expectedDate || 'N/A'}</td>`;
            }

            if (visibleColumns.status) {
                rowHtml += `
                    <td>
                        <div class="status-indicator ${service.statusInfo.class}" title="${service.statusInfo.tooltip}">
                            <i class="fas fa-${service.statusInfo.icon}"></i>
                            <span>${service.statusInfo.text}</span>
                        </div>
                    </td>
                `;
            }

            if (visibleColumns.remaining) {
                rowHtml += `<td>${formatServiceRemaining(service)}</td>`;
            }

            // Always show actions column
            rowHtml += `
                <td class="action-buttons">
                    <button class="profile-btn" data-vehicle-id="${service.vehicle.id}">
                        <i class="fas fa-car"></i> Profile
                    </button>
                </td>
            `;

            row.innerHTML = rowHtml;
            tableBody.appendChild(row);
        });

        // Setup control event listeners
        setupControlEventListeners();

    } catch (error) {
        console.error('Error rendering upcoming services:', error);
    }
}

// Add upcoming services filters to the section
function addUpcomingServicesFilters(section) {
    try {
        // Check if filters already exist
        if (section.querySelector('.service-type-filter')) {
            return; // Filters already exist
        }

        // Create filter controls container
        const controlsContainer = document.createElement('div');
        controlsContainer.className = 'table-controls';

        // Create service type filter
        const serviceTypeFilter = document.createElement('div');
        serviceTypeFilter.className = 'service-type-filter';

        // Add filter buttons with export button at the end
        serviceTypeFilter.innerHTML = `
            <button class="control-btn active" data-filter="all">All Services</button>
            <button class="control-btn" data-filter="required">Required</button>
            <button class="control-btn" data-filter="upcoming">Upcoming</button>
            <button class="control-btn" data-filter="maintenance">Maintenance</button>
            <button class="control-btn" data-filter="tires">Tire Change</button>
            <button class="control-btn" data-filter="license">License Renewal</button>
            <button id="export-excel" class="control-btn export-btn"><i class="fas fa-file-excel"></i> Excel</button>
        `;

        // Append elements to container
        controlsContainer.appendChild(serviceTypeFilter);

        // Insert controls before the table
        const tableResponsive = section.querySelector('.table-responsive');
        section.insertBefore(controlsContainer, tableResponsive);

        // Setup event listeners for the new controls
        setupControlEventListeners();
    } catch (error) {
        console.error('Error adding upcoming services filters:', error);
    }
}

// Setup control event listeners using event delegation
function setupControlEventListeners() {
    try {
        // Get all table controls on the page
        const controlContainers = document.querySelectorAll('.table-controls');
        if (controlContainers.length === 0) {
            console.log('No table controls found');
            return;
        }
        
        // Get the branch select element and setup its event listener
        const branchSelect = document.getElementById('branch-select');
        if (branchSelect) {
            branchSelect.removeEventListener('change', handleBranchChange);
            branchSelect.addEventListener('change', handleBranchChange);
        }

        const mainContainer = document.querySelector('.table-controls');
        const servicesTable = document.getElementById('upcoming-services-table');

        // Remove existing event listeners if they exist
        if (mainContainer) {
            mainContainer.removeEventListener('click', handleControlsClick);
        }
        if (servicesTable) {
            servicesTable.removeEventListener('click', handleTableClick);
        }

        // Add new event listeners using event delegation
        if (mainContainer) {
            mainContainer.addEventListener('click', handleControlsClick);
        }
        if (servicesTable) {
            servicesTable.addEventListener('click', handleTableClick);
        }

        console.log('Control event listeners set up successfully');
    } catch (error) {
        console.error('Error setting up control event listeners:', error);
    }
}

// Handle branch select change events
function handleBranchChange(e) {
    const selectedBranch = e.target.value;
    const activeServiceFilter = document.querySelector('.service-type-filter .control-btn.active');
    const serviceFilter = activeServiceFilter ? activeServiceFilter.dataset.filter : 'all';
    
    console.log(`Branch changed to ${selectedBranch}, applying with service filter: ${serviceFilter}`);
    renderUpcomingServices(serviceFilter, selectedBranch);
}

// Handle clicks on control buttons
function handleControlsClick(e) {
    const target = e.target.closest('button');
    if (!target) return;

    // Handle export button
    if (target.id === 'export-excel') {
        if (typeof window.exportToExcel === 'function') {
            window.exportToExcel();
        } else {
            console.error('exportToExcel function not found');
        }
        return;
    }

    // Handle filter buttons - now respecting both service type and branch filters
    if (target.classList.contains('control-btn') && target.closest('.service-type-filter')) {
        // Update service type filter UI
        document.querySelectorAll('.service-type-filter .control-btn')
            .forEach(btn => btn.classList.remove('active'));
        target.classList.add('active');
        
        // Get the current selected branch
        const branchSelect = document.getElementById('branch-select');
        const selectedBranch = branchSelect ? branchSelect.value : 'all';
        
        console.log(`Applying service filter: ${target.dataset.filter} with branch: ${selectedBranch}`);
        renderUpcomingServices(target.dataset.filter, selectedBranch);
    }
}

// Handle clicks on table buttons
function handleTableClick(e) {
    const profileBtn = e.target.closest('.profile-btn');
    if (profileBtn) {
        e.preventDefault();
        const vehicleId = profileBtn.dataset.vehicleId;
        if (vehicleId && typeof window.openVehicleDetailsModal === 'function') {
            window.openVehicleDetailsModal(vehicleId);
        }
    }
}

// Export to Excel function
function exportToExcel() {
    try {
        const services = getUpcomingServices();
        const visibleColumns = window.getVisibleColumns();

        // Prepare data for export
        const data = services.map(service => ({
            'Vehicle': service.vehicle.plate,
            'Service Type': getServiceTypeLabel(service.type),
            'Expected Date': service.expectedDate,
            'Status': service.statusInfo.text,
            'Remaining': formatServiceRemaining(service).replace(/<[^>]*>/g, '')
        }));

        // Create worksheet
        const ws = XLSX.utils.json_to_sheet(data);
        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, 'Upcoming Services');

        // Save file
        XLSX.writeFile(wb, 'upcoming_services.xlsx');
    } catch (error) {
        console.error('Error exporting to Excel:', error);
        showNotification('Error exporting to Excel', 'error');
    }
}

// Get current filter
function getCurrentFilter() {
    const serviceTypeBtn = document.querySelector('.service-type-filter .control-btn.active');
    return serviceTypeBtn ? serviceTypeBtn.dataset.filter : 'all';
}

// Get visible columns configuration
function getVisibleColumns() {
    const savedColumns = localStorage.getItem('upcomingServicesColumns');
    if (savedColumns) {
        try {
            return JSON.parse(savedColumns);
        } catch (e) {
            console.error('Error parsing saved columns:', e);
        }
    }
    return {
        vehicle: true,
        serviceType: true,
        expectedDate: true,
        status: true,
        remaining: true
    };
}

// updateServiceStats is imported from dashboard.js

function updateStatElement(elementId, value) {
    const element = document.getElementById(elementId);
    if (element) {
        element.textContent = value;

        // Highlight the stat card briefly
        const statCard = element.closest('.stat-card') || element.closest('.services-stat');
        if (statCard) {
            statCard.classList.add('update-highlight');
            setTimeout(() => {
                statCard.classList.remove('update-highlight');
            }, 1000);
        }
    }
}

// Add a maintenance table rendering function
export function renderMaintenanceTable(isLoading = false, error = null) {
    console.log('Rendering maintenance table, loading:', isLoading);

    try {
        // First, ensure the maintenance page exists
        const maintenancePage = document.getElementById('maintenance-page');
        if (!maintenancePage) {
            console.error('Maintenance page not found');
            showNotification('Maintenance page not found', 'error');
            return;
        }

        // Check if the maintenance records section exists, if not create it
        // First, remove any existing duplicate sections
        const existingSections = maintenancePage.querySelectorAll('.maintenance-records-section');
        if (existingSections.length > 1) {
            console.log('Found multiple maintenance records sections, removing duplicates');
            // Keep only the first one
            for (let i = 1; i < existingSections.length; i++) {
                existingSections[i].remove();
            }
        }

        let recordsSection = maintenancePage.querySelector('.maintenance-records-section');
        if (!recordsSection) {
            console.log('Creating maintenance records section');
            recordsSection = document.createElement('div');
            recordsSection.className = 'maintenance-records-section';

            // Insert the section after the page header
            const pageHeader = maintenancePage.querySelector('.page-header');
            if (pageHeader) {
                pageHeader.insertAdjacentElement('afterend', recordsSection);
            } else {
                // If no page header, insert at the beginning of the page
                maintenancePage.prepend(recordsSection);
            }
        }

        // Check if the table exists, if not create it
        let tableContainer = recordsSection.querySelector('.table-responsive');
        if (!tableContainer) {
            console.log('Creating table container');
            tableContainer = document.createElement('div');
            tableContainer.className = 'table-responsive';
            recordsSection.appendChild(tableContainer);
        }

        // If we're in loading state, show the spinner and return
        if (isLoading) {
            console.log('Showing loading spinner');
            // Clear the container first
            tableContainer.innerHTML = '';

            // Check if title already exists
            let title = recordsSection.querySelector('.section-title');
            if (!title) {
                // Add a title if it doesn't exist
                title = document.createElement('h2');
                title.className = 'section-title';
                title.textContent = 'Maintenance Records';
                recordsSection.insertBefore(title, tableContainer);
            }
            // Show spinner
            createTableSpinner(tableContainer, 'Loading maintenance records...');
            return;
        }

        // Check if title already exists
        let title = recordsSection.querySelector('.section-title');
        if (!title) {
            // Add a title if it doesn't exist
            title = document.createElement('h2');
            title.className = 'section-title';
            title.textContent = 'Maintenance Records';
            recordsSection.insertBefore(title, tableContainer);
        } else {
            // Make sure there's only one title
            const titles = recordsSection.querySelectorAll('.section-title');
            if (titles.length > 1) {
                // Remove extra titles
                for (let i = 1; i < titles.length; i++) {
                    titles[i].remove();
                }
            }
        }

        // Add search box if it doesn't exist
        let searchContainer = recordsSection.querySelector('.maintenance-search-container');
        if (!searchContainer) {
            searchContainer = document.createElement('div');
            searchContainer.className = 'maintenance-search-container';
            searchContainer.innerHTML = `
                <div class="maintenance-controls-wrapper">
                    <div class="maintenance-controls">
                        <div class="control-group search-group">
                            <div class="control-label">
                                <i class="fas fa-search"></i>
                                <span>Search</span>
                            </div>
                            <div class="search-box">
                                <input type="text" id="maintenance-search" placeholder="Search maintenance records..." class="form-control">
                                <button id="maintenance-search-btn" class="btn btn-primary">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>

                        <div class="control-group filter-group">
                            <div class="control-label">
                                <i class="fas fa-filter"></i>
                                <span>Filter</span>
                            </div>
                            <div class="filter-box">
                                <select id="service-type-filter" class="form-control">
                                    <option value="all">All Service Types</option>
                                    <option value="Routine Maintenance">Routine Maintenance</option>
                                    <option value="Oil Change">Oil Change</option>
                                    <option value="Tire Change">Tire Change</option>
                                    <option value="Tire Rotation">Tire Rotation</option>
                                    <option value="Brake Service">Brake Service</option>
                                    <option value="Battery Replacement">Battery Replacement</option>
                                    <option value="Other">Other</option>
                                </select>
                            </div>
                        </div>

                        <div class="control-group export-group">
                            <div class="control-label">
                                <i class="fas fa-download"></i>
                                <span>Export</span>
                            </div>
                            <button id="export-maintenance-excel" class="btn btn-success export-btn">
                                <i class="fas fa-file-excel"></i> Excel
                            </button>
                        </div>
                    </div>

                    <div class="active-filters">
                        <span class="filter-label">Active filters:</span>
                        <span id="search-filter-tag" class="filter-tag" style="display: none;">
                            <span class="filter-text">Search: <span class="filter-value"></span></span>
                            <button class="clear-filter" data-filter="search"><i class="fas fa-times"></i></button>
                        </span>
                        <span id="service-type-filter-tag" class="filter-tag" style="display: none;">
                            <span class="filter-text">Service Type: <span class="filter-value"></span></span>
                            <button class="clear-filter" data-filter="service-type"><i class="fas fa-times"></i></button>
                        </span>
                        <button id="clear-all-filters" class="btn btn-sm btn-outline-secondary" style="display: none;">
                            <i class="fas fa-times"></i> Clear All Filters
                        </button>
                    </div>
                </div>
            `;
            recordsSection.insertBefore(searchContainer, tableContainer);

            // Add event listeners for search
            const searchInput = searchContainer.querySelector('#maintenance-search');
            const searchButton = searchContainer.querySelector('#maintenance-search-btn');

            if (searchInput) {
                searchInput.addEventListener('keyup', (e) => {
                    if (e.key === 'Enter') {
                        const serviceType = searchContainer.querySelector('#service-type-filter')?.value || 'all';
                        filterMaintenanceRecords(searchInput.value, serviceType);
                        updateFilterTags(searchInput.value, serviceType);
                    }
                });
            }

            if (searchButton) {
                searchButton.addEventListener('click', () => {
                    const serviceType = searchContainer.querySelector('#service-type-filter')?.value || 'all';
                    filterMaintenanceRecords(searchInput.value, serviceType);
                    updateFilterTags(searchInput.value, serviceType);
                });
            }

            // Add event listener for service type filter
            const serviceTypeFilter = searchContainer.querySelector('#service-type-filter');
            if (serviceTypeFilter) {
                serviceTypeFilter.addEventListener('change', () => {
                    // Get current search query and selected service type
                    const searchQuery = searchInput ? searchInput.value : '';
                    const serviceType = serviceTypeFilter.value;

                    // Filter records based on both search query and service type
                    filterMaintenanceRecords(searchQuery, serviceType);
                    updateFilterTags(searchQuery, serviceType);
                });
            }

            // Add event listener for export button
            const exportButton = searchContainer.querySelector('#export-maintenance-excel');
            if (exportButton) {
                exportButton.addEventListener('click', () => {
                    // Get current search query and selected service type for export
                    const searchQuery = searchInput ? searchInput.value : '';
                    const serviceType = serviceTypeFilter ? serviceTypeFilter.value : 'all';

                    // Export records with current filters
                    exportMaintenanceToExcel(searchQuery, serviceType);
                });
            }

            // Add event listeners for clear filter buttons
            const clearFilterButtons = searchContainer.querySelectorAll('.clear-filter');
            clearFilterButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const filterType = button.getAttribute('data-filter');

                    if (filterType === 'search' && searchInput) {
                        searchInput.value = '';
                    } else if (filterType === 'service-type' && serviceTypeFilter) {
                        serviceTypeFilter.value = 'all';
                    }

                    // Update filters and UI
                    const currentSearchQuery = searchInput ? searchInput.value : '';
                    const currentServiceType = serviceTypeFilter ? serviceTypeFilter.value : 'all';
                    filterMaintenanceRecords(currentSearchQuery, currentServiceType);
                    updateFilterTags(currentSearchQuery, currentServiceType);
                });
            });

            // Add event listener for clear all filters button
            const clearAllButton = searchContainer.querySelector('#clear-all-filters');
            if (clearAllButton) {
                clearAllButton.addEventListener('click', () => {
                    if (searchInput) searchInput.value = '';
                    if (serviceTypeFilter) serviceTypeFilter.value = 'all';

                    // Reset filters and UI
                    filterMaintenanceRecords('', 'all');
                    updateFilterTags('', 'all');
                });
            }
        }

        // Create the table structure
        tableContainer.innerHTML = `
            <table id="maintenance-records-table" class="table">
                <thead>
                    <tr>
                        <th>License Plate</th>
                        <th>Service Date</th>
                        <th>Service Type</th>
                        <th>Service Center</th>
                        <th>Odometer Reading</th>
                        <th>Next Service Date</th>
                        <th>Total Cost</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody></tbody>
            </table>
        `;

        // Get the table body
        const tableBody = tableContainer.querySelector('tbody');
        if (!tableBody) {
            console.error('Table body not found after creation');
            showNotification('Error creating maintenance table', 'error');
            return;
        }

        // If there's an error, show the error message
        if (error) {
            console.error('Error loading maintenance records:', error);
            const errorRow = document.createElement('tr');
            errorRow.innerHTML = `<td colspan="8" style="text-align: center; color: red;">Error loading maintenance records: ${error.message}</td>`;
            tableBody.appendChild(errorRow);
            return;
        }

        // Check if we have maintenance records
        if (!maintenanceRecords || !Array.isArray(maintenanceRecords) || maintenanceRecords.length === 0) {
            console.log('No maintenance records to display');
            const emptyRow = document.createElement('tr');
            emptyRow.innerHTML = `<td colspan="8" style="text-align: center;">No maintenance records found</td>`;
            tableBody.appendChild(emptyRow);
            return;
        }

        console.log('Rendering', maintenanceRecords.length, 'maintenance records');

        // Sort records by date (newest first)
        const sortedRecords = [...maintenanceRecords].sort((a, b) => {
            return new Date(b['Service Date'] || '1970-01-01') - new Date(a['Service Date'] || '1970-01-01');
        });

        // Render each record
        sortedRecords.forEach((record, index) => {
            try {
                // Find the associated vehicle
                const vehicle = vehicles.find(v => v['Vehicle ID'] === record['Vehicle ID'] || v.id === record['Vehicle ID']);
                const vehicleName = record['License Plate'] || (vehicle ? (vehicle['License Plate'] || 'Unknown') : 'Unknown');

                // Format dates
                const formattedDate = new Date(record['Service Date'] || '1970-01-01').toLocaleDateString();
                const nextServiceDate = record['Next Service Date'] ? new Date(record['Next Service Date']).toLocaleDateString() : 'N/A';

                // Create a safe version of record with default values
                const safeRecord = {
                    'Service Type': record['Service Type'] || 'N/A',
                    'Service Center': record['Service Center'] || 'N/A',
                    'Odometer Reading': record['Odometer Reading'] || 0,
                    'Total Cost': record['Total Cost'] || 0,
                    'Maintenance ID': record['Maintenance ID'] || ''
                };

                // Create the row
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${vehicleName}</td>
                    <td>${formattedDate}</td>
                    <td>${safeRecord['Service Type']}</td>
                    <td>${safeRecord['Service Center']}</td>
                    <td>${safeRecord['Odometer Reading'] > 0 ? safeRecord['Odometer Reading'].toLocaleString() + ' km' : 'N/A'}</td>
                    <td>${nextServiceDate}</td>
                    <td>${safeRecord['Total Cost'] > 0 ? '£' + safeRecord['Total Cost'].toLocaleString() : 'N/A'}</td>
                    <td class="action-buttons">
                        ${hasPermission(currentUser?.role, PERMISSIONS.REGISTER_MAINTENANCE) ?
                        `<button class="btn btn-sm btn-primary edit-maintenance" data-id="${safeRecord['Maintenance ID']}">
                            <i class="fas fa-edit"></i> Edit
                        </button>` : ''}
                        ${hasPermission(currentUser?.role, PERMISSIONS.FULL_SYSTEM_CONTROL) ?
                        `<button class="btn btn-sm btn-danger delete-maintenance" data-id="${safeRecord['Maintenance ID']}">
                            <i class="fas fa-trash"></i> Delete
                        </button>` : ''}
                    </td>
                `;

                tableBody.appendChild(row);
            } catch (recordError) {
                console.error('Error rendering maintenance record:', recordError, record);
            }
        });

        // Add event listeners to the buttons
        console.log('Adding event listeners to maintenance buttons');
        try {
            // Add event listeners to edit buttons
            tableBody.querySelectorAll('.edit-maintenance').forEach(button => {
                button.addEventListener('click', () => {
                    const id = button.getAttribute('data-id');
                    console.log('Edit button clicked for maintenance ID:', id);
                    if (id && typeof openMaintenanceModal === 'function') {
                        openMaintenanceModal(id);
                    } else {
                        console.error('Cannot open maintenance modal, function not available or ID missing');
                    }
                });
            });

            // Add event listeners to delete buttons
            tableBody.querySelectorAll('.delete-maintenance').forEach(button => {
                button.addEventListener('click', () => {
                    const id = button.getAttribute('data-id');
                    console.log('Delete button clicked for maintenance ID:', id);
                    if (id && typeof deleteMaintenanceRecord === 'function') {
                        deleteMaintenanceRecord(id);
                    } else {
                        console.error('Cannot delete maintenance record, function not available or ID missing');
                    }
                });
            });
        } catch (eventError) {
            console.error('Error adding event listeners to maintenance buttons:', eventError);
        }
    } catch (mainError) {
        console.error('Error in renderMaintenanceTable:', mainError);
        showNotification('Error rendering maintenance table', 'error');
        hideSpinner();
    }

    // Update maintenance statistics if not loading and no error
    if (!isLoading && !error && typeof calculateMaintenanceStats === 'function') {
        calculateMaintenanceStats(maintenanceRecords);
    }
}

// Function to delete a maintenance record
async function deleteMaintenanceRecord(id) {
    if (confirm('Are you sure you want to delete this maintenance record?')) {
        try {
            const response = await fetch(`${API_URL}?action=deleteMaintenance&id=${id}`, {
                method: 'POST'
            });
            const result = await response.json();            if (result.status === 'success') {
                const index = maintenanceRecords.findIndex(record => record['Maintenance ID'] === id);
                if (index !== -1) {
                    maintenanceRecords.splice(index, 1);
                    renderMaintenanceTable();
                    showNotification('Maintenance record deleted successfully', 'success');
                }
            } else {
                showNotification('Error deleting maintenance record: ' + result.message, 'error');
            }
        } catch (error) {
            console.error('Error deleting maintenance record:', error);
            showNotification('Error deleting maintenance record', 'error');
        }
    }
}

// Fetch maintenance data from server
async function fetchMaintenanceData() {
    try {
        console.log('Fetching maintenance data from server');
        const response = await fetch(`${API_URL}?action=getMaintenance`);
        const result = await response.json();

        if (result.status === 'success' && result.data) {
            console.log('Maintenance data fetched successfully:', result.data);
            return result.data;
        } else {
            console.error('Error fetching maintenance data:', result.message);
            return [];
        }
    } catch (error) {
        console.error('Error fetching maintenance data:', error);
        return [];
    }
}

// Setup event listeners for maintenance page
function setupMaintenanceEventListeners() {
    // Add maintenance button
    const addButton = document.getElementById('add-maintenance-btn');
    if (addButton) {
        addButton.addEventListener('click', () => {
            // Call openMaintenanceModal with null to indicate no vehicle is selected
            openMaintenanceModal(null);
        });
    }

    // Save button in the modal
    const saveButton = document.getElementById('save-maintenance-btn');
    if (saveButton) {
        saveButton.addEventListener('click', saveMaintenanceRecord);
    }
}

// Function to save a maintenance record
function saveMaintenanceRecord() {
    const form = document.getElementById('maintenance-form');
    if (!form) return;

    // Get form values
    const vehicleId = form.querySelector('#maintenance-vehicle')?.value;
    const date = form.querySelector('#maintenance-date')?.value;
    const type = form.querySelector('#maintenance-type')?.value;
    const description = form.querySelector('#maintenance-description')?.value;
    const cost = parseFloat(form.querySelector('#maintenance-cost')?.value || '0');
    const odometer = parseInt(form.querySelector('#maintenance-odometer')?.value || '0');
    const nextOdometer = parseInt(form.querySelector('#next-maintenance-odometer')?.value || '0');

    // Validate required fields
    if (!vehicleId || !date || !type) {
        showNotification('Please fill all required fields', 'error');
        return;
    }

    // Get the record ID if editing
    const recordId = form.dataset.id;

    if (recordId) {
        // Update existing record
        const index = maintenanceRecords.findIndex(record => record.id === recordId);
        if (index !== -1) {
            maintenanceRecords[index] = {
                ...maintenanceRecords[index],
                vehicleId,
                date,
                type,
                description,
                cost,
                odometer,
                nextOdometer,
                updatedAt: new Date().toISOString()
            };

            // Update vehicle's maintenance info if this is a maintenance record
            updateVehicleMaintenanceInfo(vehicleId, date, odometer, nextOdometer, type);

            showNotification('Maintenance record updated successfully', 'success');
        }
    } else {
        // Create new record
        const newRecord = {
            id: 'maint-' + Date.now(),
            vehicleId,
            date,
            type,
            description,
            cost,
            odometer,
            nextOdometer,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        maintenanceRecords.push(newRecord);

        // Update vehicle's maintenance info if this is a maintenance record
        updateVehicleMaintenanceInfo(vehicleId, date, odometer, nextOdometer, type);

        showNotification('Maintenance record added successfully', 'success');
    }

    // Close modal and refresh table
    closeModal('maintenance-modal');
    renderMaintenanceTable();
    renderUpcomingServices('all');
}

// Function to update vehicle maintenance info
function updateVehicleMaintenanceInfo(vehicleId, date, odometer, nextOdometer, type) {
    const vehicle = vehicles.find(v => v['Vehicle ID'] === vehicleId || v.id === vehicleId);
    if (!vehicle) return;

    if (type === 'Routine Maintenance' || type === 'Oil Change') {
        // Update maintenance info
        vehicle['Last Maintenance Date'] = date;
        vehicle['Last Maintenance Km'] = odometer;
        vehicle['Next Maintenance Km'] = nextOdometer;

        // Calculate km to next maintenance
        const currentKm = parseInt(String(vehicle['Current Km'] || '0').replace(/,/g, '')) || 0;
        vehicle['Km to next maintenance'] = nextOdometer - currentKm;
    } else if (type === 'Tire Change' || type === 'Tire Rotation') {
        // Update tire info
        vehicle['Last tire change Data'] = date;
        vehicle['Last tire change Km'] = odometer;
        vehicle['Next Tire Change Km'] = nextOdometer;

        // Calculate km to next tire change
        const currentKm = parseInt(String(vehicle['Current Km'] || '0').replace(/,/g, '')) || 0;
        vehicle['Km left for tire change'] = nextOdometer - currentKm;
    }
}

// Function to update filter tags in the UI
function updateFilterTags(searchQuery = '', serviceType = 'all') {
    try {
        // Get filter tag elements
        const searchFilterTag = document.getElementById('search-filter-tag');
        const serviceTypeFilterTag = document.getElementById('service-type-filter-tag');
        const clearAllButton = document.getElementById('clear-all-filters');

        // Update search filter tag
        if (searchFilterTag) {
            if (searchQuery) {
                // Show and update search filter tag
                searchFilterTag.style.display = 'inline-flex';
                const valueSpan = searchFilterTag.querySelector('.filter-value');
                if (valueSpan) valueSpan.textContent = searchQuery;
            } else {
                // Hide search filter tag if no search query
                searchFilterTag.style.display = 'none';
            }
        }

        // Update service type filter tag
        if (serviceTypeFilterTag) {
            if (serviceType && serviceType !== 'all') {
                // Show and update service type filter tag
                serviceTypeFilterTag.style.display = 'inline-flex';
                const valueSpan = serviceTypeFilterTag.querySelector('.filter-value');
                if (valueSpan) valueSpan.textContent = serviceType;
            } else {
                // Hide service type filter tag if no specific service type
                serviceTypeFilterTag.style.display = 'none';
            }
        }

        // Show or hide clear all button
        if (clearAllButton) {
            if (searchQuery || (serviceType && serviceType !== 'all')) {
                clearAllButton.style.display = 'inline-flex';
            } else {
                clearAllButton.style.display = 'none';
            }
        }
    } catch (error) {
        console.error('Error updating filter tags:', error);
    }
}

// Function to filter maintenance records by search query and service type
function filterMaintenanceRecords(searchQuery = '', serviceType = 'all') {
    console.log('Filtering maintenance records - Search:', searchQuery, 'Service Type:', serviceType);

    if (!searchQuery && serviceType === 'all') {
        // If no filters applied, render all records
        renderMaintenanceTable(false);
        return;
    }

    // Convert search query to lowercase for case-insensitive search
    const query = searchQuery.toLowerCase();

    // Filter maintenance records based on both search query and service type
    const filteredRecords = maintenanceRecords.filter(record => {
        // First check service type filter
        const matchesServiceType = serviceType === 'all' ||
            (record['Service Type'] && record['Service Type'] === serviceType);

        // If doesn't match service type, exclude immediately
        if (!matchesServiceType) return false;

        // If no search query, include all records that match service type
        if (!searchQuery) return true;

        // Check if any field contains the search query
        return (
            // Check License Plate
            (record['License Plate'] && record['License Plate'].toLowerCase().includes(query)) ||
            // Check Service Type
            (record['Service Type'] && record['Service Type'].toLowerCase().includes(query)) ||
            // Check Service Center
            (record['Service Center'] && record['Service Center'].toLowerCase().includes(query)) ||
            // Check Service Date
            (record['Service Date'] && record['Service Date'].toLowerCase().includes(query)) ||
            // Check Next Service Date
            (record['Next Service Date'] && record['Next Service Date'].toLowerCase().includes(query)) ||
            // Check Odometer Reading
            (record['Odometer Reading'] && record['Odometer Reading'].toString().includes(query)) ||
            // Check Total Cost
            (record['Total Cost'] && record['Total Cost'].toString().includes(query)) ||
            // Check Notes
            (record['Notes'] && record['Notes'].toLowerCase().includes(query))
        );
    });

    console.log('Found', filteredRecords.length, 'matching records');

    // Render the filtered records
    renderFilteredMaintenanceRecords(filteredRecords);
}

// Legacy function for backward compatibility
function searchMaintenanceRecords(query) {
    filterMaintenanceRecords(query, 'all');
}

// Function to render filtered maintenance records
function renderFilteredMaintenanceRecords(filteredRecords) {
    // Get the table body
    const tableBody = document.querySelector('#maintenance-records-table tbody');
    if (!tableBody) {
        console.error('Table body not found');
        return;
    }

    // Clear the table body
    tableBody.innerHTML = '';

    // Check if we have any filtered records
    if (!filteredRecords || filteredRecords.length === 0) {
        const emptyRow = document.createElement('tr');
        emptyRow.innerHTML = `<td colspan="8" style="text-align: center;">No matching records found</td>`;
        tableBody.appendChild(emptyRow);
        return;
    }

    // Sort records by date (newest first)
    const sortedRecords = [...filteredRecords].sort((a, b) => {
        return new Date(b['Service Date'] || '1970-01-01') - new Date(a['Service Date'] || '1970-01-01');
    });

    // Render each record
    sortedRecords.forEach((record, index) => {
        try {
            // Find the associated vehicle
            const vehicle = vehicles.find(v => v['Vehicle ID'] === record['Vehicle ID'] || v.id === record['Vehicle ID']);
            const vehicleName = record['License Plate'] || (vehicle ? (vehicle['License Plate'] || 'Unknown') : 'Unknown');

            // Format dates
            const formattedDate = new Date(record['Service Date'] || '1970-01-01').toLocaleDateString();
            const nextServiceDate = record['Next Service Date'] ? new Date(record['Next Service Date']).toLocaleDateString() : 'N/A';

            // Create a safe version of record with default values
            const safeRecord = {
                'Service Type': record['Service Type'] || 'N/A',
                'Service Center': record['Service Center'] || 'N/A',
                'Odometer Reading': record['Odometer Reading'] || 0,
                'Total Cost': record['Total Cost'] || 0,
                'Maintenance ID': record['Maintenance ID'] || ''
            };

            // Create the row
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${vehicleName}</td>
                <td>${formattedDate}</td>
                <td>${safeRecord['Service Type']}</td>
                <td>${safeRecord['Service Center']}</td>
                <td>${safeRecord['Odometer Reading'] > 0 ? safeRecord['Odometer Reading'].toLocaleString() + ' km' : 'N/A'}</td>
                <td>${nextServiceDate}</td>
                <td>${safeRecord['Total Cost'] > 0 ? '£' + safeRecord['Total Cost'].toLocaleString() : 'N/A'}</td>
                <td class="action-buttons">
                    ${hasPermission(currentUser?.role, PERMISSIONS.REGISTER_MAINTENANCE) ?
                    `<button class="btn btn-sm btn-primary edit-maintenance" data-id="${safeRecord['Maintenance ID']}">
                        <i class="fas fa-edit"></i> Edit
                    </button>` : ''}
                    ${hasPermission(currentUser?.role, PERMISSIONS.FULL_SYSTEM_CONTROL) ?
                    `<button class="btn btn-sm btn-danger delete-maintenance" data-id="${safeRecord['Maintenance ID']}">
                        <i class="fas fa-trash"></i> Delete
                    </button>` : ''}
                </td>
            `;

            tableBody.appendChild(row);
        } catch (recordError) {
            console.error('Error rendering maintenance record:', recordError, record);
        }
    });

    // Add event listeners to the buttons
    try {
        // Add event listeners to edit buttons
        tableBody.querySelectorAll('.edit-maintenance').forEach(button => {
            button.addEventListener('click', () => {
                const id = button.getAttribute('data-id');
                console.log('Edit button clicked for maintenance ID:', id);
                if (id && typeof openMaintenanceModal === 'function') {
                    openMaintenanceModal(id);
                } else {
                    console.error('Cannot open maintenance modal, function not available or ID missing');
                }
            });
        });

        // Add event listeners to delete buttons
        tableBody.querySelectorAll('.delete-maintenance').forEach(button => {
            button.addEventListener('click', () => {
                const id = button.getAttribute('data-id');
                console.log('Delete button clicked for maintenance ID:', id);
                if (id && typeof deleteMaintenanceRecord === 'function') {
                    deleteMaintenanceRecord(id);
                } else {
                    console.error('Cannot delete maintenance record, function not available or ID missing');
                }
            });
        });
    } catch (eventError) {
        console.error('Error adding event listeners to maintenance buttons:', eventError);
    }
}

// Function to export maintenance records to Excel
function exportMaintenanceToExcel(searchQuery = '', serviceType = 'all') {
    try {
        console.log('Exporting maintenance records to Excel - Search:', searchQuery, 'Service Type:', serviceType);

        // Get records to export (filtered or all)
        let recordsToExport = maintenanceRecords;

        // Apply filters if any
        if (searchQuery || serviceType !== 'all') {
            const query = searchQuery.toLowerCase();
            recordsToExport = maintenanceRecords.filter(record => {
                // First check service type filter
                const matchesServiceType = serviceType === 'all' ||
                    (record['Service Type'] && record['Service Type'] === serviceType);

                // If doesn't match service type, exclude immediately
                if (!matchesServiceType) return false;

                // If no search query, include all records that match service type
                if (!searchQuery) return true;

                // Check if any field contains the search query
                return (
                    // Check License Plate
                    (record['License Plate'] && record['License Plate'].toLowerCase().includes(query)) ||
                    // Check Service Type
                    (record['Service Type'] && record['Service Type'].toLowerCase().includes(query)) ||
                    // Check Service Center
                    (record['Service Center'] && record['Service Center'].toLowerCase().includes(query)) ||
                    // Check Service Date
                    (record['Service Date'] && record['Service Date'].toLowerCase().includes(query)) ||
                    // Check Next Service Date
                    (record['Next Service Date'] && record['Next Service Date'].toLowerCase().includes(query)) ||
                    // Check Odometer Reading
                    (record['Odometer Reading'] && record['Odometer Reading'].toString().includes(query)) ||
                    // Check Total Cost
                    (record['Total Cost'] && record['Total Cost'].toString().includes(query)) ||
                    // Check Notes
                    (record['Notes'] && record['Notes'].toLowerCase().includes(query))
                );
            });
        }

        // If no records to export, show notification and return
        if (!recordsToExport || recordsToExport.length === 0) {
            showNotification('No maintenance records to export', 'warning');
            return;
        }

        // Prepare data for export
        const data = recordsToExport.map(record => {
            // Find the associated vehicle
            const vehicle = vehicles.find(v => v['Vehicle ID'] === record['Vehicle ID'] || v.id === record['Vehicle ID']);
            const vehicleName = record['License Plate'] || (vehicle ? (vehicle['License Plate'] || 'Unknown') : 'Unknown');

            // Format dates
            const formattedDate = record['Service Date'] ? new Date(record['Service Date']).toLocaleDateString() : 'N/A';
            const nextServiceDate = record['Next Service Date'] ? new Date(record['Next Service Date']).toLocaleDateString() : 'N/A';

            // Return formatted data for Excel
            return {
                'License Plate': vehicleName,
                'Service Date': formattedDate,
                'Service Type': record['Service Type'] || 'N/A',
                'Service Center': record['Service Center'] || 'N/A',
                'Odometer Reading': record['Odometer Reading'] ? record['Odometer Reading'] + ' km' : 'N/A',
                'Next Service Date': nextServiceDate,
                'Parts Cost': record['Parts Cost'] ? '£' + record['Parts Cost'] : 'N/A',
                'Labor Cost': record['Labor Cost'] ? '£' + record['Labor Cost'] : 'N/A',
                'Total Cost': record['Total Cost'] ? '£' + record['Total Cost'] : 'N/A',
                'Notes': record['Notes'] || ''
            };
        });

        // Create worksheet
        const ws = XLSX.utils.json_to_sheet(data);

        // Set column widths
        const wscols = [
            { wch: 15 }, // License Plate
            { wch: 15 }, // Service Date
            { wch: 20 }, // Service Type
            { wch: 20 }, // Service Center
            { wch: 15 }, // Odometer Reading
            { wch: 15 }, // Next Service Date
            { wch: 12 }, // Parts Cost
            { wch: 12 }, // Labor Cost
            { wch: 12 }, // Total Cost
            { wch: 30 }  // Notes
        ];
        ws['!cols'] = wscols;

        // Create workbook
        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, 'Maintenance Records');

        // Generate filename with date
        const now = new Date();
        const dateStr = now.toISOString().split('T')[0]; // YYYY-MM-DD
        let filename = `maintenance_records_${dateStr}`;

        // Add filters to filename if provided
        if (searchQuery) {
            // Clean up search query for filename
            const cleanQuery = searchQuery.replace(/[^a-z0-9]/gi, '_').toLowerCase();
            filename += `_${cleanQuery}`;
        }

        // Add service type to filename if not 'all'
        if (serviceType && serviceType !== 'all') {
            const cleanServiceType = serviceType.replace(/[^a-z0-9]/gi, '_').toLowerCase();
            filename += `_${cleanServiceType}`;
        }

        // Save file
        XLSX.writeFile(wb, `${filename}.xlsx`);

        // Show success notification
        showNotification(`Exported ${data.length} maintenance records to Excel`, 'success');
    } catch (error) {
        console.error('Error exporting maintenance records to Excel:', error);
        showNotification('Error exporting to Excel: ' + error.message, 'error');
    }
}

// Function to render maintenance statistics
function renderMaintenanceStats() {
    console.log('Rendering maintenance statistics');

    // Get all services
    const services = getUpcomingServices();

    // Count total vehicles
    const totalVehicles = vehicles.length;

    // Initialize statistics object
    const stats = {
        maintenance: { required: 0, upcoming: 0, good: 0 },
        tires: { required: 0, upcoming: 0, good: 0 },
        license: { required: 0, upcoming: 0, good: 0 }
    };

    // Count services by type and status
    services.forEach(service => {
        if (stats[service.type]) {
            stats[service.type][service.statusInfo.category]++;
        }
    });

    // Calculate totals for each type
    const totals = {
        maintenance: Object.values(stats.maintenance).reduce((a, b) => a + b, 0),
        tires: Object.values(stats.tires).reduce((a, b) => a + b, 0),
        license: Object.values(stats.license).reduce((a, b) => a + b, 0)
    };

    // Calculate total required services
    const totalRequired = stats.maintenance.required + stats.tires.required + stats.license.required;

    // Update the statistics in the UI
    document.getElementById('total-vehicles-services').textContent = totalVehicles;
    document.getElementById('maintenance-count').textContent = stats.maintenance.required;
    document.getElementById('tires-count').textContent = stats.tires.required;
    document.getElementById('license-count').textContent = stats.license.required;
    document.getElementById('critical-count').textContent = totalRequired;

    // Calculate and update percentages (based on vehicles requiring service)
    document.getElementById('total-vehicles-percentage').textContent =
        Math.round((totalRequired > 0 ? totalRequired / totalVehicles * 100 : 0)) + '%';
    document.getElementById('maintenance-percentage').textContent =
        Math.round((stats.maintenance.required > 0 ? stats.maintenance.required / totalVehicles * 100 : 0)) + '%';
    document.getElementById('tires-percentage').textContent =
        Math.round((stats.tires.required > 0 ? stats.tires.required / totalVehicles * 100 : 0)) + '%';
    document.getElementById('license-percentage').textContent =
        Math.round((stats.license.required > 0 ? stats.license.required / totalVehicles * 100 : 0)) + '%';
    document.getElementById('critical-percentage').textContent =
        Math.round((totalRequired > 0 ? totalRequired / totalVehicles * 100 : 0)) + '%';

    // Update other maintenance statistics
    if (document.getElementById('total-services')) {
        document.getElementById('total-services').textContent = services.length;
    }
    if (document.getElementById('required-services')) {
        document.getElementById('required-services').textContent = totalRequired;
    }
    if (document.getElementById('upcoming-services-count')) {
        document.getElementById('upcoming-services-count').textContent =
            services.filter(s => s.statusInfo.category === 'upcoming').length;
    }

}

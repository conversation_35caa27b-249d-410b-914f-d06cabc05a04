/**
 * Utility Functions - دوال مساعدة عامة
 * يحتوي على دوال مساعدة مختلفة للاستخدام في التطبيق
 */

import { NOTIFICATION_TYPES, NOTIFICATION_DURATION, DATE_FORMATS } from './constants.js';

/**
 * تنسيق الأرقام مع الفواصل
 */
export function formatNumber(number) {
    if (number === undefined || number === null) return '0';
    return String(number).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
}

/**
 * تنسيق التاريخ
 */
export function formatDate(date, format = DATE_FORMATS.DISPLAY) {
    if (!date) return '';
    
    if (typeof date === 'string') {
        date = new Date(date);
    }
    
    if (!(date instanceof Date) || isNaN(date.getTime())) {
        return '';
    }
    
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    
    switch (format) {
        case DATE_FORMATS.API:
            return `${year}-${month}-${day}`;
        case DATE_FORMATS.DATETIME:
            return `${day}/${month}/${year} ${hours}:${minutes}`;
        case DATE_FORMATS.TIME:
            return `${hours}:${minutes}`;
        default:
            return `${day}/${month}/${year}`;
    }
}

/**
 * حساب الفرق بالأيام بين تاريخين
 */
export function getDaysDifference(startDate, endDate) {
    if (!startDate || !endDate) return 0;
    
    if (typeof startDate === 'string') {
        startDate = new Date(startDate);
    }
    
    if (typeof endDate === 'string') {
        endDate = new Date(endDate);
    }
    
    if (!(startDate instanceof Date) || isNaN(startDate.getTime()) || 
        !(endDate instanceof Date) || isNaN(endDate.getTime())) {
        return 0;
    }
    
    const timeDiff = endDate.getTime() - startDate.getTime();
    return Math.floor(timeDiff / (1000 * 60 * 60 * 24));
}

/**
 * الحصول على أيقونة المركبة حسب النوع
 */
export function getVehicleIcon(type) {
    if (!type) return 'fa-car';
    
    type = type.toLowerCase();
    
    if (type.includes('truck')) return 'fa-truck';
    if (type.includes('bus')) return 'fa-bus';
    if (type.includes('van')) return 'fa-shuttle-van';
    if (type.includes('motorcycle')) return 'fa-motorcycle';
    if (type.includes('suv')) return 'fa-truck-pickup';
    
    return 'fa-car';
}

/**
 * الحصول على فئة لون الحالة
 */
export function getStatusColorClass(status) {
    if (!status) return 'status-inactive';
    
    status = status.toLowerCase();
    
    if (status.includes('active')) return 'status-active';
    if (status.includes('maintenance')) return 'status-maintenance';
    if (status.includes('inactive')) return 'status-inactive';
    if (status.includes('repair')) return 'status-repair';
    
    return 'status-inactive';
}

/**
 * اقتطاع النص مع إضافة نقاط
 */
export function truncateText(text, maxLength = 50) {
    if (!text) return '';
    if (text.length <= maxLength) return text;
    
    return text.substring(0, maxLength) + '...';
}

/**
 * توليد معرف عشوائي
 */
export function generateId(prefix = '') {
    return prefix + Math.random().toString(36).substring(2, 10);
}

/**
 * التحقق من صحة البريد الإلكتروني
 */
export function isValidEmail(email) {
    if (!email) return false;
    const regex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return regex.test(email);
}

/**
 * تحويل البايتات إلى حجم قابل للقراءة
 */
export function formatBytes(bytes, decimals = 2) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(decimals)) + ' ' + sizes[i];
}

/**
 * تحميل بيانات JSON كملف
 */
export function downloadJSON(data, filename) {
    const blob = new Blob([JSON.stringify(data, null, 2)], {type: 'application/json'});
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    
    link.href = url;
    link.download = filename || 'data.json';
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
}

/**
 * الحصول على اسم الفرع من المركبة
 */
export function getBranchName(vehicle) {
    if (vehicle['Branch']) return vehicle['Branch'];
    
    const branchFields = [
        'branch', 'BRANCH', 'Branch Name', 'فرع', 'الفرع',
        'branch_name', 'BranchName'
    ];
    
    for (const field of branchFields) {
        if (vehicle[field]) return vehicle[field];
    }

    if (!vehicle['Branch']) {
        console.warn('Missing branch for vehicle:', vehicle['Vehicle ID'] || vehicle['License Plate']);
    }

    return 'غير محدد';
}

/**
 * نظام الإشعارات
 */
let notificationContainer = null;

function createNotificationContainer() {
    if (!notificationContainer) {
        notificationContainer = document.createElement('div');
        notificationContainer.id = 'notification-container';
        notificationContainer.className = 'notification-container';
        document.body.appendChild(notificationContainer);
    }
    return notificationContainer;
}

export function showNotification(message, type = NOTIFICATION_TYPES.INFO, duration = null) {
    const container = createNotificationContainer();
    const notification = document.createElement('div');
    const notificationId = generateId('notification-');
    
    notification.id = notificationId;
    notification.className = `notification notification-${type}`;
    
    const icon = getNotificationIcon(type);
    notification.innerHTML = `
        <div class="notification-content">
            <i class="${icon}"></i>
            <span class="notification-message">${message}</span>
            <button class="notification-close" onclick="removeNotification('${notificationId}')">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;
    
    container.appendChild(notification);
    
    // إضافة تأثير الظهور
    setTimeout(() => notification.classList.add('show'), 100);
    
    // إزالة تلقائية
    const autoRemoveDuration = duration || NOTIFICATION_DURATION[type] || 3000;
    setTimeout(() => removeNotification(notificationId), autoRemoveDuration);
    
    return notificationId;
}

function getNotificationIcon(type) {
    const icons = {
        [NOTIFICATION_TYPES.SUCCESS]: 'fas fa-check-circle',
        [NOTIFICATION_TYPES.ERROR]: 'fas fa-exclamation-circle',
        [NOTIFICATION_TYPES.WARNING]: 'fas fa-exclamation-triangle',
        [NOTIFICATION_TYPES.INFO]: 'fas fa-info-circle'
    };
    return icons[type] || icons[NOTIFICATION_TYPES.INFO];
}

export function removeNotification(notificationId) {
    const notification = document.getElementById(notificationId);
    if (notification) {
        notification.classList.add('hide');
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }
}

// إضافة دالة إزالة الإشعار للنطاق العام
window.removeNotification = removeNotification;

/**
 * دوال مساعدة للتوست
 */
export function showToast(message, type = NOTIFICATION_TYPES.INFO) {
    return showNotification(message, type);
}

export function showSuccess(message) {
    return showNotification(message, NOTIFICATION_TYPES.SUCCESS);
}

export function showError(message) {
    return showNotification(message, NOTIFICATION_TYPES.ERROR);
}

export function showWarning(message) {
    return showNotification(message, NOTIFICATION_TYPES.WARNING);
}

export function showInfo(message) {
    return showNotification(message, NOTIFICATION_TYPES.INFO);
}

/**
 * دوال مساعدة للتحقق من القيم
 */
export function isEmpty(value) {
    return value === null || value === undefined || value === '';
}

export function isNotEmpty(value) {
    return !isEmpty(value);
}

export function isNumber(value) {
    return !isNaN(value) && !isNaN(parseFloat(value));
}

/**
 * دوال مساعدة للنصوص
 */
export function capitalize(text) {
    if (!text) return '';
    return text.charAt(0).toUpperCase() + text.slice(1).toLowerCase();
}

export function slugify(text) {
    return text
        .toString()
        .toLowerCase()
        .replace(/\s+/g, '-')
        .replace(/[^\w\-]+/g, '')
        .replace(/\-\-+/g, '-')
        .replace(/^-+/, '')
        .replace(/-+$/, '');
}

/**
 * دوال مساعدة للمصفوفات
 */
export function groupBy(array, key) {
    return array.reduce((groups, item) => {
        const group = item[key];
        groups[group] = groups[group] || [];
        groups[group].push(item);
        return groups;
    }, {});
}

export function sortBy(array, key, direction = 'asc') {
    return array.sort((a, b) => {
        const aVal = a[key];
        const bVal = b[key];
        
        if (direction === 'desc') {
            return bVal > aVal ? 1 : -1;
        }
        return aVal > bVal ? 1 : -1;
    });
}

/**
 * دوال مساعدة للتخزين المحلي
 */
export function setLocalStorage(key, value) {
    try {
        localStorage.setItem(key, JSON.stringify(value));
        return true;
    } catch (error) {
        console.error('Error saving to localStorage:', error);
        return false;
    }
}

export function getLocalStorage(key, defaultValue = null) {
    try {
        const item = localStorage.getItem(key);
        return item ? JSON.parse(item) : defaultValue;
    } catch (error) {
        console.error('Error reading from localStorage:', error);
        return defaultValue;
    }
}

export function removeLocalStorage(key) {
    try {
        localStorage.removeItem(key);
        return true;
    } catch (error) {
        console.error('Error removing from localStorage:', error);
        return false;
    }
}

/**
 * دالة للتأخير
 */
export function delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * دالة لنسخ النص إلى الحافظة
 */
export async function copyToClipboard(text) {
    try {
        await navigator.clipboard.writeText(text);
        showSuccess('تم نسخ النص إلى الحافظة');
        return true;
    } catch (error) {
        console.error('Failed to copy text:', error);
        showError('فشل في نسخ النص');
        return false;
    }
}

export default {
    formatNumber,
    formatDate,
    getDaysDifference,
    getVehicleIcon,
    getStatusColorClass,
    truncateText,
    generateId,
    isValidEmail,
    formatBytes,
    downloadJSON,
    getBranchName,
    showNotification,
    showToast,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    isEmpty,
    isNotEmpty,
    isNumber,
    capitalize,
    slugify,
    groupBy,
    sortBy,
    setLocalStorage,
    getLocalStorage,
    removeLocalStorage,
    delay,
    copyToClipboard
};

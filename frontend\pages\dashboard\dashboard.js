/**
 * Dashboard Page - صفحة لوحة التحكم
 * يوفر عرض شامل لإحصائيات الأسطول والخدمات القادمة
 */

import { StatCard, FLEET_STAT_CARDS, createStatsGrid } from '../../components/StatCard.js';
import { ChartWrapper, ChartManager } from '../../components/ChartWrapper.js';
import { getVehicles, getVehicleStats, getUpcomingServices } from '../../services/dataService.js';
import { showNotification, getBranchName, groupBy } from '../../utils/utility.js';
import { getUpcomingServices as calculateUpcomingServices, getServicesStats } from '../../utils/upcoming-services.js';
import appStore, { storeSelectors } from '../../store/store.js';

// مدير الرسوم البيانية
const chartManager = new ChartManager();

// متغيرات الحالة
let currentBranch = 'all';
let dashboardData = {
    vehicles: [],
    stats: {},
    upcomingServices: []
};

/**
 * تهيئة صفحة لوحة التحكم
 */
export async function initializeDashboard() {
    try {
        console.log('Initializing dashboard...');
        
        // إنشاء هيكل الصفحة
        createDashboardStructure();
        
        // إنشاء فلتر الفروع
        createBranchFilter();
        
        // تحميل البيانات الأولية
        await loadDashboardData();
        
        // عرض البيانات
        renderDashboard();
        
        // ربط الأحداث
        bindEvents();
        
        console.log('Dashboard initialized successfully');
        
    } catch (error) {
        console.error('Error initializing dashboard:', error);
        showNotification('فشل في تحميل لوحة التحكم', 'error');
    }
}

/**
 * إنشاء هيكل صفحة لوحة التحكم
 */
function createDashboardStructure() {
    const dashboardPage = document.getElementById('dashboard-page');
    if (!dashboardPage) return;
    
    dashboardPage.innerHTML = `
        <div class="page-header">
            <h1 class="page-title">لوحة التحكم</h1>
            <div class="page-actions">
                <button id="refresh-dashboard" class="btn btn-primary">
                    <i class="fas fa-sync-alt"></i> تحديث البيانات
                </button>
            </div>
        </div>
        
        <!-- فلتر الفروع -->
        <div id="branch-filter-container"></div>
        
        <!-- إحصائيات المركبات -->
        <div class="section">
            <div class="section-header">
                <h2 class="section-title">إحصائيات المركبات</h2>
            </div>
            <div class="section-body">
                <div id="vehicle-stats-grid"></div>
            </div>
        </div>
        
        <!-- إحصائيات الخدمات -->
        <div class="section">
            <div class="section-header">
                <h2 class="section-title">إحصائيات الخدمات القادمة</h2>
            </div>
            <div class="section-body">
                <div id="services-stats-grid"></div>
            </div>
        </div>
        
        <!-- الرسوم البيانية -->
        <div class="section">
            <div class="section-header">
                <h2 class="section-title">نظرة عامة على الأسطول</h2>
            </div>
            <div class="section-body">
                <div class="dashboard-charts">
                    <div class="chart-container">
                        <h3>توزيع المركبات حسب الحالة</h3>
                        <canvas id="vehicle-status-chart"></canvas>
                    </div>
                    <div class="chart-container">
                        <h3>توزيع المركبات حسب الفرع</h3>
                        <canvas id="branch-distribution-chart"></canvas>
                    </div>
                    <div class="chart-container">
                        <h3>أنواع المركبات</h3>
                        <canvas id="vehicle-types-chart"></canvas>
                    </div>
                    <div class="chart-container">
                        <h3>الخدمات القادمة</h3>
                        <canvas id="upcoming-services-chart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- الخدمات القادمة -->
        <div class="section">
            <div class="section-header">
                <h2 class="section-title">الخدمات القادمة</h2>
                <div class="section-actions">
                    <select id="services-filter" class="form-control">
                        <option value="all">جميع الخدمات</option>
                        <option value="critical">الخدمات الحرجة</option>
                        <option value="upcoming">الخدمات القادمة</option>
                        <option value="maintenance">الصيانة</option>
                        <option value="tires">الإطارات</option>
                        <option value="license">تجديد الرخصة</option>
                    </select>
                </div>
            </div>
            <div class="section-body">
                <div id="upcoming-services-table-container">
                    <div class="table-responsive">
                        <table id="upcoming-services-table" class="table">
                            <thead>
                                <tr>
                                    <th>المركبة</th>
                                    <th>نوع الخدمة</th>
                                    <th>الفرع</th>
                                    <th>الحالة</th>
                                    <th>المتبقي</th>
                                    <th>التاريخ المتوقع</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    `;
}

/**
 * إنشاء فلتر الفروع
 */
function createBranchFilter() {
    const container = document.getElementById('branch-filter-container');
    if (!container) return;
    
    // الحصول على الفروع المتاحة
    const branches = [...new Set(dashboardData.vehicles.map(getBranchName))].sort();
    
    container.innerHTML = `
        <div class="filter-bar">
            <div class="filter-group">
                <label for="branch-select">فلتر الفرع:</label>
                <select id="branch-select" class="form-control">
                    <option value="all">جميع الفروع</option>
                    ${branches.map(branch => 
                        `<option value="${branch}" ${branch === currentBranch ? 'selected' : ''}>${branch}</option>`
                    ).join('')}
                </select>
            </div>
            <div class="filter-group">
                <label>إجمالي المركبات:</label>
                <span id="total-vehicles-count" class="badge badge-primary">0</span>
            </div>
        </div>
    `;
}

/**
 * تحميل بيانات لوحة التحكم
 */
async function loadDashboardData() {
    try {
        // تحميل المركبات
        dashboardData.vehicles = await getVehicles();
        
        // حساب الإحصائيات
        dashboardData.stats = await getVehicleStats();
        
        // حساب الخدمات القادمة
        dashboardData.upcomingServices = calculateUpcomingServices(dashboardData.vehicles, currentBranch);
        
        console.log('Dashboard data loaded:', {
            vehicles: dashboardData.vehicles.length,
            stats: dashboardData.stats,
            services: dashboardData.upcomingServices.length
        });
        
    } catch (error) {
        console.error('Error loading dashboard data:', error);
        throw error;
    }
}

/**
 * عرض لوحة التحكم
 */
function renderDashboard() {
    try {
        // تصفية البيانات حسب الفرع المحدد
        const filteredVehicles = filterVehiclesByBranch(dashboardData.vehicles, currentBranch);
        const filteredServices = dashboardData.upcomingServices.filter(service => 
            currentBranch === 'all' || service.vehicle.branch === currentBranch
        );
        
        // عرض الإحصائيات
        renderVehicleStats(filteredVehicles);
        renderServicesStats(filteredServices);
        
        // عرض الرسوم البيانية
        renderCharts(filteredVehicles, filteredServices);
        
        // عرض جدول الخدمات القادمة
        renderUpcomingServicesTable(filteredServices);
        
        // تحديث عداد المركبات
        updateVehicleCount(filteredVehicles.length);
        
    } catch (error) {
        console.error('Error rendering dashboard:', error);
        showNotification('فشل في عرض بيانات لوحة التحكم', 'error');
    }
}

/**
 * عرض إحصائيات المركبات
 */
function renderVehicleStats(vehicles) {
    const container = document.getElementById('vehicle-stats-grid');
    if (!container) return;
    
    // حساب الإحصائيات
    const stats = {
        total: vehicles.length,
        active: vehicles.filter(v => v['Vehicle Status']?.toLowerCase() === 'active').length,
        inactive: vehicles.filter(v => v['Vehicle Status']?.toLowerCase() === 'inactive').length,
        maintenance: vehicles.filter(v => v['Vehicle Status']?.toLowerCase() === 'maintenance').length
    };
    
    // إنشاء البطاقات
    const statsArray = [
        {
            title: 'إجمالي المركبات',
            value: stats.total,
            icon: 'fas fa-car',
            color: 'blue'
        },
        {
            title: 'المركبات النشطة',
            value: stats.active,
            icon: 'fas fa-truck',
            color: 'green',
            trend: stats.total > 0 ? { value: Math.round((stats.active / stats.total) * 100), direction: 'up' } : null
        },
        {
            title: 'في الصيانة',
            value: stats.maintenance,
            icon: 'fas fa-tools',
            color: 'orange'
        },
        {
            title: 'المركبات المتوقفة',
            value: stats.inactive,
            icon: 'fas fa-times-circle',
            color: 'red'
        }
    ];
    
    // مسح المحتوى السابق
    container.innerHTML = '';
    
    // إنشاء الشبكة
    createStatsGrid(container, statsArray);
}

/**
 * عرض إحصائيات الخدمات
 */
function renderServicesStats(services) {
    const container = document.getElementById('services-stats-grid');
    if (!container) return;
    
    const stats = getServicesStats(services);
    
    const statsArray = [
        {
            title: 'إجمالي الخدمات',
            value: stats.total,
            icon: 'fas fa-cogs',
            color: 'blue'
        },
        {
            title: 'الخدمات الحرجة',
            value: stats.critical,
            icon: 'fas fa-exclamation-triangle',
            color: 'red'
        },
        {
            title: 'الصيانة',
            value: stats.maintenance,
            icon: 'fas fa-tools',
            color: 'orange'
        },
        {
            title: 'تجديد الرخص',
            value: stats.license,
            icon: 'fas fa-id-card',
            color: 'green'
        }
    ];
    
    container.innerHTML = '';
    createStatsGrid(container, statsArray);
}

/**
 * عرض الرسوم البيانية
 */
async function renderCharts(vehicles, services) {
    try {
        // رسم توزيع المركبات حسب الحالة
        await renderVehicleStatusChart(vehicles);
        
        // رسم توزيع المركبات حسب الفرع
        await renderBranchDistributionChart(vehicles);
        
        // رسم أنواع المركبات
        await renderVehicleTypesChart(vehicles);
        
        // رسم الخدمات القادمة
        await renderUpcomingServicesChart(services);
        
    } catch (error) {
        console.error('Error rendering charts:', error);
    }
}

/**
 * رسم توزيع المركبات حسب الحالة
 */
async function renderVehicleStatusChart(vehicles) {
    const statusCounts = groupBy(vehicles, 'Vehicle Status');
    const data = {
        labels: Object.keys(statusCounts),
        values: Object.values(statusCounts).map(group => group.length)
    };
    
    await chartManager.addChart(
        'vehicle-status',
        'vehicle-status-chart',
        'pie',
        data,
        { title: 'توزيع المركبات حسب الحالة' }
    );
}

/**
 * رسم توزيع المركبات حسب الفرع
 */
async function renderBranchDistributionChart(vehicles) {
    const branchCounts = groupBy(vehicles, getBranchName);
    const data = {
        labels: Object.keys(branchCounts),
        values: Object.values(branchCounts).map(group => group.length)
    };
    
    await chartManager.addChart(
        'branch-distribution',
        'branch-distribution-chart',
        'bar',
        data,
        { title: 'توزيع المركبات حسب الفرع' }
    );
}

/**
 * تصفية المركبات حسب الفرع
 */
function filterVehiclesByBranch(vehicles, branch) {
    if (branch === 'all') return vehicles;
    return vehicles.filter(vehicle => getBranchName(vehicle) === branch);
}

/**
 * ربط الأحداث
 */
function bindEvents() {
    // فلتر الفروع
    const branchSelect = document.getElementById('branch-select');
    if (branchSelect) {
        branchSelect.addEventListener('change', (e) => {
            currentBranch = e.target.value;
            renderDashboard();
        });
    }
    
    // تحديث البيانات
    const refreshBtn = document.getElementById('refresh-dashboard');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', async () => {
            await loadDashboardData();
            renderDashboard();
            showNotification('تم تحديث البيانات بنجاح', 'success');
        });
    }
    
    // فلتر الخدمات
    const servicesFilter = document.getElementById('services-filter');
    if (servicesFilter) {
        servicesFilter.addEventListener('change', (e) => {
            filterUpcomingServices(e.target.value);
        });
    }
}

/**
 * تحديث عداد المركبات
 */
function updateVehicleCount(count) {
    const counter = document.getElementById('total-vehicles-count');
    if (counter) {
        counter.textContent = count;
    }
}

/**
 * دالة الرندر الرئيسية للصفحة
 */
export async function render(container) {
    if (container) {
        // إذا تم تمرير حاوية، استخدمها
        container.innerHTML = '';
        await initializeDashboard();
    } else {
        // وإلا ابحث عن الصفحة في DOM
        await initializeDashboard();
    }
}

// تصدير الدوال للاستخدام الخارجي
export {
    loadDashboardData,
    renderDashboard,
    filterVehiclesByBranch
};

export default {
    render,
    initializeDashboard,
    loadDashboardData,
    renderDashboard
};

// Role-based permissions configuration
export const ROLES = {
    SUPER_ADMIN: 'Super Admin',
    GENERAL_MANAGER: 'General Manager',
    OPERATIONS_MANAGER: 'Operations Manager',
    FLEET_MANAGER: 'Fleet Manager',
    FLEET_SUPERVISOR: 'Fleet Supervisor',
    FLEET_STAFF: 'Fleet Staff',
    DRIVER: 'Driver'
};

// Permissions definitions
export const PERMISSIONS = {
    FULL_SYSTEM_CONTROL: 'Full system control',
    <PERSON><PERSON><PERSON>_SYSTEM_CODE: 'Manage system code',
    MANAGE_USERS: 'Manage users',
    ASSIGN_SUPERVISORS_MANAGERS: 'Assign supervisors and managers',
    MANAGE_VEHICLES_ALL_BRANCHES: 'Manage vehicles (all branches)',
    MANAGE_VEHICLES_BRANCH: 'Manage vehicles (branch only)',
    REGISTER_MAINTENANCE: 'Register maintenance',
    RECEIVE_MAINTENANCE_ALERTS: 'Receive maintenance alerts',
    UPDATE_MILEAGE_ALL_BRANCHES: 'Update mileage (all branches)',
    UPDATE_MILEAGE_BRANCH: 'Update mileage (branch only)',
    UPDATE_MILEAGE_ASSIGNED: 'Update mileage (assigned vehicle only)',
    VIEW_REPORTS: 'View reports',
    GRANT_TEMPORARY_PERMISSIONS: 'Grant temporary permissions'
};

// Permissions for viewing and editing users
export const USER_PERMISSIONS = {
    VIEW_ALL_USERS: 'View all users',
    EDIT_ALL_USERS: 'Edit all users',
    EDIT_OPERATIONS_MANAGER_BRANCH: 'Edit Operations Manager branch',
    EDIT_FLEET_MANAGER_BRANCH: 'Edit Fleet Manager branch',
    EDIT_FLEET_SUPERVISOR_BRANCH: 'Edit Fleet Supervisor branch',
    EDIT_FLEET_STAFF_BRANCH: 'Edit Fleet Staff branch',
    EDIT_DRIVER_BRANCH: 'Edit Driver branch'
};

// Role-based permissions matrix
export const ROLE_PERMISSIONS = {
    [ROLES.SUPER_ADMIN]: [
        PERMISSIONS.FULL_SYSTEM_CONTROL,
        PERMISSIONS.MANAGE_SYSTEM_CODE,
        PERMISSIONS.MANAGE_USERS,
        PERMISSIONS.ASSIGN_SUPERVISORS_MANAGERS,
        PERMISSIONS.MANAGE_VEHICLES_ALL_BRANCHES,
        PERMISSIONS.MANAGE_VEHICLES_BRANCH,
        PERMISSIONS.REGISTER_MAINTENANCE,
        PERMISSIONS.RECEIVE_MAINTENANCE_ALERTS,
        PERMISSIONS.UPDATE_MILEAGE_ALL_BRANCHES,
        PERMISSIONS.UPDATE_MILEAGE_BRANCH,
        PERMISSIONS.VIEW_REPORTS,
        PERMISSIONS.GRANT_TEMPORARY_PERMISSIONS,
        USER_PERMISSIONS.VIEW_ALL_USERS,
        USER_PERMISSIONS.EDIT_ALL_USERS
    ],
    [ROLES.GENERAL_MANAGER]: [
        PERMISSIONS.MANAGE_USERS,
        PERMISSIONS.ASSIGN_SUPERVISORS_MANAGERS,
        PERMISSIONS.MANAGE_VEHICLES_ALL_BRANCHES,
        PERMISSIONS.MANAGE_VEHICLES_BRANCH,
        PERMISSIONS.REGISTER_MAINTENANCE,
        PERMISSIONS.RECEIVE_MAINTENANCE_ALERTS,
        PERMISSIONS.UPDATE_MILEAGE_ALL_BRANCHES,
        PERMISSIONS.UPDATE_MILEAGE_BRANCH,
        PERMISSIONS.VIEW_REPORTS,
        PERMISSIONS.GRANT_TEMPORARY_PERMISSIONS,
        USER_PERMISSIONS.VIEW_ALL_USERS,
        USER_PERMISSIONS.EDIT_OPERATIONS_MANAGER_BRANCH,
        USER_PERMISSIONS.EDIT_FLEET_MANAGER_BRANCH
    ],
    [ROLES.OPERATIONS_MANAGER]: [
        PERMISSIONS.ASSIGN_SUPERVISORS_MANAGERS,
        PERMISSIONS.MANAGE_VEHICLES_ALL_BRANCHES,
        PERMISSIONS.MANAGE_VEHICLES_BRANCH,
        PERMISSIONS.REGISTER_MAINTENANCE,
        PERMISSIONS.RECEIVE_MAINTENANCE_ALERTS,
        PERMISSIONS.UPDATE_MILEAGE_ALL_BRANCHES,
        PERMISSIONS.UPDATE_MILEAGE_BRANCH,
        PERMISSIONS.VIEW_REPORTS,
        USER_PERMISSIONS.VIEW_ALL_USERS,
        USER_PERMISSIONS.EDIT_FLEET_SUPERVISOR_BRANCH,
        USER_PERMISSIONS.EDIT_FLEET_STAFF_BRANCH,
        USER_PERMISSIONS.EDIT_DRIVER_BRANCH
    ],
    [ROLES.FLEET_MANAGER]: [
        PERMISSIONS.ASSIGN_SUPERVISORS_MANAGERS,
        PERMISSIONS.MANAGE_VEHICLES_ALL_BRANCHES,
        PERMISSIONS.MANAGE_VEHICLES_BRANCH,
        PERMISSIONS.REGISTER_MAINTENANCE,
        PERMISSIONS.RECEIVE_MAINTENANCE_ALERTS,
        PERMISSIONS.UPDATE_MILEAGE_ALL_BRANCHES,
        PERMISSIONS.UPDATE_MILEAGE_BRANCH,
        PERMISSIONS.VIEW_REPORTS,
        USER_PERMISSIONS.VIEW_ALL_USERS,
        USER_PERMISSIONS.EDIT_FLEET_SUPERVISOR_BRANCH,
        USER_PERMISSIONS.EDIT_FLEET_STAFF_BRANCH,
        USER_PERMISSIONS.EDIT_DRIVER_BRANCH
    ],
    [ROLES.FLEET_SUPERVISOR]: [
        PERMISSIONS.MANAGE_VEHICLES_BRANCH,
        PERMISSIONS.REGISTER_MAINTENANCE,
        PERMISSIONS.RECEIVE_MAINTENANCE_ALERTS,
        PERMISSIONS.UPDATE_MILEAGE_ALL_BRANCHES,
        PERMISSIONS.UPDATE_MILEAGE_BRANCH,
        USER_PERMISSIONS.VIEW_ALL_USERS,
        USER_PERMISSIONS.EDIT_FLEET_STAFF_BRANCH,
        USER_PERMISSIONS.EDIT_DRIVER_BRANCH
    ],
    [ROLES.FLEET_STAFF]: [
        PERMISSIONS.UPDATE_MILEAGE_ALL_BRANCHES,
        PERMISSIONS.UPDATE_MILEAGE_BRANCH
    ],
    [ROLES.DRIVER]: [
        PERMISSIONS.UPDATE_MILEAGE_ASSIGNED
    ]
};

// Function to check if a user has a specific permission
export function hasPermission(userRole, permission) {
    if (!userRole || !permission) return false;

    // Get permissions for the user's role
    const permissions = ROLE_PERMISSIONS[userRole] || [];

    // Check if the permission exists in the user's role permissions
    return permissions.includes(permission);
}

// Function to check if a user has any of the specified permissions
export function hasAnyPermission(userRole, permissions) {
    if (!userRole || !permissions || !Array.isArray(permissions)) return false;

    // Get permissions for the user's role
    const userPermissions = ROLE_PERMISSIONS[userRole] || [];

    // Check if any of the specified permissions exist in the user's role permissions
    return permissions.some(permission => userPermissions.includes(permission));
}

// Function to check if a user has all of the specified permissions
export function hasAllPermissions(userRole, permissions) {
    if (!userRole || !permissions || !Array.isArray(permissions)) return false;

    // Get permissions for the user's role
    const userPermissions = ROLE_PERMISSIONS[userRole] || [];

    // Check if all of the specified permissions exist in the user's role permissions
    return permissions.every(permission => userPermissions.includes(permission));
}

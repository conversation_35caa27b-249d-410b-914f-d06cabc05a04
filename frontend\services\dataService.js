/**
 * Data Service - خدمة إدارة البيانات
 * يوفر دوال للحصول على البيانات ومعالجتها وتخزينها مؤقتاً
 */

import { fleetAPI } from './api.js';

export class DataService {
    constructor() {
        this.cache = new Map();
        this.cacheTimeout = 5 * 60 * 1000; // 5 دقائق
        this.subscribers = new Map();
    }

    /**
     * الحصول على البيانات مع التخزين المؤقت
     */
    async getData(key, fetchFunction, forceRefresh = false) {
        const cacheKey = `data_${key}`;
        const cached = this.cache.get(cacheKey);
        
        // التحقق من صحة البيانات المخزنة مؤقتاً
        if (!forceRefresh && cached && (Date.now() - cached.timestamp) < this.cacheTimeout) {
            return cached.data;
        }

        try {
            const data = await fetchFunction();
            
            // تخزين البيانات مؤقتاً
            this.cache.set(cacheKey, {
                data,
                timestamp: Date.now()
            });

            // إشعار المشتركين
            this.notifySubscribers(key, data);
            
            return data;
        } catch (error) {
            console.error(`Failed to fetch ${key}:`, error);
            
            // إرجاع البيانات المخزنة مؤقتاً إذا كانت متوفرة
            if (cached) {
                console.warn(`Using cached data for ${key}`);
                return cached.data;
            }
            
            throw error;
        }
    }

    /**
     * مسح البيانات المخزنة مؤقتاً
     */
    clearCache(key = null) {
        if (key) {
            this.cache.delete(`data_${key}`);
        } else {
            this.cache.clear();
        }
    }

    /**
     * الاشتراك في تحديثات البيانات
     */
    subscribe(key, callback) {
        if (!this.subscribers.has(key)) {
            this.subscribers.set(key, new Set());
        }
        this.subscribers.get(key).add(callback);
        
        // إرجاع دالة لإلغاء الاشتراك
        return () => {
            const keySubscribers = this.subscribers.get(key);
            if (keySubscribers) {
                keySubscribers.delete(callback);
            }
        };
    }

    /**
     * إشعار المشتركين بتحديث البيانات
     */
    notifySubscribers(key, data) {
        const keySubscribers = this.subscribers.get(key);
        if (keySubscribers) {
            keySubscribers.forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error('Subscriber callback error:', error);
                }
            });
        }
    }

    // دوال البيانات المحددة للمشروع

    /**
     * الحصول على المركبات
     */
    async getVehicles(forceRefresh = false) {
        return await this.getData('vehicles', () => fleetAPI.getVehicles(), forceRefresh);
    }

    /**
     * الحصول على مركبة محددة
     */
    async getVehicleById(vehicleId) {
        const vehicles = await this.getVehicles();
        return vehicles.find(vehicle => vehicle.id === vehicleId);
    }

    /**
     * الحصول على السائقين
     */
    async getDrivers(forceRefresh = false) {
        return await this.getData('drivers', () => fleetAPI.getDrivers(), forceRefresh);
    }

    /**
     * الحصول على سجلات الصيانة
     */
    async getMaintenanceRecords(forceRefresh = false) {
        return await this.getData('maintenance', () => fleetAPI.getMaintenanceRecords(), forceRefresh);
    }

    /**
     * الحصول على سجلات الصيانة لمركبة محددة
     */
    async getMaintenanceByVehicleId(vehicleId) {
        const maintenanceRecords = await this.getMaintenanceRecords();
        return maintenanceRecords.filter(record => record.vehicleId === vehicleId);
    }

    /**
     * الحصول على سجلات الوقود
     */
    async getFuelRecords(forceRefresh = false) {
        return await this.getData('fuel', () => fleetAPI.getFuelRecords(), forceRefresh);
    }

    /**
     * الحصول على المستخدمين
     */
    async getUsers(forceRefresh = false) {
        return await this.getData('users', () => fleetAPI.getUsers(), forceRefresh);
    }

    /**
     * إضافة مركبة جديدة
     */
    async addVehicle(vehicleData) {
        const result = await fleetAPI.addVehicle(vehicleData);
        if (result.success) {
            this.clearCache('vehicles');
        }
        return result;
    }

    /**
     * تحديث مركبة
     */
    async updateVehicle(vehicleId, vehicleData) {
        const result = await fleetAPI.updateVehicle(vehicleId, vehicleData);
        if (result.success) {
            this.clearCache('vehicles');
        }
        return result;
    }

    /**
     * حذف مركبة
     */
    async deleteVehicle(vehicleId) {
        const result = await fleetAPI.deleteVehicle(vehicleId);
        if (result.success) {
            this.clearCache('vehicles');
        }
        return result;
    }

    /**
     * إضافة سجل صيانة
     */
    async addMaintenanceRecord(maintenanceData) {
        const result = await fleetAPI.addMaintenanceRecord(maintenanceData);
        if (result.success) {
            this.clearCache('maintenance');
            this.clearCache('vehicles'); // قد تتأثر بيانات المركبات
        }
        return result;
    }

    /**
     * إضافة سجل وقود
     */
    async addFuelRecord(fuelData) {
        const result = await fleetAPI.addFuelRecord(fuelData);
        if (result.success) {
            this.clearCache('fuel');
            this.clearCache('vehicles'); // قد تتأثر بيانات المركبات
        }
        return result;
    }

    /**
     * الحصول على إحصائيات المركبات
     */
    async getVehicleStats() {
        const vehicles = await this.getVehicles();
        
        const stats = {
            total: vehicles.length,
            active: vehicles.filter(v => v.status === 'active').length,
            inactive: vehicles.filter(v => v.status === 'inactive').length,
            inMaintenance: vehicles.filter(v => v.status === 'maintenance').length
        };

        return stats;
    }

    /**
     * الحصول على الخدمات القادمة
     */
    async getUpcomingServices() {
        const vehicles = await this.getVehicles();
        const maintenanceRecords = await this.getMaintenanceRecords();
        
        const upcomingServices = [];
        
        vehicles.forEach(vehicle => {
            // حساب الصيانة القادمة
            const lastMaintenance = maintenanceRecords
                .filter(record => record.vehicleId === vehicle.id && record.serviceType === 'maintenance')
                .sort((a, b) => new Date(b.serviceDate) - new Date(a.serviceDate))[0];
            
            if (lastMaintenance && vehicle.currentMileage) {
                const nextMaintenanceMileage = lastMaintenance.nextServiceOdometer;
                const remainingKm = nextMaintenanceMileage - vehicle.currentMileage;
                
                if (remainingKm <= 1000) { // إذا كان أقل من 1000 كم
                    upcomingServices.push({
                        vehicleId: vehicle.id,
                        vehicleName: vehicle.licensePlate,
                        serviceType: 'maintenance',
                        remainingKm,
                        status: remainingKm <= 0 ? 'overdue' : 'due'
                    });
                }
            }
        });
        
        return upcomingServices;
    }

    /**
     * البحث في البيانات
     */
    async searchVehicles(query) {
        const vehicles = await this.getVehicles();
        const searchTerm = query.toLowerCase();
        
        return vehicles.filter(vehicle => 
            vehicle.licensePlate?.toLowerCase().includes(searchTerm) ||
            vehicle.make?.toLowerCase().includes(searchTerm) ||
            vehicle.model?.toLowerCase().includes(searchTerm) ||
            vehicle.location?.toLowerCase().includes(searchTerm)
        );
    }
}

// إنشاء instance مشترك
const dataService = new DataService();

// دوال مساعدة للاستخدام السريع
export async function getVehicles(forceRefresh = false) {
    return await dataService.getVehicles(forceRefresh);
}

export async function getVehicleById(vehicleId) {
    return await dataService.getVehicleById(vehicleId);
}

export async function getDrivers(forceRefresh = false) {
    return await dataService.getDrivers(forceRefresh);
}

export async function getMaintenanceRecords(forceRefresh = false) {
    return await dataService.getMaintenanceRecords(forceRefresh);
}

export async function getMaintenanceByVehicleId(vehicleId) {
    return await dataService.getMaintenanceByVehicleId(vehicleId);
}

export async function getFuelRecords(forceRefresh = false) {
    return await dataService.getFuelRecords(forceRefresh);
}

export async function getVehicleStats() {
    return await dataService.getVehicleStats();
}

export async function getUpcomingServices() {
    return await dataService.getUpcomingServices();
}

export function subscribeToData(key, callback) {
    return dataService.subscribe(key, callback);
}

export function clearDataCache(key = null) {
    dataService.clearCache(key);
}

export default dataService;

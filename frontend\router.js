/**
 * Router - نظام التنقل بدون إعادة تحميل
 * يوفر تنقل سلس بين صفحات التطبيق مع إدارة التاريخ والحالة
 */

import appStore, { storeActions } from './store/store.js';
import { canAccessPage } from './utils/permissions.js';
import { showNotification } from './utils/utility.js';

export class Router {
    constructor() {
        this.routes = new Map();
        this.currentRoute = null;
        this.defaultRoute = 'dashboard';
        this.beforeRouteChange = null;
        this.afterRouteChange = null;
        
        // ربط الأحداث
        this.bindEvents();
        
        // تحديد الصفحة الحالية من URL
        this.initializeFromURL();
    }

    /**
     * تسجيل مسار جديد
     */
    register(path, config) {
        this.routes.set(path, {
            component: config.component,
            title: config.title || path,
            requiresAuth: config.requiresAuth !== false,
            permissions: config.permissions || [],
            beforeEnter: config.beforeEnter || null,
            afterEnter: config.afterEnter || null,
            ...config
        });
    }

    /**
     * التنقل إلى مسار محدد
     */
    async navigate(path, options = {}) {
        try {
            const route = this.routes.get(path);
            
            if (!route) {
                console.error(`Route not found: ${path}`);
                this.navigate(this.defaultRoute);
                return false;
            }

            // التحقق من المصادقة
            if (route.requiresAuth && !this.isAuthenticated()) {
                showNotification('يجب تسجيل الدخول أولاً', 'warning');
                this.navigate('login');
                return false;
            }

            // التحقق من الصلاحيات
            if (route.permissions.length > 0 && !this.hasPermissions(route.permissions)) {
                showNotification('ليس لديك صلاحية للوصول لهذه الصفحة', 'error');
                return false;
            }

            // تنفيذ beforeRouteChange العام
            if (this.beforeRouteChange) {
                const canProceed = await this.beforeRouteChange(this.currentRoute, route);
                if (!canProceed) return false;
            }

            // تنفيذ beforeEnter للمسار
            if (route.beforeEnter) {
                const canProceed = await route.beforeEnter(this.currentRoute, route);
                if (!canProceed) return false;
            }

            // إخفاء جميع الصفحات
            this.hideAllPages();

            // تحديث URL إذا لم يكن من التاريخ
            if (!options.fromHistory) {
                this.updateURL(path);
            }

            // تحديث الصفحة الحالية
            this.currentRoute = { path, ...route };

            // تحديث العنوان
            document.title = `${route.title} - Fleet Management System`;

            // تحديث الحالة في Store
            storeActions.setCurrentPage(path);

            // تحديث التنقل النشط
            this.updateActiveNavigation(path);

            // عرض الصفحة
            await this.showPage(path, route);

            // تنفيذ afterEnter للمسار
            if (route.afterEnter) {
                await route.afterEnter(route);
            }

            // تنفيذ afterRouteChange العام
            if (this.afterRouteChange) {
                await this.afterRouteChange(this.currentRoute);
            }

            return true;

        } catch (error) {
            console.error('Navigation error:', error);
            showNotification('حدث خطأ أثناء التنقل', 'error');
            return false;
        }
    }

    /**
     * إخفاء جميع الصفحات
     */
    hideAllPages() {
        const pages = document.querySelectorAll('.page');
        pages.forEach(page => {
            page.classList.remove('active');
            page.style.display = 'none';
        });
    }

    /**
     * عرض صفحة محددة
     */
    async showPage(path, route) {
        const pageElement = document.getElementById(`${path}-page`);
        
        if (pageElement) {
            pageElement.style.display = 'block';
            pageElement.classList.add('active');
            
            // تحديث عنوان الصفحة في الهيدر
            const pageTitle = document.getElementById('current-page-title');
            if (pageTitle) {
                pageTitle.textContent = route.title;
            }
        }

        // تحميل المكون إذا كان محدداً
        if (route.component) {
            try {
                const module = await import(route.component);
                if (module.default && typeof module.default.render === 'function') {
                    await module.default.render(pageElement);
                } else if (module.render && typeof module.render === 'function') {
                    await module.render(pageElement);
                }
            } catch (error) {
                console.error(`Error loading component for ${path}:`, error);
            }
        }
    }

    /**
     * تحديث التنقل النشط
     */
    updateActiveNavigation(path) {
        // إزالة الفئة النشطة من جميع روابط التنقل
        const navLinks = document.querySelectorAll('.sidebar-nav a');
        navLinks.forEach(link => link.classList.remove('active'));

        // إضافة الفئة النشطة للرابط الحالي
        const activeLink = document.querySelector(`.sidebar-nav a[data-page="${path}"]`);
        if (activeLink) {
            activeLink.classList.add('active');
        }
    }

    /**
     * تحديث URL
     */
    updateURL(path) {
        const url = `#/${path}`;
        if (window.location.hash !== url) {
            history.pushState({ path }, '', url);
        }
    }

    /**
     * ربط الأحداث
     */
    bindEvents() {
        // الاستماع لتغييرات التاريخ
        window.addEventListener('popstate', (event) => {
            const path = this.getPathFromURL();
            this.navigate(path, { fromHistory: true });
        });

        // الاستماع لنقرات روابط التنقل
        document.addEventListener('click', (event) => {
            const link = event.target.closest('[data-page]');
            if (link) {
                event.preventDefault();
                const path = link.getAttribute('data-page');
                this.navigate(path);
            }
        });

        // الاستماع لتغييرات المصادقة
        document.addEventListener('auth:login', () => {
            // إعادة التوجيه للصفحة الرئيسية بعد تسجيل الدخول
            this.navigate(this.defaultRoute);
        });

        document.addEventListener('auth:logout', () => {
            // إعادة التوجيه لصفحة تسجيل الدخول
            this.navigate('login');
        });
    }

    /**
     * تحديد الصفحة الحالية من URL
     */
    initializeFromURL() {
        const path = this.getPathFromURL();
        if (path && this.routes.has(path)) {
            this.navigate(path, { fromHistory: true });
        } else {
            this.navigate(this.defaultRoute);
        }
    }

    /**
     * استخراج المسار من URL
     */
    getPathFromURL() {
        const hash = window.location.hash;
        if (hash.startsWith('#/')) {
            return hash.substring(2);
        }
        return this.defaultRoute;
    }

    /**
     * التحقق من المصادقة
     */
    isAuthenticated() {
        const state = appStore.getState();
        return state.user.isAuthenticated;
    }

    /**
     * التحقق من الصلاحيات
     */
    hasPermissions(permissions) {
        const state = appStore.getState();
        const userRole = state.user.currentUser?.role;
        
        if (!userRole) return false;
        
        return permissions.every(permission => 
            canAccessPage(userRole, permission)
        );
    }

    /**
     * الحصول على المسار الحالي
     */
    getCurrentRoute() {
        return this.currentRoute;
    }

    /**
     * العودة للصفحة السابقة
     */
    goBack() {
        history.back();
    }

    /**
     * الانتقال للصفحة التالية
     */
    goForward() {
        history.forward();
    }

    /**
     * إعادة تحميل الصفحة الحالية
     */
    reload() {
        if (this.currentRoute) {
            this.navigate(this.currentRoute.path);
        }
    }

    /**
     * تسجيل middleware للتنفيذ قبل تغيير المسار
     */
    beforeEach(callback) {
        this.beforeRouteChange = callback;
    }

    /**
     * تسجيل middleware للتنفيذ بعد تغيير المسار
     */
    afterEach(callback) {
        this.afterRouteChange = callback;
    }
}

// إنشاء instance مشترك للراوتر
const router = new Router();

// تسجيل المسارات الافتراضية
router.register('dashboard', {
    title: 'لوحة التحكم',
    component: './pages/dashboard/dashboard.js',
    permissions: ['dashboard']
});

router.register('vehicles', {
    title: 'المركبات',
    component: './pages/vehicles/vehicles.js',
    permissions: ['vehicles']
});

router.register('maintenance', {
    title: 'الصيانة',
    component: './pages/maintenance/maintenance.js',
    permissions: ['maintenance']
});

router.register('fuel', {
    title: 'الوقود',
    component: './pages/fuel/fuel.js',
    permissions: ['fuel']
});

router.register('drivers', {
    title: 'السائقين',
    component: './pages/drivers/drivers.js',
    permissions: ['drivers']
});

router.register('users', {
    title: 'المستخدمين',
    component: './pages/users/users.js',
    permissions: ['users']
});

router.register('reports', {
    title: 'التقارير',
    component: './pages/reports/reports.js',
    permissions: ['reports']
});

router.register('login', {
    title: 'تسجيل الدخول',
    requiresAuth: false
});

// دوال مساعدة للاستخدام السريع
export function navigateTo(path) {
    return router.navigate(path);
}

export function getCurrentRoute() {
    return router.getCurrentRoute();
}

export function goBack() {
    router.goBack();
}

export function goForward() {
    router.goForward();
}

export function reloadCurrentPage() {
    router.reload();
}

export default router;

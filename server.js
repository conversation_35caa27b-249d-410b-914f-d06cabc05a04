import express from 'express';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const app = express();

// خدمة الملفات الثابتة من المجلد الجذر
app.use(express.static(__dirname));

// خدمة مجلد frontend
app.use('/frontend', express.static(join(__dirname, 'frontend')));

// توجيه الصفحة الرئيسية
app.get('/', (req, res) => {
  res.sendFile(join(__dirname, 'index.html'));
});

// توجيه مسارات frontend
app.get('/frontend/*', (req, res) => {
  const filePath = req.path.replace('/frontend', '');
  const fullPath = join(__dirname, 'frontend', filePath || 'index.html');
  res.sendFile(fullPath, (err) => {
    if (err) {
      res.sendFile(join(__dirname, 'frontend', 'index.html'));
    }
  });
});

// توجيه باقي الطلبات إلى frontend
app.get('*', (req, res) => {
  res.sendFile(join(__dirname, 'frontend', 'index.html'));
});

// تشغيل الخادم على المنفذ 3000
const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`✅ Server is running on port ${PORT}`);
  console.log(`🌐 Open http://localhost:${PORT} in your browser`);
  console.log(`🚀 Fleet Management System is ready!`);
  console.log(`📁 Frontend: http://localhost:${PORT}/frontend/`);
  console.log(`🧪 Test: http://localhost:${PORT}/frontend/test.html`);
});
// Import required modules/functions
import { vehicles } from './script.js';
import { getBranchName } from './utility.js';

// Constants for service types and thresholds
const SERVICE_TYPES = {
    maintenance: 'Maintenance',
    tires: 'Tire Change',
    license: 'License Renewal'
};

const THRESHOLDS = {
    maintenance: {
        critical: 1000,
        warning: 5000
    },
    tires: {
        critical: 1000,
        warning: 5000
    },
    license: {
        critical: 7,
        warning: 30
    }
};

// Helper function to get service type label
export function getServiceTypeLabel(type) {
    const labels = {
        maintenance: 'Maintenance',
        tires: 'Tire Change',
        license: 'License Renewal'
    };
    return labels[type] || type;
}

// Helper function to format service remaining time/distance
export function formatServiceRemaining(service) {
    const remaining = service.remaining;
    if (remaining === undefined || remaining === null) {
        return 'N/A';
    }
    
    // Handle negative values (overdue)
    if (remaining < 0) {
        if (service.type === 'license') {
            return `<span class="overdue-value">Overdue by ${Math.abs(remaining)} Days</span>`;
        } else {
            return `<span class="overdue-value">Overdue by ${Math.abs(remaining).toLocaleString()} Km</span>`;
        }
    }
    
    return service.type === 'license'
        ? `${remaining} Days`
        : `${remaining.toLocaleString()} Km`;
}

// Get upcoming services
export function getUpcomingServices(selectedBranch = 'all') {
    // This will collect all upcoming maintenance, tire changes, and license renewals
    const services = [];
    
    // Get filtered vehicles based on branch
    const filteredVehicles = selectedBranch === 'all' 
        ? vehicles 
        : vehicles.filter(v => getBranchName(v).toLowerCase() === selectedBranch.toLowerCase());
    
    // Process each vehicle for its upcoming services
    filteredVehicles.forEach(vehicle => {
        // Add maintenance service
        const kmToMaintenance = parseInt(String(vehicle['Km to next maintenance'] || '0').replace(/,/g, '')) || 0;
        if (!isNaN(kmToMaintenance)) {
            const statusInfo = getStatusInfo(kmToMaintenance, 'maintenance');
            services.push({
                vehicle: {
                    id: vehicle['Vehicle ID'] || vehicle.id,
                    plate: vehicle['License Plate'],
                    branch: getBranchName(vehicle)
                },
                type: 'maintenance',
                remaining: kmToMaintenance,
                statusInfo: statusInfo,
                expectedDate: calculateExpectedDate(vehicle, kmToMaintenance, 'maintenance')
            });
        }
        
        // Add tire change service
        const kmToTires = parseInt(String(vehicle['Km left for tire change'] || '0').replace(/,/g, '')) || 0;
        if (!isNaN(kmToTires)) {
            const statusInfo = getStatusInfo(kmToTires, 'tires');
            services.push({
                vehicle: {
                    id: vehicle['Vehicle ID'] || vehicle.id,
                    plate: vehicle['License Plate'],
                    branch: getBranchName(vehicle)
                },
                type: 'tires',
                remaining: kmToTires,
                statusInfo: statusInfo,
                expectedDate: calculateExpectedDate(vehicle, kmToTires, 'tires')
            });
        }
        
        // Add license renewal service
        const daysToLicense = parseInt(vehicle['Days to renew license'] || '0');
        if (!isNaN(daysToLicense)) {
            const statusInfo = getStatusInfo(daysToLicense, 'license');
            services.push({
                vehicle: {
                    id: vehicle['Vehicle ID'] || vehicle.id,
                    plate: vehicle['License Plate'],
                    branch: getBranchName(vehicle)
                },
                type: 'license',
                remaining: daysToLicense,
                statusInfo: statusInfo,
                expectedDate: vehicle['License Renewal Date'] || calculateExpectedDate(vehicle, daysToLicense, 'license')
            });
        }
    });
    
    // Sort services: critical first, then warning, then good
    return services.sort((a, b) => {
        // First sort by status category
        const categoryOrder = { required: 0, upcoming: 1, good: 2 };
        if (categoryOrder[a.statusInfo.category] !== categoryOrder[b.statusInfo.category]) {
            return categoryOrder[a.statusInfo.category] - categoryOrder[b.statusInfo.category];
        }
        
        // Then by remaining time/distance
        return a.remaining - b.remaining;
    });
}

// Filter services based on type
export function filterServices(services, filter) {
    // First filter out 'good' status services
    const nonGoodServices = services.filter(service => service.statusInfo.category !== 'good');
    
    if (filter === 'all') {
        return nonGoodServices;
    } else if (filter === 'required') {
        return nonGoodServices.filter(service => service.statusInfo.category === 'required');
    } else if (filter === 'upcoming') {
        return nonGoodServices.filter(service => service.statusInfo.category === 'upcoming');
    } else if (filter === 'maintenance') {
        return nonGoodServices.filter(service => service.type === 'maintenance');
    } else if (filter === 'tires') {
        return nonGoodServices.filter(service => service.type === 'tires');
    } else if (filter === 'license') {
        return nonGoodServices.filter(service => service.type === 'license');
    }
    return nonGoodServices;
}

// Get status information based on remaining km/days and service type
export function getStatusInfo(kmLeft, type) {
    const statusInfo = {};
    
    if (type === 'maintenance') {
        if (kmLeft <= 0) {
            statusInfo.category = 'required';
            statusInfo.class = 'critical';
            statusInfo.icon = 'exclamation-triangle';
            statusInfo.text = 'Immediate Maintenance Required';
            statusInfo.tooltip = 'Vehicle is overdue for maintenance';
            statusInfo.color = '#ef4444';
        } else if (kmLeft < 1000) {
            statusInfo.category = 'required';
            statusInfo.class = 'critical';
            statusInfo.icon = 'exclamation-circle';
            statusInfo.text = 'Maintenance Required';
            statusInfo.tooltip = 'Vehicle requires maintenance soon';
            statusInfo.color = '#ef4444';
        } else if (kmLeft < 5000) {
            statusInfo.category = 'upcoming';
            statusInfo.class = 'warning';
            statusInfo.icon = 'exclamation';
            statusInfo.text = 'Upcoming Maintenance';
            statusInfo.tooltip = 'Maintenance is needed within 5000 km';
            statusInfo.color = '#f59e0b';
        } else {
            statusInfo.category = 'good';
            statusInfo.class = 'good';
            statusInfo.icon = 'check-circle';
            statusInfo.text = 'Maintenance Good';
            statusInfo.tooltip = 'Vehicle is well maintained';
            statusInfo.color = '#22c55e';
        }
    } else if (type === 'tires') {
        if (kmLeft <= 0) {
            statusInfo.category = 'required';
            statusInfo.class = 'critical';
            statusInfo.icon = 'exclamation-triangle';
            statusInfo.text = 'Immediate Tire Change Required';
            statusInfo.tooltip = 'Vehicle is overdue for tire change';
            statusInfo.color = '#ef4444';
        } else if (kmLeft < 1000) {
            statusInfo.category = 'required';
            statusInfo.class = 'critical';
            statusInfo.icon = 'exclamation-circle';
            statusInfo.text = 'Tire Change Required';
            statusInfo.tooltip = 'Vehicle requires tire change soon';
            statusInfo.color = '#ef4444';
        } else if (kmLeft < 5000) {
            statusInfo.category = 'upcoming';
            statusInfo.class = 'warning';
            statusInfo.icon = 'exclamation';
            statusInfo.text = 'Upcoming Tire Change';
            statusInfo.tooltip = 'Tire change is needed within 5000 km';
            statusInfo.color = '#f59e0b';
        } else {
            statusInfo.category = 'good';
            statusInfo.class = 'good';
            statusInfo.icon = 'check-circle';
            statusInfo.text = 'Tires Good';
            statusInfo.tooltip = 'Vehicle tires are in good condition';
            statusInfo.color = '#22c55e';
        }
    } else if (type === 'license') {
        if (kmLeft <= 0) {
            statusInfo.category = 'required';
            statusInfo.class = 'critical';
            statusInfo.icon = 'exclamation-triangle';
            statusInfo.text = 'License Renewal Overdue';
            statusInfo.tooltip = 'Vehicle license is expired';
            statusInfo.color = '#ef4444';
        } else if (kmLeft < 7) {
            statusInfo.category = 'required';
            statusInfo.class = 'critical';
            statusInfo.icon = 'exclamation-circle';
            statusInfo.text = 'License Renewal Required';
            statusInfo.tooltip = 'Vehicle license expires within 7 days';
            statusInfo.color = '#ef4444';
        } else if (kmLeft < 30) {
            statusInfo.category = 'upcoming';
            statusInfo.class = 'warning';
            statusInfo.icon = 'exclamation';
            statusInfo.text = 'Upcoming License Renewal';
            statusInfo.tooltip = 'Vehicle license expires within 30 days';
            statusInfo.color = '#f59e0b';
        } else {
            statusInfo.category = 'good';
            statusInfo.class = 'good';
            statusInfo.icon = 'check-circle';
            statusInfo.text = 'License Valid';
            statusInfo.tooltip = 'Vehicle license is valid';
            statusInfo.color = '#22c55e';
        }
    }
    
    return statusInfo;
}

// Helper function to calculate expected date
export function calculateExpectedDate(vehicle, remaining, type) {
    // Check for predefined date
    if (type === 'license' && vehicle['License Renewal Date']) {
        return vehicle['License Renewal Date'];
    }
    
    // Calculate expected date based on remaining distance and daily usage
    const currentDate = new Date();
    const dailyUsage = 50; // Default daily usage in km
    
    if (type === 'license') {
        // For license, use days directly
        const expectedDate = new Date(currentDate);
        expectedDate.setDate(currentDate.getDate() + remaining);
        return expectedDate.toLocaleDateString();
    } else {
        // For maintenance and tires, calculate days based on daily usage
        const daysRemaining = Math.max(0, remaining) / dailyUsage;
        const expectedDate = new Date(currentDate);
        expectedDate.setDate(currentDate.getDate() + Math.round(daysRemaining));
        return expectedDate.toLocaleDateString();
    }
}

{"name": "fleet-management-system", "version": "1.0.0", "description": "نظام شامل لإدارة الأسطول وتتبع المركبات والصيانة والعمليات باللغة العربية", "main": "main.js", "type": "module", "scripts": {"start": "npx serve . -p 8000", "dev": "npx serve . -p 3000", "build": "echo 'No build process needed for vanilla JS'", "test": "echo 'No tests specified'", "lint": "echo 'No linting configured'", "serve": "npx serve .", "preview": "npx serve . -p 4173"}, "keywords": ["fleet-management", "vehicle-tracking", "maintenance", "arabic", "rtl", "dashboard", "javascript", "vanilla-js", "إدارة-الأسطول", "تتبع-المركبات", "صيانة"], "author": {"name": "Fleet Management Team", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-username/fleet-management-system.git"}, "bugs": {"url": "https://github.com/your-username/fleet-management-system/issues"}, "homepage": "https://github.com/your-username/fleet-management-system#readme", "engines": {"node": ">=14.0.0", "npm": ">=6.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"], "devDependencies": {"serve": "^14.2.1"}, "dependencies": {}, "peerDependencies": {}, "optionalDependencies": {}, "config": {"port": 8000, "host": "localhost"}, "directories": {"lib": "./", "doc": "./docs"}, "files": ["*.html", "*.js", "css/", "components/", "pages/", "services/", "store/", "utils/", "assets/", "data/", "README.md", "LICENSE"], "funding": {"type": "individual", "url": "https://github.com/sponsors/your-username"}, "private": false, "workspaces": [], "os": ["darwin", "linux", "win32"], "cpu": ["x64", "arm64"]}
/**
 * Store Selectors - محددات المخزن
 * يحتوي على دوال لاستخراج أجزاء محددة من حالة التطبيق
 */

/**
 * محددات المستخدم والمصادقة
 */
export const userSelectors = {
    /**
     * الحصول على المستخدم الحالي
     */
    getCurrentUser: (state) => state.user?.currentUser,

    /**
     * التحقق من حالة المصادقة
     */
    isAuthenticated: (state) => state.user?.isAuthenticated || false,

    /**
     * الحصول على صلاحيات المستخدم
     */
    getUserPermissions: (state) => state.user?.permissions || [],

    /**
     * الحصول على تفضيلات المستخدم
     */
    getUserPreferences: (state) => state.user?.preferences || {},

    /**
     * الحصول على دور المستخدم
     */
    getUserRole: (state) => state.user?.currentUser?.role,

    /**
     * الحصول على فرع المستخدم
     */
    getUserBranch: (state) => state.user?.currentUser?.branch,

    /**
     * التحقق من صلاحية محددة
     */
    hasPermission: (permission) => (state) => {
        const permissions = state.user?.permissions || [];
        return permissions.includes(permission);
    },

    /**
     * التحقق من دور محدد
     */
    hasRole: (role) => (state) => {
        return state.user?.currentUser?.role === role;
    }
};

/**
 * محددات التطبيق العامة
 */
export const appSelectors = {
    /**
     * الحصول على الصفحة الحالية
     */
    getCurrentPage: (state) => state.app?.currentPage || 'dashboard',

    /**
     * التحقق من حالة التحميل
     */
    isLoading: (state) => state.app?.isLoading || false,

    /**
     * الحصول على رسالة التحميل
     */
    getLoadingMessage: (state) => state.app?.loadingMessage || '',

    /**
     * الحصول على الإشعارات
     */
    getNotifications: (state) => state.app?.notifications || [],

    /**
     * الحصول على الأخطاء
     */
    getErrors: (state) => state.app?.errors || [],

    /**
     * التحقق من حالة الشريط الجانبي
     */
    isSidebarOpen: (state) => state.app?.sidebarOpen !== false,

    /**
     * الحصول على آخر إشعار
     */
    getLatestNotification: (state) => {
        const notifications = state.app?.notifications || [];
        return notifications[notifications.length - 1];
    },

    /**
     * عدد الإشعارات غير المقروءة
     */
    getUnreadNotificationsCount: (state) => {
        const notifications = state.app?.notifications || [];
        return notifications.filter(n => !n.read).length;
    }
};

/**
 * محددات البيانات
 */
export const dataSelectors = {
    /**
     * الحصول على جميع المركبات
     */
    getAllVehicles: (state) => state.data?.vehicles || [],

    /**
     * الحصول على المركبات النشطة
     */
    getActiveVehicles: (state) => {
        const vehicles = state.data?.vehicles || [];
        return vehicles.filter(vehicle => 
            vehicle['Vehicle Status']?.toLowerCase() === 'active' ||
            vehicle.status?.toLowerCase() === 'active'
        );
    },

    /**
     * الحصول على المركبات في الصيانة
     */
    getVehiclesInMaintenance: (state) => {
        const vehicles = state.data?.vehicles || [];
        return vehicles.filter(vehicle => 
            vehicle['Vehicle Status']?.toLowerCase() === 'maintenance' ||
            vehicle.status?.toLowerCase() === 'maintenance'
        );
    },

    /**
     * الحصول على مركبة بالمعرف
     */
    getVehicleById: (id) => (state) => {
        const vehicles = state.data?.vehicles || [];
        return vehicles.find(vehicle => 
            vehicle.id === id || 
            vehicle['Vehicle ID'] === id
        );
    },

    /**
     * الحصول على المركبات حسب الفرع
     */
    getVehiclesByBranch: (branch) => (state) => {
        const vehicles = state.data?.vehicles || [];
        return vehicles.filter(vehicle => 
            vehicle.Branch === branch || 
            vehicle.branch === branch
        );
    },

    /**
     * الحصول على جميع السائقين
     */
    getAllDrivers: (state) => state.data?.drivers || [],

    /**
     * الحصول على السائقين النشطين
     */
    getActiveDrivers: (state) => {
        const drivers = state.data?.drivers || [];
        return drivers.filter(driver => 
            driver.status?.toLowerCase() === 'active'
        );
    },

    /**
     * الحصول على سائق بالمعرف
     */
    getDriverById: (id) => (state) => {
        const drivers = state.data?.drivers || [];
        return drivers.find(driver => driver.id === id);
    },

    /**
     * الحصول على سجلات الصيانة
     */
    getMaintenanceRecords: (state) => state.data?.maintenance || [],

    /**
     * الحصول على سجلات الصيانة لمركبة محددة
     */
    getMaintenanceByVehicle: (vehicleId) => (state) => {
        const maintenance = state.data?.maintenance || [];
        return maintenance.filter(record => record.vehicleId === vehicleId);
    },

    /**
     * الحصول على سجلات الوقود
     */
    getFuelRecords: (state) => state.data?.fuel || [],

    /**
     * الحصول على سجلات الوقود لمركبة محددة
     */
    getFuelByVehicle: (vehicleId) => (state) => {
        const fuel = state.data?.fuel || [];
        return fuel.filter(record => record.vehicleId === vehicleId);
    },

    /**
     * الحصول على جميع المستخدمين
     */
    getAllUsers: (state) => state.data?.users || [],

    /**
     * الحصول على آخر وقت مزامنة
     */
    getLastSync: (state) => state.data?.lastSync,

    /**
     * التحقق من حالة الاتصال
     */
    isOnline: (state) => state.data?.isOnline !== false
};

/**
 * محددات واجهة المستخدم
 */
export const uiSelectors = {
    /**
     * الحصول على حالة النافذة المنبثقة
     */
    getModalState: (modalId) => (state) => {
        return state.ui?.modals?.[modalId] || { isOpen: false, data: null };
    },

    /**
     * التحقق من فتح النافذة المنبثقة
     */
    isModalOpen: (modalId) => (state) => {
        return state.ui?.modals?.[modalId]?.isOpen || false;
    },

    /**
     * الحصول على حالة النموذج
     */
    getFormState: (formId) => (state) => {
        return state.ui?.forms?.[formId] || {};
    },

    /**
     * الحصول على حالة الجدول
     */
    getTableState: (tableId) => (state) => {
        return state.ui?.tables?.[tableId] || {};
    },

    /**
     * الحصول على الفلاتر
     */
    getFilters: (section) => (state) => {
        return state.filters?.[section] || {};
    },

    /**
     * التحقق من وجود فلاتر نشطة
     */
    hasActiveFilters: (section) => (state) => {
        const filters = state.filters?.[section] || {};
        return Object.keys(filters).length > 0;
    }
};

/**
 * محددات الإحصائيات المحسوبة
 */
export const statsSelectors = {
    /**
     * إحصائيات المركبات
     */
    getVehicleStats: (state) => {
        const vehicles = state.data?.vehicles || [];
        
        return {
            total: vehicles.length,
            active: vehicles.filter(v => 
                v['Vehicle Status']?.toLowerCase() === 'active' ||
                v.status?.toLowerCase() === 'active'
            ).length,
            maintenance: vehicles.filter(v => 
                v['Vehicle Status']?.toLowerCase() === 'maintenance' ||
                v.status?.toLowerCase() === 'maintenance'
            ).length,
            inactive: vehicles.filter(v => 
                v['Vehicle Status']?.toLowerCase() === 'inactive' ||
                v.status?.toLowerCase() === 'inactive'
            ).length
        };
    },

    /**
     * إحصائيات السائقين
     */
    getDriverStats: (state) => {
        const drivers = state.data?.drivers || [];
        
        return {
            total: drivers.length,
            active: drivers.filter(d => d.status?.toLowerCase() === 'active').length,
            inactive: drivers.filter(d => d.status?.toLowerCase() === 'inactive').length
        };
    },

    /**
     * إحصائيات الصيانة
     */
    getMaintenanceStats: (state) => {
        const maintenance = state.data?.maintenance || [];
        const now = new Date();
        const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);
        
        return {
            total: maintenance.length,
            thisMonth: maintenance.filter(m => 
                new Date(m.serviceDate) >= thisMonth
            ).length,
            pending: maintenance.filter(m => 
                m.status?.toLowerCase() === 'pending'
            ).length,
            completed: maintenance.filter(m => 
                m.status?.toLowerCase() === 'completed'
            ).length
        };
    },

    /**
     * إحصائيات الوقود
     */
    getFuelStats: (state) => {
        const fuel = state.data?.fuel || [];
        const now = new Date();
        const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);
        
        const thisMonthRecords = fuel.filter(f => 
            new Date(f.fuelDate) >= thisMonth
        );
        
        return {
            total: fuel.length,
            thisMonth: thisMonthRecords.length,
            totalCost: fuel.reduce((sum, f) => sum + (f.cost || 0), 0),
            totalLiters: fuel.reduce((sum, f) => sum + (f.liters || 0), 0),
            monthlyLiters: thisMonthRecords.reduce((sum, f) => sum + (f.liters || 0), 0),
            monthlyCost: thisMonthRecords.reduce((sum, f) => sum + (f.cost || 0), 0)
        };
    }
};

/**
 * محددات البحث والفلترة
 */
export const searchSelectors = {
    /**
     * البحث في المركبات
     */
    searchVehicles: (searchTerm) => (state) => {
        const vehicles = state.data?.vehicles || [];
        if (!searchTerm) return vehicles;
        
        const term = searchTerm.toLowerCase();
        return vehicles.filter(vehicle => 
            vehicle['License Plate']?.toLowerCase().includes(term) ||
            vehicle['Vehicle Type']?.toLowerCase().includes(term) ||
            vehicle['Driver Name']?.toLowerCase().includes(term) ||
            vehicle.Branch?.toLowerCase().includes(term)
        );
    },

    /**
     * البحث في السائقين
     */
    searchDrivers: (searchTerm) => (state) => {
        const drivers = state.data?.drivers || [];
        if (!searchTerm) return drivers;
        
        const term = searchTerm.toLowerCase();
        return drivers.filter(driver => 
            driver.name?.toLowerCase().includes(term) ||
            driver.employeeId?.toLowerCase().includes(term) ||
            driver.phone?.includes(term) ||
            driver.email?.toLowerCase().includes(term)
        );
    },

    /**
     * فلترة المركبات حسب الفرع والحالة
     */
    filterVehicles: (filters) => (state) => {
        let vehicles = state.data?.vehicles || [];
        
        if (filters.branch) {
            vehicles = vehicles.filter(v => 
                v.Branch === filters.branch || v.branch === filters.branch
            );
        }
        
        if (filters.status) {
            vehicles = vehicles.filter(v => 
                v['Vehicle Status'] === filters.status || v.status === filters.status
            );
        }
        
        if (filters.type) {
            vehicles = vehicles.filter(v => 
                v['Vehicle Type'] === filters.type || v.type === filters.type
            );
        }
        
        return vehicles;
    }
};

/**
 * تجميع جميع المحددات
 */
export const selectors = {
    user: userSelectors,
    app: appSelectors,
    data: dataSelectors,
    ui: uiSelectors,
    stats: statsSelectors,
    search: searchSelectors
};

// تصدير افتراضي
export default selectors;
